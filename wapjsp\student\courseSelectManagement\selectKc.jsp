<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>选课</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 选课页面样式 */
        .course-select-header {
            background: linear-gradient(135deg, var(--success-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        
        .course-select-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .course-select-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .semester-info {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--info-dark);
        }
        
        .semester-info i {
            color: var(--info-color);
            margin-right: 8px;
        }
        
        .semester-text {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .actions-title i {
            color: var(--success-color);
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }
        
        .btn-action {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--divider-color);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-action:hover {
            background: var(--info-light);
            color: var(--info-dark);
            border-color: var(--info-color);
        }
        
        .course-types-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .types-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .types-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .types-title i {
            color: var(--success-color);
        }
        
        .course-type-tabs {
            display: flex;
            flex-wrap: wrap;
            padding: var(--padding-sm);
            gap: var(--spacing-xs);
        }
        
        .course-type-tab {
            flex: 1;
            min-width: 120px;
            padding: var(--padding-sm);
            text-align: center;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-secondary);
            border: 1px solid var(--divider-color);
        }
        
        .course-type-tab:hover {
            background: var(--success-light);
            color: var(--success-dark);
            border-color: var(--success-color);
        }
        
        .course-type-tab.active {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }
        
        .course-content {
            padding: var(--padding-md);
            min-height: 200px;
        }
        
        .course-iframe {
            width: 100%;
            height: 400px;
            border: none;
            border-radius: 6px;
            background: var(--bg-tertiary);
        }
        
        .loading-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: var(--text-secondary);
        }
        
        .loading-placeholder i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .notice-section {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .notice-title i {
            color: var(--warning-color);
        }
        
        .notice-list {
            font-size: var(--font-size-small);
            line-height: 1.6;
        }
        
        .notice-list li {
            margin-bottom: 4px;
        }
        
        @media (max-width: 480px) {
            .course-select-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .semester-info,
            .quick-actions,
            .course-types-section,
            .notice-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
            
            .course-type-tabs {
                flex-direction: column;
            }
            
            .course-type-tab {
                min-width: auto;
            }
            
            .course-iframe {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" value="${token_in_session}"/>
    <input type="hidden" id="checkCt" value="${checkCt}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">选课</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 选课头部 -->
        <div class="course-select-header">
            <div class="course-select-title">☑️选课</div>
            <div class="course-select-desc">选择您的课程</div>
        </div>
        
        <!-- 学期信息 -->
        <div class="semester-info">
            <i class="ace-icon fa fa-calendar"></i>
            <div class="semester-text">
                <strong>${zxjxjh.yearName}${zxjxjh.semesterName}</strong><br>
                <span style="color: blue;">${fajh.famc}</span>
                <span style="color: red;">${fajh.xdlxmc}</span>
            </div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="actions-title">
                <i class="ace-icon fa fa-bolt"></i>
                快捷操作
            </div>
            <div class="action-buttons">
                <button class="btn-action" onclick="goToNotice();">
                    <i class="ace-icon fa fa-bullhorn"></i>
                    <span>选课公告</span>
                </button>
                <button class="btn-action" onclick="goToQuitCourse();">
                    <i class="ace-icon fa fa-times-circle"></i>
                    <span>退课</span>
                </button>
            </div>
        </div>
        
        <!-- 选课类型 -->
        <div class="course-types-section">
            <div class="types-header">
                <div class="types-title">
                    <i class="ace-icon fa fa-list"></i>
                    选课类型
                </div>
            </div>
            
            <div class="course-type-tabs">
                <div class="course-type-tab active" id="faxk" onclick="selectCourseType(this, '/student/courseSelect/planCourse/index?fajhh=${fajhh}');">
                    方案选课
                </div>
                
                <c:if test="${xsafatjxk == '1'}">
                    <div class="course-type-tab" id="fatjxk" onclick="selectCourseType(this, '/student/courseSelect/planRecommendCourse/index?fajhh=${fajhh}');">
                        按方案推荐选课
                    </div>
                </c:if>
                
                <c:if test="${xxbm != '100008'}">
                    <div class="course-type-tab" id="xirxk" onclick="selectCourseType(this, '/student/courseSelect/departCourse/index?fajhh=${fajhh}');">
                        系任选课
                    </div>
                </c:if>
                
                <div class="course-type-tab" id="xarxk" onclick="selectCourseType(this, '/student/courseSelect/schoolCourse/index?fajhh=${fajhh}');">
                    校任选课
                </div>
                
                <div class="course-type-tab" id="zyxk" onclick="selectCourseType(this, '/student/courseSelect/specialCourse/index?fajhh=${fajhh}');">
                    专业选课
                </div>
                
                <c:if test="${xxbm == '100008'}">
                    <div class="course-type-tab" id="xirxk" onclick="selectCourseType(this, '/student/courseSelect/departCourse/index?fajhh=${fajhh}');">
                        系任选课
                    </div>
                </c:if>
                
                <c:if test="${xxbm == '100010'}">
                    <div class="course-type-tab" id="cxxk" onclick="selectCourseType(this, '/student/courseSelect/retakeCourse/index?fajhh=${fajhh}');">
                        重修选课
                    </div>
                </c:if>
                
                <c:if test="${xxbm != '100010'}">
                    <div class="course-type-tab" id="cxxk" onclick="selectCourseType(this, '/student/courseSelect/retakeCourse/index?fajhh=${fajhh}');">
                        重修选课
                    </div>
                </c:if>
                
                <c:if test="${xxbm == '100010'}">
                    <div class="course-type-tab" id="jhxk" onclick="selectCourseType(this, '/student/courseSelect/planCourse/index?fajhh=${fajhh}');">
                        计划选课
                    </div>
                </c:if>
                
                <c:if test="${xxbm == '100010'}">
                    <div class="course-type-tab" id="fxxk" onclick="selectCourseType(this, '/student/courseSelect/supplementCourse/index?fajhh=${fajhh}');">
                        辅修选课
                    </div>
                </c:if>
            </div>
            
            <div class="course-content">
                <iframe id="iframe-xk" class="course-iframe" src="/student/courseSelect/planCourse/index?fajhh=${fajhh}"></iframe>
            </div>
        </div>
        
        <!-- 选课注意事项 -->
        <div class="notice-section">
            <div class="notice-title">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                选课注意事项
            </div>
            <ul class="notice-list">
                <li>请在规定时间内完成选课，逾期系统将自动关闭</li>
                <li>选课前请仔细查看课程信息，包括上课时间、地点等</li>
                <li>注意课程的先修要求和选课限制条件</li>
                <li>如有疑问请及时联系教务处或开课院系</li>
                <li>选课成功后请及时查看课表确认</li>
            </ul>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentCourseType = 'faxk';

        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            adjustIframeHeight();

            // 监听iframe加载完成
            $('#iframe-xk').on('load', function() {
                adjustIframeHeight();
            });

            // 监听窗口大小变化
            $(window).on('resize', function() {
                adjustIframeHeight();
            });
        }

        // 调整iframe高度
        function adjustIframeHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight() || 0;
            const headerHeight = $('.course-select-header').outerHeight() || 0;
            const semesterHeight = $('.semester-info').outerHeight() || 0;
            const actionsHeight = $('.quick-actions').outerHeight() || 0;
            const typesHeaderHeight = $('.types-header').outerHeight() || 0;
            const tabsHeight = $('.course-type-tabs').outerHeight() || 0;
            const noticeHeight = $('.notice-section').outerHeight() || 0;

            const availableHeight = windowHeight - navbarHeight - headerHeight -
                                  semesterHeight - actionsHeight - typesHeaderHeight -
                                  tabsHeight - noticeHeight - 100; // 100px for margins and padding

            const minHeight = 300;
            const finalHeight = Math.max(availableHeight, minHeight);

            $('#iframe-xk').height(finalHeight);
        }

        // 选择课程类型
        function selectCourseType(element, url) {
            // 更新选中状态
            $('.course-type-tab').removeClass('active');
            $(element).addClass('active');

            // 更新当前类型
            currentCourseType = $(element).attr('id');

            // 显示加载状态
            showLoading(true);

            // 更新iframe源
            $('#iframe-xk').attr('src', url);

            // iframe加载完成后隐藏加载状态
            $('#iframe-xk').on('load', function() {
                showLoading(false);
                adjustIframeHeight();
            });
        }

        // 跳转到选课公告
        function goToNotice() {
            window.location.href = '/student/courseSelect/courseSelectNotice/index';
        }

        // 跳转到退课
        function goToQuitCourse() {
            window.location.href = '/student/courseSelect/quitCourse/index';
        }

        // 返回上一页
        function returnIndex() {
            history.back();
        }

        // 刷新数据
        function refreshData() {
            // 刷新当前iframe
            const currentSrc = $('#iframe-xk').attr('src');
            if (currentSrc) {
                showLoading(true);
                $('#iframe-xk').attr('src', currentSrc);
            } else {
                window.location.reload();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 处理iframe通信
        window.addEventListener('message', function(event) {
            // 处理来自iframe的消息
            if (event.data && event.data.type) {
                switch (event.data.type) {
                    case 'courseSelected':
                        // 课程选择成功
                        showAlert('课程选择成功！');
                        break;
                    case 'courseDropped':
                        // 课程退选成功
                        showAlert('课程退选成功！');
                        break;
                    case 'error':
                        // 错误信息
                        showAlert(event.data.message || '操作失败');
                        break;
                    case 'resize':
                        // 调整iframe高度
                        if (event.data.height) {
                            $('#iframe-xk').height(event.data.height);
                        }
                        break;
                }
            }
        });

        // 显示提示信息
        function showAlert(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 页面可见性变化时刷新iframe
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                // 页面变为可见时，刷新iframe以获取最新数据
                setTimeout(function() {
                    const currentSrc = $('#iframe-xk').attr('src');
                    if (currentSrc) {
                        $('#iframe-xk').attr('src', currentSrc);
                    }
                }, 500);
            }
        });
    </script>
</body>
</html>
