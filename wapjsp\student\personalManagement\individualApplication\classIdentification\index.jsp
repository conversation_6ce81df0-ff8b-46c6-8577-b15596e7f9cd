<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>课组认定申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 课组认定申请页面样式 */
        .identification-header {
            background: linear-gradient(135deg, var(--primary-color), var(--warning-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .identification-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .identification-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .action-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-add-application {
            width: 100%;
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-add-application:hover {
            background: var(--success-dark);
        }
        
        .btn-add-application:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .applications-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .applications-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .applications-section-title i {
            color: var(--warning-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-index {
            background: var(--warning-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .application-content {
            flex: 1;
        }
        
        .application-number {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .application-category {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            align-items: flex-start;
        }
        
        .detail-label {
            font-weight: 500;
            min-width: 60px;
            margin-right: var(--margin-sm);
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
            word-break: break-word;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-draft {
            background: var(--text-disabled);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .application-reason {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-application-action {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-revoke {
            background: var(--error-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .notice-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            overflow-y: auto;
        }
        
        .notice-modal-content {
            background: var(--bg-primary);
            margin: 20px;
            border-radius: 8px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        
        .notice-modal-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1001;
        }
        
        .notice-modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .btn-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
        }
        
        .notice-modal-body {
            padding: var(--padding-md);
            line-height: 1.6;
            color: var(--text-primary);
        }
        
        .notice-modal-footer {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn-continue {
            background: var(--info-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-md);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-continue:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-md);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
        }
        
        @media (max-width: 480px) {
            .identification-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .action-section,
            .applications-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .application-details {
                grid-template-columns: 1fr;
            }
            
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .detail-value {
                text-align: left;
                margin-top: 4px;
            }
            
            .notice-modal-content {
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">课组认定申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 课组认定申请头部 -->
        <div class="identification-header">
            <div class="identification-title">课组认定申请</div>
            <div class="identification-desc">管理您的课组认定申请记录</div>
        </div>
        
        <!-- 添加申请按钮 -->
        <c:if test="${flag == 'showAdd'}">
            <div class="action-section">
                <button class="btn-add-application" onclick="addApplication();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>新增申请</span>
                </button>
            </div>
        </c:if>
        
        <!-- 申请列表 -->
        <div class="applications-section">
            <div class="applications-section-header">
                <div class="applications-section-title">
                    <i class="ace-icon fa fa-list"></i>
                    我的申请记录
                </div>
            </div>
            
            <div id="applicationsList">
                <!-- 动态加载申请列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreApplications();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div class="empty-state-title">暂无申请记录</div>
            <div class="empty-state-desc">您还没有提交任何课组认定申请</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>
    
    <!-- 申请须知模态框 -->
    <div class="notice-modal" id="noticeModal">
        <div class="notice-modal-content">
            <div class="notice-modal-header">
                <div class="notice-modal-title">申请须知</div>
                <button class="btn-close" onclick="closeNoticeModal();">
                    <i class="ace-icon fa fa-times"></i>
                </button>
            </div>
            <div class="notice-modal-body" id="noticeContent">
                <!-- 动态加载须知内容 -->
            </div>
            <div class="notice-modal-footer">
                <button class="btn-continue" id="btnContinue" onclick="continueApplication();" disabled>
                    <i class="ace-icon fa fa-arrow-right"></i>
                    <span>继续</span>
                    <span id="countdown"></span>
                </button>
                <button class="btn-cancel" onclick="closeNoticeModal();">
                    <i class="ace-icon fa fa-times"></i>
                    关闭
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let applicationData = [];
        let countdownTimer = null;

        $(function() {
            initPage();
            loadApplications(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载申请列表
        function loadApplications(page, conditionChanged) {
            if (conditionChanged) {
                currentPage = 1;
            }

            showLoading(true);

            const url = "/student/personalManagement/individualApplication/classIdentification/index/getPage";

            $.ajax({
                url: url,
                cache: false,
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records) {
                        applicationData = data.records;
                        totalCount = data.pageContext ? data.pageContext.totalCount : 0;

                        if (applicationData.length > 0) {
                            renderApplications();
                        } else {
                            showEmptyState();
                        }
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplications() {
            const container = $('#applicationsList');
            container.empty();

            if (applicationData.length === 0) {
                showEmptyState();
                return;
            }

            applicationData.forEach(function(application, index) {
                const applicationHtml = createApplicationItem(application, index);
                container.append(applicationHtml);
            });

            hideEmptyState();
        }

        // 创建申请项目HTML
        function createApplicationItem(application, index) {
            const statusInfo = getStatusInfo(application.SQZT);
            const canRevoke = application.SQZT == 0 || application.SQZT == 1 || (application.SQZT == 3 && "${flag}" == "showAdd");

            return `
                <div class="application-item">
                    <div class="application-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="application-index">${index + 1}</div>
                            <div class="application-content">
                                <div class="application-number">${application.SQBH || ''}</div>
                                <div class="application-category">${application.KZLBMC || ''}</div>
                            </div>
                        </div>
                        <div class="status-badge ${statusInfo.class}">${statusInfo.text}</div>
                    </div>

                    <div class="application-details">
                        <div class="detail-item">
                            <span class="detail-label">申请日期</span>
                            <span class="detail-value">${application.SQRQSTR || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">申请状态</span>
                            <span class="detail-value">${application.STATUSNAME || ''}</span>
                        </div>
                    </div>

                    ${application.SQYY ? `
                        <div class="application-reason">
                            <strong>申请原因：</strong>${application.SQYY}
                        </div>
                    ` : ''}

                    <div class="application-actions">
                        <button class="btn-application-action btn-view" onclick="viewApplication('${application.SQBH}', '${application.APPLY_TYPE}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${canRevoke ? `
                            <button class="btn-application-action btn-revoke" onclick="revokeApplication('${application.SQBH}');">
                                <i class="ace-icon fa fa-reply"></i>
                                <span>撤销</span>
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 获取状态信息
        function getStatusInfo(status) {
            switch(status) {
                case 0:
                    return { class: 'status-draft', text: '草稿' };
                case 1:
                    return { class: 'status-pending', text: '待审批' };
                case 2:
                    return { class: 'status-approved', text: '已通过' };
                case 3:
                    return { class: 'status-rejected', text: '未通过' };
                default:
                    return { class: 'status-draft', text: '未知' };
            }
        }

        // 添加申请
        function addApplication() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/classIdentification/checkApplyTimes",
                cache: false,
                type: "post",
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data.result === "ok") {
                        if (data.ywsqkzb.sfxyd === "1") {
                            showNoticeModal(data.ywsqkzb);
                        } else {
                            goToAddPage();
                        }
                    } else {
                        showError(data.result);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示申请须知模态框
        function showNoticeModal(noticeData) {
            $('#noticeContent').html(noticeData.ydnrstr);
            $('#noticeModal').show();

            const countdownSeconds = noticeData.qzydms;
            if (countdownSeconds > 0) {
                startCountdown(countdownSeconds);
            } else {
                enableContinueButton();
            }
        }

        // 开始倒计时
        function startCountdown(seconds) {
            let remainingSeconds = seconds;
            $('#btnContinue').prop('disabled', true);

            countdownTimer = setInterval(function() {
                $('#countdown').text(`（${remainingSeconds}s）`);
                remainingSeconds--;

                if (remainingSeconds < 0) {
                    clearInterval(countdownTimer);
                    enableContinueButton();
                }
            }, 1000);
        }

        // 启用继续按钮
        function enableContinueButton() {
            $('#btnContinue').prop('disabled', false);
            $('#countdown').text('');
        }

        // 继续申请
        function continueApplication() {
            closeNoticeModal();
            setTimeout(function() {
                goToAddPage();
            }, 500);
        }

        // 关闭须知模态框
        function closeNoticeModal() {
            $('#noticeModal').hide();
            if (countdownTimer) {
                clearInterval(countdownTimer);
                countdownTimer = null;
            }
        }

        // 跳转到添加页面
        function goToAddPage() {
            window.location.href = "/student/personalManagement/individualApplication/classIdentification/kcIndex";
        }

        // 查看申请
        function viewApplication(sqbh, applyType) {
            const url = "/student/application/index/seeInfo?applyId=" + sqbh + "&applyType=" + applyType;

            if (parent && parent.addTab) {
                parent.addTab('查看申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 撤销申请
        function revokeApplication(sqbh) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm("是否确认撤销当前申请？", function(confirmed) {
                    if (confirmed) {
                        showRevokeReasonDialog(sqbh);
                    }
                });
            } else {
                if (confirm("是否确认撤销当前申请？")) {
                    showRevokeReasonDialog(sqbh);
                }
            }
        }

        // 显示撤销原因对话框
        function showRevokeReasonDialog(sqbh) {
            const reason = prompt("请输入撤销原因（必填）：");
            if (reason && reason.trim()) {
                doRevokeApplication(sqbh, reason.trim());
            } else if (reason !== null) {
                showError("撤销原因不能为空！");
            }
        }

        // 执行撤销申请
        function doRevokeApplication(sqbh, reason) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/classIdentification/revokeInfo",
                type: "post",
                data: "sqbh=" + sqbh + "&cxyy=" + reason + "&tokenValue=" + $("#tokenValue").val(),
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data.result.indexOf("/") !== -1) {
                        window.location.href = data.result;
                    } else {
                        if (data.result === "ok") {
                            showSuccess("操作成功！");
                            loadApplications(1, true);
                        } else {
                            showError(data.result);
                        }
                    }
                    $("#tokenValue").val(data.token);
                },
                error: function() {
                    showError("操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示空状态
        function showEmptyState() {
            $('#emptyState').show();
            $('#loadMoreContainer').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 返回首页
        function returnIndex() {
            location.href = "/student/application/index";
        }

        // 刷新数据
        function refreshData() {
            loadApplications(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#noticeModal').click(function(e) {
            if (e.target === this) {
                closeNoticeModal();
            }
        });
    </script>
</body>
</html>
