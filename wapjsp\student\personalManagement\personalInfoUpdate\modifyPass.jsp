<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>修改密码</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 修改密码页面样式 */
        .password-header {
            background: linear-gradient(135deg, var(--warning-color), var(--error-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }
        
        .password-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .password-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .warning-notice {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .warning-notice i {
            color: var(--warning-color);
            margin-right: 8px;
        }
        
        .form-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-title i {
            color: var(--warning-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: 4px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .required {
            color: var(--error-color);
        }
        
        .form-input {
            width: 100%;
            padding: var(--padding-md);
            border: 1px solid var(--divider-color);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            box-sizing: border-box;
        }
        
        .form-input:focus {
            border-color: var(--warning-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
        }
        
        .form-hint {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 4px;
            line-height: 1.4;
        }
        
        .verification-group {
            display: flex;
            gap: var(--spacing-sm);
            align-items: flex-end;
        }
        
        .verification-input {
            flex: 1;
        }
        
        .btn-verification {
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-small);
            cursor: pointer;
            white-space: nowrap;
            min-width: 100px;
        }
        
        .btn-verification:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .password-requirements {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: 4px;
        }
        
        .requirements-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--info-dark);
            margin-bottom: 4px;
        }
        
        .requirements-list {
            font-size: var(--font-size-small);
            color: var(--info-dark);
            line-height: 1.4;
        }
        
        .action-buttons {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-primary {
            flex: 1;
            background: var(--warning-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .btn-secondary {
            flex: 1;
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: 1px solid var(--divider-color);
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .security-tips {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tips-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tips-title i {
            color: var(--info-color);
        }
        
        .tips-list {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.6;
        }
        
        .tips-list li {
            margin-bottom: 4px;
        }
        
        @media (max-width: 480px) {
            .password-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .form-section,
            .action-buttons,
            .security-tips,
            .warning-notice {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .verification-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .btn-verification {
                min-width: auto;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">修改密码</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 修改密码头部 -->
        <div class="password-header">
            <div class="password-title">修改密码</div>
            <div class="password-desc">保护您的账户安全</div>
        </div>
        
        <!-- 密码过期提醒 -->
        <c:if test="${cycle == 'cycle'}">
            <div class="warning-notice">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                您的密码使用时间过长，可能会有泄露风险，请及时修改！
            </div>
        </c:if>
        
        <!-- 修改密码表单 -->
        <div class="form-section">
            <div class="form-title">
                <i class="ace-icon fa fa-key"></i>
                密码信息
            </div>
            
            <form id="passwordForm">
                <div class="form-group">
                    <div class="form-label">
                        <span class="required">*</span>
                        旧密码
                    </div>
                    <input type="password" id="oldPass" class="form-input" 
                           placeholder="请输入当前密码" autocomplete="new-password" 
                           onchange="checkOldPass();">
                    <div class="form-hint">请输入您当前使用的密码</div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">
                        <span class="required">*</span>
                        新密码
                    </div>
                    <input type="password" id="newPass1" class="form-input" 
                           placeholder="请输入新密码" autocomplete="new-password">
                    <div class="password-requirements">
                        <div class="requirements-title">密码要求：</div>
                        <div class="requirements-list">
                            新密码必须包含大写、小写字母、数字、特殊字符，且长度至少为${passwordLength}位
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">
                        <span class="required">*</span>
                        确认新密码
                    </div>
                    <input type="password" id="newPass2" class="form-input" 
                           placeholder="请再次输入新密码" autocomplete="new-password">
                    <div class="form-hint">请再次输入新密码以确认</div>
                </div>
                
                <!-- 验证码（特定学校） -->
                <c:if test="${schoolCode == '100014'}">
                    <div class="form-group">
                        <div class="form-label">
                            <span class="required">*</span>
                            手机验证码
                        </div>
                        <div class="verification-group">
                            <div class="verification-input">
                                <input type="text" id="verifycode" class="form-input" 
                                       placeholder="请输入验证码">
                            </div>
                            <button type="button" id="butInfo" class="btn-verification" 
                                    onclick="getVCode();">获取验证码</button>
                        </div>
                        <div class="form-hint">验证码将发送到您绑定的手机号</div>
                    </div>
                </c:if>
            </form>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button type="button" id="submitButton" class="btn-primary" onclick="check();">
                <i class="ace-icon fa fa-edit"></i>
                <span>修改密码</span>
            </button>
            <button type="button" class="btn-secondary" onclick="returnIndex();">
                <i class="ace-icon fa fa-undo"></i>
                <span>取消</span>
            </button>
        </div>
        
        <!-- 安全提示 -->
        <div class="security-tips">
            <div class="tips-title">
                <i class="ace-icon fa fa-shield"></i>
                安全提示
            </div>
            <ul class="tips-list">
                <li>请定期更换密码，建议每3-6个月更换一次</li>
                <li>不要使用与其他网站相同的密码</li>
                <li>不要在公共场所输入密码</li>
                <li>密码修改成功后需要重新登录</li>
            </ul>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>处理中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let count = 180;
        let btnDisable = false;
        const regex = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[\W_!@#$%^&*`~()-+=]).{${passwordLength},}$/;

        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();

            // 绑定验证码按钮事件
            <c:if test="${schoolCode == '100014'}">
                $("#butInfo").click(function() {
                    if (count == 180) {
                        clock();
                    }
                });
            </c:if>
        }

        // 验证密码
        function check() {
            if ($("#oldPass").val() == "") {
                showAlert("请输入原密码！");
                return false;
            }

            if ($("#newPass1").val() == "") {
                showAlert("请输入新密码！");
                return false;
            }

            if ($("#newPass2").val() == "") {
                showAlert("请输入确认新密码");
                return false;
            }

            if (!regex.test($("#newPass1").val().trim())) {
                showAlert("新密码必须包含大写、小写字母、数字、特殊字符，且密码长度至少为${passwordLength}位。");
                return false;
            }

            if ($("#oldPass").val().trim() == $("#newPass1").val().trim()) {
                showAlert("新密码不能与原密码相同！");
                return false;
            }

            if ($("#newPass1").val().trim() != $("#newPass2").val().trim()) {
                showAlert("新密码输入不一致，请重新输入");
                return false;
            }

            <c:if test="${schoolCode == '100014'}">
                var verifycode = $("#verifycode").val().trim();
                if (verifycode == "" || verifycode == undefined || verifycode == null) {
                    showAlert("验证码不能为空，请填写验证码！");
                    return false;
                }
            </c:if>

            updatePassword();
        }

        // 更新密码
        function updatePassword() {
            showLoading(true);
            $("#submitButton").prop('disabled', true);

            $.ajax({
                url: "/student/rollManagement/personalInfoUpdate/doupdatepassword",
                type: "post",
                data: {
                    newPass1: $("#newPass1").val().trim(),
                    <c:if test="${schoolCode == '100014'}">verifycode: $("#verifycode").val().trim(),</c:if>
                    oldPass: $("#oldPass").val().trim()
                },
                success: function(d) {
                    if (d.flag == "fail") {
                        showAlert(d.msg);
                    }
                    if (d.flag == "success") {
                        showAlert(d.msg, function() {
                            setTimeout(function() {
                                location.href = "/logout";
                            }, 500);
                        });
                    }
                },
                error: function(xhr) {
                    showAlert("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                    $("#submitButton").prop('disabled', false);
                }
            });
        }

        // 检查旧密码
        function checkOldPass() {
            if ($("#oldPass").val() == "") {
                return;
            }

            $.ajax({
                url: "/student/rollManagement/personalInfoUpdate/checkOldPass",
                type: "post",
                data: {
                    oldPass: $("#oldPass").val()
                },
                success: function(d) {
                    if (d.flag == "fail") {
                        showAlert("原密码输入错误，请重新输入");
                    }
                },
                error: function(xhr) {
                    console.log("检查密码失败");
                }
            });
        }

        <c:if test="${schoolCode == '100014'}">
        // 验证码倒计时
        function clock() {
            if (count == 0) {
                $('#butInfo').removeAttr("disabled");
                $("#butInfo").text("重新获取");
                btnDisable = false;
                count = 180;
                return;
            } else {
                $('#butInfo').attr("disabled", "disabled");
                $("#butInfo").text(count + "s");
                btnDisable = true;
                count--;
            }
            setTimeout(function() {
                clock();
            }, 1000);
        }

        // 获取验证码
        function getVCode() {
            if (!btnDisable) {
                showLoading(true);

                $.ajax({
                    url: "/forgetPassword/getVerificationCode",
                    cache: false,
                    type: "post",
                    data: {
                        loginId: "${idNumber}",
                        tokenValue: $("#tokenValue").val()
                    },
                    dataType: "json",
                    success: function(d) {
                        if (d.result == "ok") {
                            showAlert(d.resultMess);
                        } else if (d.result == "empty") {
                            showAlert("您没有维护手机号！");
                        } else if (d.result == "error") {
                            showAlert("出错了，请联系教务处！");
                        } else if (d.result == "errorcode") {
                            var mess = d.resultMess;
                            if (mess != null && mess != "" && mess != undefined) {
                                showAlert(mess);
                            } else {
                                showAlert("生成验证码出错，请联系教务处！");
                            }
                        }
                    },
                    error: function(xhr) {
                        showAlert("发送失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }
        </c:if>

        // 返回上一页
        function returnIndex() {
            history.back();
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示提示信息
        function showAlert(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }
    </script>
</body>
</html>
