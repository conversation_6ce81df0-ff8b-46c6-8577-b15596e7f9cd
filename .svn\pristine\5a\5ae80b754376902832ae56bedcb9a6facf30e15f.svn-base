<%--
  Created by IntelliJ IDEA.
  User: gdx
  Date: 2025/5/17/017
  Time: 11:00
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<html>
<head>
    <title>我的答辩</title>
    <style type="text/css">
    </style>
</head>
<body>
<div class="row">
    <div class="col-xs-12 self-margin">
        <h4 class="header smaller lighter grey">
            <i class="glyphicon glyphicon-list"></i> 我的答辩
        </h4>

        <ul class="nav nav-tabs">
            <c:forEach items="${xsxtfabs}" var="xsxtfa" varStatus="i">
                <li <c:if test="${i.first}">class="active"</c:if>>
                    <a data-toggle="tab" href="#xsxtfa_${xsxtfa.tmbh}">
                            ${xsxtfa.pcmc}【${xsxtfa.famc}】${xsxtfa.dbxzlbmc}
                    </a>
                </li>
            </c:forEach>

        </ul>
        <div class="tab-content">
            <c:forEach items="${xsxtfabs}" var="xsxtfa" varStatus="i">
                <div id="xsxtfa_${xsxtfa.tmbh}" class="tab-pane fade <c:if test="${i.first}">in active</c:if>">
                    <div class="profile-user-info profile-user-info-striped self">
                        <div class="profile-info-row">
                            <div class="profile-info-name">论文题目</div>
                            <div class="profile-info-value">${xsxtfa.tmmc}</div>
                            <div class="profile-info-name">方案年级</div>
                            <div class="profile-info-value">${xsxtfa.nj}</div>
                        </div>
                        <div class="profile-info-row">
                            <div class="profile-info-name">方案院系</div>
                            <div class="profile-info-value">${xsxtfa.xsm}</div>
                            <div class="profile-info-name">方案专业</div>
                            <div class="profile-info-value">${xsxtfa.zym}</div>
                        </div>
                        <div class="profile-info-row">
                            <div class="profile-info-name">指导教师</div>
                            <div class="profile-info-value">${xsxtfa.jsm}</div>
                            <div class="profile-info-name">第二指导教师</div>
                            <div class="profile-info-value">${xsxtfa.jsm2nd}</div>
                        </div>
                        <div class="profile-info-row">
                            <div class="profile-info-name">答辩资格审查结果</div>
                            <div class="profile-info-value">${xsxtfa.dbzgscjg}</div>
                            <div class="profile-info-name">答辩资格审查时间</div>
                            <div class="profile-info-value">${xsxtfa.dbzgczsj}</div>
                        </div>
                        <div class="profile-info-row">
                            <div class="profile-info-name">答辩时间</div>
                            <div class="profile-info-value">${xsxtfa.dbsj}</div>
                            <div class="profile-info-name">答辩地点</div>
                            <div class="profile-info-value">${xsxtfa.dbdd}</div>
                        </div>
                        <div class="profile-info-row">
                            <div class="profile-info-name">答辩小组</div>
                            <div class="profile-info-value">${xsxtfa.dbxzmc}</div>
                            <c:if test="${schoolid == '100015'}">
                                <div class="profile-info-name">答辩小组成员</div>
                                <div class="profile-info-value">${xsxtfa.dbcy}</div>
                            </c:if>
                            <c:if test="${schoolid != '100015'}">
                                <div class="profile-info-name" style="background-color: white;"></div>
                                <div class="profile-info-value"></div>
                            </c:if>
                        </div>
                    </div>
                </div>
            </c:forEach>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
    });
</script>
</body>
</html>
