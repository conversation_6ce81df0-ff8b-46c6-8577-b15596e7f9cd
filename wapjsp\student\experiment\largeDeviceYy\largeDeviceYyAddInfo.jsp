<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<!DOCTYPE html>
<html>
<head>
    <title>大型设备预约申请</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="/wapjsp/css/mobile-framework.css">
    <link rel="stylesheet" href="/wapjsp/css/font-awesome.min.css">
    <script src="/wapjsp/js/jquery.min.js"></script>
    <script src="/wapjsp/js/layer.js"></script>
    <script src="/wapjsp/js/urp.js"></script>
    <style>
        .device-info-card {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: 16px;
            margin-bottom: 16px;
            border-radius: 12px;
        }
        
        .device-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .device-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 14px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .detail-label {
            opacity: 0.9;
        }
        
        .detail-value {
            font-weight: 500;
        }
        
        .form-section {
            background: var(--card-background);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: var(--card-shadow);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-icon {
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 6px;
            display: block;
        }
        
        .form-label.required::before {
            content: '*';
            color: var(--error-color);
            margin-right: 4px;
        }
        
        .form-input, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            background: var(--background-primary);
            color: var(--text-primary);
            min-height: 44px;
        }
        
        .form-input:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .week-selector {
            background: var(--background-secondary);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
        }
        
        .week-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
            margin-top: 8px;
        }
        
        .week-item {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 8px 4px;
            text-align: center;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .week-item.disabled {
            background: var(--background-secondary);
            color: var(--text-secondary);
            cursor: not-allowed;
            opacity: 0.5;
        }
        
        .week-item.available {
            background: var(--success-light);
            border-color: var(--success-color);
            color: var(--success-dark);
        }
        
        .week-item.selected {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        
        .schedule-selector {
            background: var(--background-secondary);
            border-radius: 8px;
            padding: 12px;
        }
        
        .schedule-grid {
            display: grid;
            grid-template-columns: 60px repeat(7, 1fr);
            gap: 2px;
            margin-top: 8px;
        }
        
        .schedule-header {
            background: var(--primary-color);
            color: white;
            padding: 8px 4px;
            text-align: center;
            font-size: 11px;
            font-weight: 500;
            border-radius: 4px;
        }
        
        .schedule-cell {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 6px 4px;
            text-align: center;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .schedule-cell.disabled {
            background: var(--background-secondary);
            color: var(--text-secondary);
            cursor: not-allowed;
            opacity: 0.5;
        }
        
        .schedule-cell.available {
            background: var(--success-light);
            border-color: var(--success-color);
            color: var(--success-dark);
        }
        
        .schedule-cell.selected {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        
        .submit-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--card-background);
            border-top: 1px solid var(--border-color);
            padding: 16px;
            display: flex;
            gap: 12px;
            z-index: 100;
        }
        
        .btn-submit {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-cancel {
            background: var(--background-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .selected-info {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
            font-size: 14px;
            color: var(--info-dark);
        }
        
        .info-title {
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .info-content {
            line-height: 1.4;
        }
        
        .container-mobile {
            padding-bottom: 80px; /* 为底部固定按钮留出空间 */
        }
        
        .section-header-mobile {
            padding: 16px;
            background: var(--background-secondary);
            border-bottom: 1px solid var(--border-color);
        }
        
        .section-title-mobile {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title-mobile i {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="history.back();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">设备预约申请</div>
            <div class="navbar-action" onclick="resetForm();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-mobile">
            <!-- 页面标题 -->
            <div class="section-header-mobile">
                <h3 class="section-title-mobile">
                    <i class="fa fa-cogs"></i>
                    大型实验设备预约申请
                </h3>
            </div>

            <!-- 设备信息卡片 -->
            <div class="device-info-card">
                <div class="device-title">${sbmc}</div>
                <div class="device-details">
                    <div class="detail-item">
                        <span class="detail-label">规格</span>
                        <span class="detail-value">${sbgg}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">型号</span>
                        <span class="detail-value">${sbxh}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">存放地点</span>
                        <span class="detail-value">${cfdd}</span>
                    </div>
                </div>
            </div>

            <!-- 申请信息表单 -->
            <form name="addInfo" id="addInfo">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fa fa-edit section-icon"></i>
                        申请信息
                    </h4>
                    
                    <div class="form-group">
                        <label class="form-label required">参与人数</label>
                        <input type="number" name="cyrs" id="cyrs" class="form-input" 
                               placeholder="请输入参与人数" min="1" max="50" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">预约说明</label>
                        <textarea name="yyztsm" id="yyztsm" class="form-textarea" 
                                  placeholder="请输入预约说明（选填）" maxlength="250"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">备注</label>
                        <textarea name="bz" id="bz" class="form-textarea" 
                                  placeholder="请输入备注信息（选填）" maxlength="250"></textarea>
                    </div>
                </div>

                <!-- 周次选择 -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fa fa-calendar section-icon"></i>
                        选择周次
                    </h4>
                    
                    <div class="week-selector">
                        <div class="week-grid" id="weekGrid">
                            <c:forEach begin="1" end="${zc}" step="1" var="i">
                                <div class="week-item ${i < nowzc ? 'disabled' : 'available'}" 
                                     data-week="${i}" onclick="selectWeek(${i})">
                                    第${i}周
                                </div>
                            </c:forEach>
                        </div>
                        
                        <div class="selected-info" id="selectedWeeksInfo" style="display: none;">
                            <div class="info-title">已选择周次：</div>
                            <div class="info-content" id="selectedWeeksText"></div>
                        </div>
                    </div>
                </div>

                <!-- 时间选择 -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fa fa-clock-o section-icon"></i>
                        选择时间
                    </h4>
                    
                    <div class="schedule-selector">
                        <div class="schedule-grid" id="scheduleGrid">
                            <!-- 表头 -->
                            <div class="schedule-header">节次</div>
                            <div class="schedule-header">周一</div>
                            <div class="schedule-header">周二</div>
                            <div class="schedule-header">周三</div>
                            <div class="schedule-header">周四</div>
                            <div class="schedule-header">周五</div>
                            <div class="schedule-header">周六</div>
                            <div class="schedule-header">周日</div>
                            
                            <!-- 时间格子 -->
                            <c:forEach begin="1" end="12" step="1" var="period">
                                <div class="schedule-header">${period}</div>
                                <c:forEach begin="1" end="7" step="1" var="day">
                                    <div class="schedule-cell available" 
                                         data-day="${day}" data-period="${period}" 
                                         onclick="selectSchedule(${day}, ${period})">
                                    </div>
                                </c:forEach>
                            </c:forEach>
                        </div>
                        
                        <div class="selected-info" id="selectedScheduleInfo" style="display: none;">
                            <div class="info-title">已选择时间：</div>
                            <div class="info-content" id="selectedScheduleText"></div>
                        </div>
                    </div>
                </div>

                <!-- 隐藏字段 -->
                <input type="hidden" name="sbbm" id="sbbm" value="${sbbm}">
                <input type="hidden" name="weekNum1" id="weekNum1" value="">
                <input type="hidden" name="sessions" id="sessions" value="">
                <input type="hidden" name="xqjc" id="xqjc" value="">
                <input type="hidden" name="yzzc" id="yzzc" value="">
                <input type="hidden" name="tokenValue" id="tokenValue" value="${token_in_session}">
                <input type="hidden" name="SQID" id="SQID" value="${id}">
                <input type="hidden" name="SBID" id="SBID" value="${rwsqId}">
            </form>
        </div>

        <!-- 底部操作按钮 -->
        <div class="submit-actions">
            <button type="button" class="btn-cancel" onclick="history.back();">
                <i class="fa fa-times"></i>
                取消
            </button>
            <button type="button" class="btn-submit" onclick="doSave();">
                <i class="fa fa-check"></i>
                提交申请
            </button>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedWeeks = [];
        let selectedSchedules = [];
        let weekDays = ["", "周一", "周二", "周三", "周四", "周五", "周六", "周日"];
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            initializePage();
            loadAvailableSchedules();
        });
        
        // 初始化页面
        function initializePage() {
            // 添加触摸事件处理
            $('.week-item, .schedule-cell').on('touchstart', function() {
                if (!$(this).hasClass('disabled')) {
                    $(this).css('transform', 'scale(0.95)');
                }
            }).on('touchend', function() {
                $(this).css('transform', '');
            });
        }
        
        // 加载可用时间段
        function loadAvailableSchedules() {
            // 根据后端数据设置可用时间段
            const scheduleData = ${syskfSqbSj};
            
            if (scheduleData && scheduleData.length > 0) {
                scheduleData.forEach(function(item) {
                    const day = item[2];
                    const startPeriod = item[3];
                    const duration = item[4];
                    
                    if (duration === 1) {
                        // 单节课
                        $(`.schedule-cell[data-day="${day}"][data-period="${startPeriod}"]`)
                            .removeClass('disabled').addClass('available');
                    } else {
                        // 连续多节课
                        for (let i = 0; i < duration; i++) {
                            const period = startPeriod + i;
                            $(`.schedule-cell[data-day="${day}"][data-period="${period}"]`)
                                .removeClass('disabled').addClass('available');
                        }
                    }
                });
            }
        }
        
        // 选择周次
        function selectWeek(week) {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            const $weekItem = $(`.week-item[data-week="${week}"]`);
            
            if ($weekItem.hasClass('disabled')) {
                return;
            }
            
            if ($weekItem.hasClass('selected')) {
                // 取消选择
                $weekItem.removeClass('selected');
                selectedWeeks = selectedWeeks.filter(w => w !== week);
            } else {
                // 选择
                $weekItem.addClass('selected');
                selectedWeeks.push(week);
            }
            
            updateSelectedWeeksDisplay();
        }
        
        // 更新已选择周次显示
        function updateSelectedWeeksDisplay() {
            if (selectedWeeks.length > 0) {
                selectedWeeks.sort((a, b) => a - b);
                const weeksText = selectedWeeks.map(w => `第${w}周`).join('、');
                $('#selectedWeeksText').text(weeksText);
                $('#selectedWeeksInfo').show();
                $('#weekNum1').val(selectedWeeks.join(','));
                
                // 生成周次二进制字符串
                let weekBinary = '';
                for (let i = 1; i <= 24; i++) {
                    weekBinary += selectedWeeks.includes(i) ? '1' : '0';
                }
                $('#yzzc').val(weekBinary);
            } else {
                $('#selectedWeeksInfo').hide();
                $('#weekNum1').val('');
                $('#yzzc').val('');
            }
        }
        
        // 选择时间段
        function selectSchedule(day, period) {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            const $scheduleCell = $(`.schedule-cell[data-day="${day}"][data-period="${period}"]`);
            
            if ($scheduleCell.hasClass('disabled')) {
                return;
            }
            
            const scheduleKey = `${day}/${period}`;
            
            if ($scheduleCell.hasClass('selected')) {
                // 取消选择
                $scheduleCell.removeClass('selected');
                selectedSchedules = selectedSchedules.filter(s => s !== scheduleKey);
            } else {
                // 选择
                $scheduleCell.addClass('selected');
                selectedSchedules.push(scheduleKey);
            }
            
            updateSelectedScheduleDisplay();
        }
        
        // 更新已选择时间段显示
        function updateSelectedScheduleDisplay() {
            if (selectedSchedules.length > 0) {
                const scheduleText = selectedSchedules.map(s => {
                    const [day, period] = s.split('/');
                    return `${weekDays[day]}第${period}节`;
                }).join('、');
                
                $('#selectedScheduleText').text(scheduleText);
                $('#selectedScheduleInfo').show();
                $('#sessions').val(selectedSchedules.join(','));
                
                // 生成时间段信息
                generateScheduleInfo();
            } else {
                $('#selectedScheduleInfo').hide();
                $('#sessions').val('');
                $('#xqjc').val('');
            }
        }
        
        // 生成时间段信息
        function generateScheduleInfo() {
            const weekSchedules = {};
            
            selectedSchedules.forEach(function(schedule) {
                const [day, period] = schedule.split('/');
                if (!weekSchedules[day]) {
                    weekSchedules[day] = [];
                }
                weekSchedules[day].push(period);
            });
            
            let scheduleInfo = '';
            Object.keys(weekSchedules).forEach(function(day) {
                const periods = weekSchedules[day].sort((a, b) => a - b);
                scheduleInfo += `${weekDays[day]}:${periods.join(',')} `;
            });
            
            $('#xqjc').val(scheduleInfo.trim());
        }
        
        // 重置表单
        function resetForm() {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            layer.confirm('确定要重置表单吗？', {
                icon: 3,
                title: '确认重置',
                skin: 'layer-mobile'
            }, function(index) {
                layer.close(index);
                
                // 重置表单
                $('#addInfo')[0].reset();
                
                // 重置选择状态
                selectedWeeks = [];
                selectedSchedules = [];
                $('.week-item').removeClass('selected');
                $('.schedule-cell').removeClass('selected');
                $('#selectedWeeksInfo').hide();
                $('#selectedScheduleInfo').hide();
                
                // 清空隐藏字段
                $('#weekNum1, #sessions, #xqjc, #yzzc').val('');
                
                showSuccess('表单已重置');
            });
        }
        
        // 提交申请
        function doSave() {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            // 表单验证
            if (!validateForm()) {
                return;
            }
            
            layer.confirm('确定提交预约申请吗？', {
                icon: 3,
                title: '确认提交',
                skin: 'layer-mobile'
            }, function(index) {
                layer.close(index);
                
                const loadingIndex = layer.load(0, {
                    shade: [0.2, "#000"]
                });
                
                $.ajax({
                    url: "/student/experiment/largeDeviceYy/saveAddInfo",
                    cache: false,
                    type: "post",
                    data: $('#addInfo').serialize(),
                    dataType: "json",
                    success: function(d) {
                        layer.close(loadingIndex);
                        
                        if (d.success) {
                            showSuccess('预约申请提交成功！');
                            setTimeout(function() {
                                history.back();
                            }, 1500);
                        } else {
                            showError(d.message || '提交失败，请重试！');
                        }
                    },
                    error: function(xhr) {
                        layer.close(loadingIndex);
                        showError('网络错误，请检查网络连接后重试！');
                    }
                });
            });
        }
        
        // 表单验证
        function validateForm() {
            const cyrs = $('#cyrs').val().trim();
            const weekNum1 = $('#weekNum1').val();
            const sessions = $('#sessions').val();
            
            if (!cyrs) {
                showError('请输入参与人数！');
                $('#cyrs').focus();
                return false;
            }
            
            if (isNaN(cyrs) || parseInt(cyrs) < 1 || parseInt(cyrs) > 50) {
                showError('参与人数必须是1-50之间的数字！');
                $('#cyrs').focus();
                return false;
            }
            
            if (!weekNum1) {
                showError('请选择预约周次！');
                return false;
            }
            
            if (!sessions) {
                showError('请选择预约时间！');
                return false;
            }
            
            return true;
        }
        
        // 显示成功信息
        function showSuccess(message) {
            layer.msg(message, {
                icon: 1,
                skin: 'layer-mobile'
            });
        }
        
        // 显示错误信息
        function showError(message) {
            layer.alert(message, {
                icon: 2,
                title: '提示',
                skin: 'layer-mobile'
            });
        }
    </script>
</body>
</html>
