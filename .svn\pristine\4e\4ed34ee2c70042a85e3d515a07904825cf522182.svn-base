<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!-- 缓存 -->
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
	<head>
		<link rel="stylesheet" href="/css/phone/phone.css" type="text/css"></link>
		<style type="text/css">
			.self-margin .header {
			    margin-top: 6px !important;
			    margin-bottom: 10px !important;
			    padding-bottom: 4px !important;
			    border-bottom: 1px solid #CCC;
			    line-height: 28px;
			}
			
			.header.grey {
			    border-bottom-color: #c3c3c3;
			}
			h4.smaller {
			    font-size: 17px;
			}
			.header {
			    line-height: 28px;
			    margin-bottom: 16px;
			    margin-top: 18px;
			    padding-bottom: 4px;
			    border-bottom: 1px solid #CCC;
			}
			.grey {
			    color: #777 !important;
			}
			.lighter {
			    font-weight: lighter;
			}
            
            .btn.btn-round {
			    border-radius: 4px !important;
			}
			
			.btn.btn-bold, .btn.btn-round {
			    border-bottom-width: 2px;
			}
			
			.btn-group-xs>.btn, .btn-xs {
			    padding-top: 3px;
			    padding-bottom: 3px;
			    border-width: 3px;
			}
			.btn {
			    color: #FFF !important;
			    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);
			    background-image: none !important;
			    border: 5px solid #FFF;
			    border-radius: 0;
			    box-shadow: none !important;
			    -webkit-transition: background-color .15s, border-color .15s, opacity .15s;
			    -o-transition: background-color .15s,border-color .15s,opacity .15s;
			    transition: background-color .15s, border-color .15s, opacity .15s;
			    vertical-align: middle;
			    margin: 0;
			    position: relative;
			    font-weight: 400;
			}
			.breadcrumb, .breadcrumb>li>a, .btn {
			    display: inline-block;
			}
			.btn, .dropdown-colorpicker a {
			    cursor: pointer;
			}
			.btn-group-xs>.btn, .btn-xs {
			    padding: 1px 5px;
			    font-size: 12px;
			    line-height: 1.3;
			    border-radius: 3px;
			}
			.btn, .btn-danger.active, .btn-danger:active, .btn-default.active, .btn-default:active, .btn-info.active, .btn-info:active, .btn-primary.active, .btn-primary:active, .btn-warning.active, .btn-warning:active, .btn.active, .btn:active, .dropdown-menu>.disabled>a:focus, .dropdown-menu>.disabled>a:hover, .form-control, .navbar-toggle, .open>.dropdown-toggle.btn-danger, .open>.dropdown-toggle.btn-default, .open>.dropdown-toggle.btn-info, .open>.dropdown-toggle.btn-primary, .open>.dropdown-toggle.btn-warning {
			    background-image: none;
			}
			button, input, select, textarea {
			    font-family: inherit;
			    font-size: inherit;
			    line-height: inherit;
			}
			button, html input[type=button], input[type=reset], input[type=submit] {
			    -webkit-appearance: button;
			    cursor: pointer;
			}
			button, select {
			    text-transform: none;
			}
			button {
			    overflow: visible;
			}
			button, input, optgroup, select, textarea {
			    color: inherit;
			    font: inherit;
			    margin: 0;
			}
			.btn, .btn-default, .btn-default.focus, .btn-default:focus, .btn.focus, .btn:focus {
			    background-color: #ABBAC3 !important;
			    border-color: #ABBAC3;
			}
			.btn-info, .btn-info.focus, .btn-info:focus {
			    background-color: #6FB3E0 !important;
			    border-color: #6FB3E0;
			}
			.btn-success, .btn-success.focus, .btn-success:focus {
			    background-color: #87B87F !important;
			    border-color: #87B87F;
			}
			.btn-purple, .btn-purple.focus, .btn-purple:focus {
			    background-color: #9585BF !important;
			    border-color: #9585BF;
			}
			.self div.profile-info-value > select {
			    width: 150px !important;
			    height: 25px !important;
			    line-height: 25px !important;
			    padding: 0;
			}
			.self div.profile-info-value > input[type=text] {
			    width: 148px !important;
			    height: 23px !important;
			    line-height: 23px !important;
			    padding: 0;
			}
			input[type=color], input[type=date], input[type=datetime-local], input[type=datetime], input[type=email], input[type=month], input[type=number], input[type=password], input[type=search], input[type=tel], input[type=text], input[type=time], input[type=url], input[type=week], textarea {
			    border-radius: 0 !important;
			    color: #858585;
			    background-color: #fff;
			    border: 1px solid #d5d5d5;
			    padding: 5px 4px 6px;
			    font-size: 14px;
			    font-family: inherit;
			    -webkit-box-shadow: none !important;
			    box-shadow: none !important;
			    -webkit-transition-duration: .1s;
			    transition-duration: .1s;
			}
			input[type=color]:focus,input[type=date]:focus,input[type=datetime-local]:focus,input[type=datetime]:focus,input[type=email]:focus,input[type=month]:focus,input[type=number]:focus,input[type=password]:focus,input[type=search]:focus,input[type=tel]:focus,input[type=text]:focus,input[type=time]:focus,input[type=url]:focus,input[type=week]:focus,textarea:focus {
			    -webkit-box-shadow: none;
			    box-shadow: none;
			    color: #696969;
			    border-color: #f59942;
			    background-color: #fff;
			    outline: 0
			}
			.form-control,select {
			    border-radius: 0;
			    -webkit-box-shadow: none!important;
			    box-shadow: none!important;
			    color: #858585;
			    background-color: #fff;
			    border: 1px solid #d5d5d5
			}
			
			.form-control:focus,select:focus {
			    color: #696969;
			    border-color: #f59942;
			    background-color: #fff;
			    outline: 0
			}
			
			.modal-open .modal {
			    overflow-x: hidden;
			    overflow-y: auto;
			}
			.fade.in {
			    opacity: 1;
			}
			
			.modal {
			    display: none;
			    position: fixed;
			    top: 0;
			    z-index: 1050;
			    -webkit-overflow-scrolling: touch;
			    outline: 0;
			}
			.modal, .modal-backdrop {
			    right: 0;
			    bottom: 0;
			    left: 0;
			}
			
			.modal.fade .modal-dialog {
			    -webkit-transform: translate(0, -25%);
			    -ms-transform: translate(0,-25%);
			    -o-transform: translate(0,-25%);
			    transform: translate(0, -25%);
			    -webkit-transition: -webkit-transform .3s ease-out;
			    -moz-transition: -moz-transform .3s ease-out;
			    -o-transition: -o-transform .3s ease-out;
			    transition: transform .3s ease-out;
			}
			.modal-dialog {
			    position: relative;
			    width: auto;
			    margin: 90px auto;
			}
			
			.modal-content {
			    border-radius: 0;
			    -webkit-box-shadow: none;
			    box-shadow: none;
			}
			
			.modal-content {
			    position: relative;
			    background-color: #fff;
			    border: 1px solid #999;
			    border: 1px solid rgba(0, 0, 0, .2);
			    border-radius: 6px;
			    -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
			    box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
			    outline: 0;
			}
			.modal-content, .popover {
			    background-clip: padding-box;
			}
			.no-padding {
			    padding: 0 !important;
			}
			
			.modal-header {
			    padding: 15px;
			    border-bottom: 1px solid #e5e5e5;
			}
			.table-header {
			    background-color: #307ECC;
			    color: #FFF;
			    font-size: 14px;
			    line-height: 38px;
			    padding-left: 12px;
			    margin-bottom: 1px;
			}
			.table-header .close {
			    margin-right: 8px;
			    margin-top: 0;
			    opacity: .45;
			    filter: alpha(opacity=45);
			}
			
			.modal-header .close {
			    font-size: 32px;
			}
			.modal-header .close {
			    margin-top: 2px;
			    color: white;
			}
			button.close {
			    padding: 0;
			    cursor: pointer;
			    background: 0 0;
			    border: 0;
			    -webkit-appearance: none;
			}
			.close {
			    float: right;
			    font-size: 21px;
			    color: #000;
			    text-shadow: 0 1px 0 #fff;
			    opacity: .2;
			    filter: alpha(opacity=20);
			}
			.alert .alert-link, .close {
			    font-weight: 700;
			}
			.badge, .close, .label {
			    line-height: 1;
			}
			.modal-body {
			    position: relative;
			    padding: 15px;
			}
			.modal-footer {
			    padding-top: 12px;
			    padding-bottom: 14px;
			    border-top-color: #E4E9EE;
			    -webkit-box-shadow: none;
			    box-shadow: none;
			    background-color: #EFF3F8;
			}
			
			.no-margin-top {
			    margin-top: 0 !important;
			}
			.modal-footer {
			    padding: 15px;
			    text-align: right;
			    border-top: 1px solid #e5e5e5;
			}
			
			.modal-backdrop.in {
			    opacity: .5;
			    filter: alpha(opacity=50);
			}
			
			.modal-backdrop {
			    position: fixed;
			    top: 0;
			    z-index: 1040;
			    background-color: #000;
			}
			.fade {
			    opacity: 0;
			    -webkit-transition: opacity .15s linear;
			    -o-transition: opacity .15s linear;
			    transition: opacity .15s linear;
			}
			.phone-control-input {
			    width: calc(100% - 10px);
			    height: 18px;
			    line-height: 18px;
			    padding: 5px;
			}
			.phone-control-select {
			    width: 100%;
			    height: 30px;
			    line-height: 30px;
			}
			.control-label {
				height: 40px;
			}
			.alert {
			    font-size: 14px;
			}
			
			.alert, .well {
			    border-radius: 0;
			}
			.alert-success {
			    background-color: #dff0d8;
			    border-color: #d6e9c6;
			    color: #3c763d;
			}
			.alert {
			    padding: 15px;
			    border: 1px solid transparent;
			    border-radius: 4px;
			}
			.alert, .thumbnail {
			    margin-bottom: 20px;
			}
		</style>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<script src="/js/jQuery/jquery-3.4.1.min.js"></script>
		<script type="text/javascript" src="/assets/layer/layer.js"></script>
		<script type="text/javascript" src="/js/json/json2.js"></script>
		<script type="text/javascript">
			function Urp() {
			};
			
			Urp.prototype = {
				"alert": function (msg, callback) {
					layer.msg(msg);
					if (typeof callback === 'function') {
						setTimeout(callback, 1000);
					}
				}
			};
			urp = new Urp();
		
			var yxkc = "";
			var yxkch = "";
			var bxkc = new Array();
			var weekZw = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"];
			var xxbm = "${xxbm}";
			let xkfs = "${xkfs}";
			let xkfsm = xkfs=="jhxk" ? "计划" : (xkfs=="faxk" ? "方案" : (xkfs=="xarxk" ? "校任" : (xkfs=="xirxk" ? "系任" : (xkfs=="zyxk" ? "自由" : (xkfs=="cxxk" ? "重修" : (xkfs=="fxxk" ? "复修" : ""))))));
			$("title").html(xkfsm + "选课");
			var xkjdlx = "${xkjdlx}";
			
			function centerModals() {
	            $('#search-modal').each(function (i) {
	                var $clone = $(this).clone().css('display', 'block').appendTo('body');
	                var top = Math.round(($clone.height() - $clone.find('.modal-content').height()) / 2);
	                top = top > 0 ? top : 0;
	                $clone.remove();
	                $(this).find('.modal-content').css("top", top);
	            });
	        }

	        $(window).on('resize', centerModals);
			
			$(function(){
				
				if(xxbm == "100010" && xkjdlx == "005") {
					tsxx("test", "calc(100vw - 20px)");
				}
				
				$("#xkfs_title").html(xkfsm);
				
				if(xkfs=="faxk" && xxbm == "100010" && xkjdlx == "005") {
					$("#jhxn").val("");
				}

				setTimeout(function() {
					$("#courseList_div").css("height", "calc(100vh - "+ ($("#navbar-container").height() + $("#breadcrumbs").height() + 140) +"px)");
				}, 1000);
				
				if(xkfs == "jhxk" || xkfs == "cxxk" || xkfs == "fxxk") {
					guolv();
				}
			});
			
			function guolv(){
				<c:if test="${xkfs == 'zyxk'}">
					var kkxsh = $("#kkxsh").val();
					var kch = $("#kch").val();
					var kcm = $("#kcm").val();
					var skjs = $("#skjs").val();
					var kclbdm = $("#kclbdm").val();
					if(kkxsh == "" && kch == "" && kcm == "" && skjs == "" && kclbdm == "") {
						urp.alert("请至少选择或填写一个条件！");
						return;
					}
				</c:if>
					
				let url = xkfs=="jhxk" ? "/student/courseSelect/intentCourse/courseList" : 
					(xkfs=="faxk" ? "/student/courseSelect/planCourse/courseList" : 
						(xkfs=="xarxk" ? "/student/courseSelect/schoolCourse/courseList" : 
							(xkfs=="xirxk" ? "/student/courseSelect/departCourse/courseList" : 
								(xkfs=="zyxk" ? "/student/courseSelect/freeCourse/courseList" : 
									(xkfs=="cxxk" ? "/student/courseSelect/relearnCourse/courseList" : 
										(xkfs=="fxxk" ? "/student/courseSelect/reViewCourse/courseList" : ""))))));
				let index;
				$.ajax({
					url: url,
					method: "post",
					data: {
						mobile: "true",
						fajhh: "${fajhh}",
						<c:if test="${xkfs == 'jhxk'}">
							mxbj: $("#filterClassCourses").length > 0 && $("#filterClassCourses").is(":checked") ? "1" : "0",
						</c:if>
						<c:if test="${xkfs == 'faxk'}">
							jhxn: $("#jhxn").val(),
							kcsxdm: $("#kcsxdm").val(),
							kch: $("#kch").val(),
							kcm: $("#kcm").val(),
							kxh: $("#kxh").val(),
							kclbdm: $("#kclbdm").val(),
							kzh: $("#kzh").val(),
							xqh: $("#xqh").val(),
						</c:if>
						<c:if test="${xkfs == 'faxk' || xkfs == 'xirxk' || xkfs == 'xarxk' || xkfs == 'zyxk'}">
							xq: "0",
							jc: "0",
						</c:if>
						<c:if test="${xkfs == 'xirxk' || xkfs == 'xarxk'}">
							searchtj: $("input[name=searchtj]").val(),
							kclbdm: $("#kclbdm").val(),
						</c:if>
						<c:if test="${xkfs == 'zyxk'}">
							kkxsh: kkxsh, kch: kch, kcm: kcm, skjs: skjs,
							kclbdm: $("#kclbdm").val(),
						</c:if>
							kzmc: $("#kzmc").length > 0 ? $("#kzmc").val() : ""
					},
					beforeSend : function(){
						index = layer.load(0, {
							shade : [ 0.2, "#000"]
						});
					},
					success: function(data){
						yxkc = eval("(" + data["yxkclist"] + ")");
						yxkch = data["kchlist"];
						fillfakcbody(data);
						if((xkfs!="cxxk" && xkfs != "fxxk") || (xkfs=="cxxk" && "${byscxpdct}" != "1" && "${cxpctfs}" == "any") || (xkfs=="fxxk" && "${fxpct}" == "1")){
							dealxtsjkc1();
						}
					},
					error: function(){
						urp.alert("查询课程失败，请稍后再试...");
					},
					complete : function(){
						layer.close(index);
					}
				});
			}
			
			//校验周次有无冲突
			function checkZC(a,b){
				var ff = false;
				if(a!=undefined && a!="" && b!=undefined && b!="" 
					&& Math.abs(parseInt(a, 2)&parseInt(b, 2))) {
					ff = true;
				}
				
				return ff;
			}
			
			function queryZjJsJl(id, obj) {
				if (id != undefined && $(obj).attr("title") == undefined) {
					$(obj).css("cursor", "wait");
					$.ajax({
						url: "/student/courseSelect/queryTeacherJL",
						method: "post",
						data: {id: id },
						dataType: "json",
						success: function(d) {
							var cont = "负责教师：" + d[0][0] + "\r\n";
							$(obj).attr("title", cont + (d[0][1]==null? "": d[0][1]));
						},
						error: function() {
						},
						complete : function(){
							$(obj).css("cursor", "pointer");
						}
					});
				}
			}
			
			function leaveJs(obj) {
				$(obj).css("cursor", "pointer");
			}
			
			function checkKchIsContained(kch) {
				if(!yxkch || yxkch.length == 0) {
					return true;
				} else {
					var length = yxkch.length;
					for(var i=0; i<length; i++) {
						if(yxkch[i] == kch) {
							return false;
						}
					}
					return true;
				}
			}
			
			function fillfakcbody(d){
				let data = xkfs=="jhxk" ? d["zdjhList"] : 
					(xkfs=="faxk" ? d["rwfalist"] : 
						(xkfs=="xarxk" ? d["rwXarxkZlList"] : 
							(xkfs=="xirxk" ? d["rwXirxkZlList"] : 
								(xkfs=="zyxk" ? d["rwRxkZlList"] : 
									(xkfs=="cxxk" ? d["rwCxxkList"] : 
										(xkfs=="fxxk" ? d["rwCxxkList"] : ""))))));
				data = xkfs=="cxxk" || xkfs=="fxxk" ? data : JSON.stringify(data).replace(/"null"/g, "\"\"").replace(/null/g, "\"\"")
				var datalist = eval(data);
				var kylMap = d["kylMap"];
				$("#courseList_div").html("");
				//方案课程列表
				if(datalist.length>0){
					var kyl = $("#kyl").val();
					var tempkey = "";
					for(var i=0;i<datalist.length;i++){
						var jl = datalist[i];
						var isShow = true;
						if(xxbm == "100010" && xkjdlx == "005") {
							isShow = false;
							if(jl.kcsxdm == "001") {
								isShow = true;
							}
						}
						let zxjxjhh = (jl.termCode || jl.zxjxjhh);
						let kch = (jl.courseNum || jl.kch);
						let kxh = (jl.classNum || jl.kxh);
						
						var kylKey = zxjxjhh + "_" + kch + "_" + kxh;
						var bkskyl = kylMap[kylKey] == undefined ? jl.bkskyl : kylMap[kylKey];
						if(isShow && (kyl == undefined || (kyl=="1" && bkskyl > 0) || (kyl=="0" && bkskyl <= 0)) && checkKchIsContained(kch)){
							if(tempkey != kylKey) {
								let tcont = "";
								tcont += "<div class=\"phone-message-item\">";
				                tcont += "<h5 style=\"margin-top: 0px; margin-bottom: 0px; border-bottom: 1px solid white;\"><i class=\"ace-icon fa fa-user bigger-110\"></i> <label><input style='"+ (d["pkyl"]== "1" && bkskyl<=0 ? "display:none;" : "") + " width: 18px; height: 18px;' type='checkbox' \
									id='"+kch+"_"+kxh+"_"+zxjxjhh+(xkfs=="cxxk" || xkfs=="fxxk" ? "_" +jl.tdkch+"_"+jl.cxfsdm : "")+"' class='ace ace-checkbox-2' name='kcId' value='"+JSON.stringify(jl)+"' \
									onclick='dealkc(this);'/><span class='lbl' style='vertical-align: top;'>" + jl.kcm + " <small>" + (kch + "_" + kxh) + "</small></span>\
									</label><span style='float: right; position: relative; border-radius: 20px; background: #1ba74c !important; padding: 2px 5px;' class='label'>" + jl.xf + "学分</span></h5>";
								tcont += "<table><tbody>";
								if(xkfs == "faxk") {
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>计划学年学期：</td>\
											<td style='text-align: left;'>" + (jl.schemeYear == "学年季学期" ? "暂未维护" : jl.schemeYear) + "</td>\
										</tr>\
										<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>方案课组名：</td>\
											<td style='text-align: left;'>" + jl.kzm + "</td>\
										</tr>";
								}
								if(xkfs == "xirxk" || xkfs == "xarxk") {
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>选课课组：</td>\
											<td style='text-align: left;'>" + jl.xkkzm + "</td>\
										</tr>";
								}
								if(xkfs == "zyxk") {
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>开课院系：</td>\
											<td style='text-align: left;'>" + jl.kkxsjc + "</td>\
										</tr>";
								}
								if(xkfs == "cxxk" || xkfs == "fxxk") {
									if(xkfs == "cxxk" && xxbm == "100008") {
										tcont += "<tr>\
												<td style='text-align: right; width: 120px; padding: 5px 0 0;'><span style='color: red;'>*</span>是否自学重修：</td>\
												<td style='text-align: left;'><label><input type='radio' name='sfzxcx_"+kylKey +"' value='ZX' class='ace'/><span class='lbl'>是</span></label> <label><input type='radio' name='sfzxcx_"+kylKey +"' value='' class='ace'/><span class='lbl'>否</span></label></td>\
											</tr>";
									}
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>备注：</td>\
											<td style='text-align: left;'>" + (jl.bz!="" ? jl.bz : "") + "</td>\
										</tr>\
										<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>被替代课程：</td>\
											<td style='text-align: left;'>" + (jl.tdkch== null || jl.tdkch == "" ? "无" : (jl.tdkcm +"(" + jl.tdkch + ")")) + "</td>\
										</tr>";
								}
								tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>课余量/已选人数：</td>\
											<td style='text-align: left;'>" + bkskyl+"/<a title='点击查看' href='javascript:void(0);' \
											onclick='viewXkCount(\""+zxjxjhh+"\",\""+kch+"\",\""+kxh+"\", this);'>点击查看</a></td>\
										</tr>\
										<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>上课时间地点：</td>\
											<td style='text-align: left;' id='"+ kylKey +"_sjdd'></td>\
										</tr>";
								tcont += "</table></tbody>";
				                tcont += " <div class=\"hr hr8 hr-dotted white\" style='border-top: 1px solid #FFF;'></div>";
				                tcont += " <div style=\"height: 20px; top: 5px; position: relative;\"> ";
				                tcont += " <span style='position: relative; border-radius: 20px; background: #1ba74c !important;padding: 2px 5px; top: 4px;' class='label'>" + jl.skjs + "</span> ";
			                    tcont += "<button class='btn btn-xs btn-white btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
			                            "border-bottom-width: 1px;border: 1px;'  onclick='showDetail(\"" + kylKey + "\");return false;'>详情</button>";
			                    if (jl.zkxh != "") {
			                        tcont += "<button class='btn btn-xs btn-white btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
			                                "border-bottom-width: 1px;border: 1px;'  onclick='showDetail(\"" + zxjxjhh+"_"+kch+"_"+jl.zkxh + "\");return false;'>主课堂</button>";
			                    }
				                tcont += " </div>";
				                tcont += " </div>";
				                tcont += " <div></div>";
								
								//详情隐藏
								tcont += "<table style='width: 100%; margin-bottom: 12px; display: none;' class='"+ kylKey +"_detail'>";
								tcont += "<thead>\
										<tr>\
											<th style='padding: 10px 3px; border-left: 0; font-size: 15px; background: #6fb3e0; border-radius: 8px !important;' colspan='2'>\
												【" + kch + "_" + kxh + "】"+ jl.kcm +"\
											</th>\
										</tr>\
									</thead>";
								tcont += "<tbody>";
								if(xkfs == "faxk") {
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>计划学年学期：</td>\
											<td style='text-align: left;'>" + (jl.schemeYear == "学年季学期" ? "暂未维护" : jl.schemeYear) + "</td>\
										</tr>\
										<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>方案课组名：</td>\
											<td style='text-align: left;'>" + jl.kzm + "</td>\
										</tr>";
								}
								if(xkfs == "xirxk" || xkfs == "xarxk") {
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>选课课组：</td>\
											<td style='text-align: left;'>" + jl.xkkzm + "</td>\
										</tr>";
									if(xkfs == "xarxk" && xxbm == '100018') {
										tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>课程课组：</td>\
											<td style='text-align: left;'>" + jl.kzm + "</td>\
										</tr>";
									}
								}
								if(xkfs == "zyxk") {
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>开课院系：</td>\
											<td style='text-align: left;'>" + jl.kkxsjc + "</td>\
										</tr>";
								}
								if(xkfs == "cxxk" || xkfs == "fxxk") {
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>备注：</td>\
											<td style='text-align: left;'>" + (jl.bz!="" ? jl.bz : "") + "</td>\
										</tr>\
										<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>被替代课程：</td>\
											<td style='text-align: left;'>" + (jl.tdkch== null || jl.tdkch == "" ? "无" : (jl.tdkcm +"(" + jl.tdkch + ")")) + "</td>\
										</tr>";
								}
								tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>学分：</td>\
											<td style='text-align: left;'>" + jl.xf + "</td>\
										</tr>";
										
								if(xkfs == "faxk" || xkfs == "xarxk" || xkfs == "xirxk" || xkfs == "zyxk") {
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>课程类别：</td>\
											<td style='text-align: left;'>" + jl.kclbmc + "</td>\
										</tr>";
								}
										
								if(xxbm == '100006') {
									tcont += "<tr>\
												<td style='text-align: right; width: 120px; padding: 5px 0 0;'>课程类别2：</td>\
												<td style='text-align: left;'>" + jl.kclbmc2 + "</td>\
											</tr>";
								}
								if(xkfs == "jhxk" || xkfs == "faxk" || xkfs == "xarxk" || xkfs == "xirxk" || xkfs == "cxxk" || xkfs == "fxxk") {
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>课程属性：</td>\
											<td style='text-align: left;'>" + jl.kcsxmc + "</td>\
										</tr>";
								}
								tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>考试类型：</td>\
											<td style='text-align: left;'>" + jl.kslxmc + "</td>\
										</tr>";
								if(xkfs == "faxk") {
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>授课语种：</td>\
											<td style='text-align: left;'>" + jl.skyzmc + "</td>\
										</tr>";
								}
								tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>教师：</td>\
											<td style='text-align: left;'>" + jl.skjs + "</td>\
										</tr>\
										<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>课余量/已选人数：</td>\
											<td style='text-align: left;'>" + bkskyl+"/<a title='点击查看' href='javascript:void(0);' \
											onclick='viewXkCount(\""+zxjxjhh+"\",\""+kch+"\",\""+kxh+"\", this);'>点击查看</a></td>\
										</tr>\
										<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>选课模式：</td>\
											<td style='text-align: left;'>" + jl.xkmssm + "</td>\
										</tr>\
										<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>选课控制：</td>\
											<td style='text-align: left;'>" + jl.xkkzsm + "</td>\
										</tr>\
										<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>选课限制说明：</td>\
											<td style='text-align: left;'>" + jl.xkxzsm + "</td>\
										</tr>\
										<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>允许跨校区选课：</td>\
											<td style='text-align: left;'>" + (jl.yxkxqxk=="1" ? "是" : "否") + "</td>\
										</tr>";
								if(xkfs == "faxk" || xkfs == "zyxk") {
									tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>备注：</td>\
											<td style='text-align: left;'>" + jl.xkbz + "</td>\
										</tr>";
								}
								tcont += "<tr>\
											<td style='text-align: right; width: 120px; padding: 5px 0 0;'>上课时间地点：</td>\
											<td style='text-align: left;' id='"+ kylKey +"_sjdd_detail'></td>\
										</tr>";
								tcont += "</tbody></table>";
								
								$("#courseList_div").append(tcont);
								
								tempkey = kylKey
							}
							let skxq = (jl.weekNum || jl.skxq);
							let skjc = (jl.courseStartNum || jl.skjc);
							let sjdd = (jl.zcsm=="" ? "无" : jl.zcsm) + " >> " + 
								(skxq=="" ? "无" : weekZw[parseInt(skxq)-1]) + " >> " + 
								(skjc=="" ? "无" : (skjc+(jl.cxjc=="" || jl.cxjc==null || jl.cxjc == "1" ? "" 
														:  "~"+(parseInt(jl.cxjc)+parseInt(skjc)-1)) + "节")) + "<br>" +
								(jl.xqm=="" ? "无" : jl.xqm) + " >> " + 
								(jl.jxlm=="" ? "无" : jl.jxlm) + " >> " + 
								(jl.jasm=="" ? "无" : jl.jasm);
								
							$("[id='" + tempkey + "_sjdd']").append($("[id='" + tempkey + "_sjdd']").html() == "" ? "" : "<br>")
								.append(sjdd);
							$("[id='" + tempkey + "_sjdd_detail']").append($("[id='" + tempkey + "_sjdd_detail']").html() == "" ? "" : "<br>")
								.append(sjdd);
							
							if(jl.zkxh != "") {
								let zhuketang = $("[id='"+ zxjxjhh+"_"+kch+"_"+jl.zkxh +"_tab']");
								if(zhuketang.length > 0) {
									zhuketang.remove();
								}
							}
						}
					}
				}
				
				if($("#courseList_div").html() == "") {
					$("#courseList_div").html("<i class='fa fa-folder-open bigger-300' style='color: #ffb752;'></i> 未查询到课程！");
				}
			}
			
			function showDetail(key) {
				let detail = $("." + key + "_detail");
				if(detail.length > 0) {
					layer.open({
						type: 1,
						shade: false,
						//title: false, //是否显示标题
						content: detail.prop("outerHTML"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相对元素所影响
						//cancel: function(){
						//}
					});
					$(".layui-layer-page").css({"left": "0", "top": "0", "height": "100%", "width": "100%"});
					$(".layui-layer-content").css({"overflow": "auto", "height": "calc(100vh - 50px)"});
					$(".layui-layer-content table").show();
				} else {
					urp.alert("无详情！");
				}
			}
			
			//提交选课
			function xuanze(){
				var idarr = new Array();
				var kcarr = new Array();
				for(var i=0; i<bxkc.length; i++){
					var qcjson = JSON.parse(bxkc[i]);
					let zxjxjhh = (qcjson.termCode || qcjson.zxjxjhh);
					let kch = (qcjson.courseNum || qcjson.kch);
					let kxh = (qcjson.classNum || qcjson.kxh);
					let cxfsdm = qcjson.cxfsdm;
					if(xkfs == "cxxk" && xxbm == "100008") {
						let zx = $("input[id^='"+ kch + "_" + kxh + "_" + zxjxjhh +"']");
						if(zx.length == 0) {
							urp.alert("列表中未查询到课程课堂：" + qcjson.kcm + "_" + kxh);
							return;
						}
						
						let sfzxcx = $(zx).parents(".phone-message-item").find("input[name^='sfzxcx_']:checked");
						if(sfzxcx.length == 0) {
							urp.alert(qcjson.kcm + "_" + kxh + "：未维护是否自学重修！");
							return;
						}
						cxfsdm = sfzxcx.val();
					}
					
					idarr[idarr.length] = kch + "_" + kxh + "_" + zxjxjhh + (xkfs=="cxxk" || xkfs=="fxxk" ? "_" +qcjson.tdkch+"_"+qcjson.cxfsdm : "");
					kcarr[kcarr.length] = qcjson.kcm + "_" + kxh + (qcjson.zkxh ? "（主课序号："+ qcjson.zkxh +"）" : "");
				}
				$("#kcIds").val(idarr.join(","));
				var xx = kcarr.join(",");
				var xxa = "";
				for(var i=0; i<xx.length; i++) {
					xxa += xx.charCodeAt(i) + ",";
				}
				$("#kcms").val(xxa);
				
				var kcids = $("#kcIds");
				if(!kcids || !$(kcids).val()){
					urp.alert("您还未选择课程！");
					return;
				}
				
				var inputCode = $("#submitCode").val();
				if("${xsyzm}" == "1" && inputCode == "") {
					urp.alert("请输入验证码！");
					$("#submitCode").focus();
					return;
				}
				
				var params = $("form[name='frm']").serialize();
				var index;
				$.ajax({
					url : "/student/courseSelect/selectCourse/checkInputCodeAndSubmit",
					type : "post",
					data : params + "&inputCode=" + inputCode + "&mobile=true&tokenValue=" + $("#tokenValue").val(),
					dataType : "json",
					beforeSend: function () {
		                index = layer.load(0, {
		                    shade: [0.2, "#000"] //0.1透明度的白色背景
		                });
		            },
					success : function (d) {
						if(d["result"].indexOf("/logout") != -1) {
							layer.alert('你的选课请求已经在浏览器的其他标签页提交，请刷新后再试！', {
								skin: 'layui-layer-molv',
								closeBtn: 0
							});
						} else {
							$("#tokenValue").val(d["token"]);
							
							if(d["result"] == "ok") {
								urp.alert("选课数据已提交，即将跳转至结果查询页面...", function() {
									$.find("form[name='frm']")[0].submit();
								});
							} else {
								urp.alert(d["result"]);
							}
						}
					},
					error: function (xhr) {
		                layer.close(index);
		                urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
		            },
		            complete: function () {
		            	$("#yzm_area img").attr("src", $("#yzm_area img").attr("src") + "?time=" + new Date().getMilliseconds());
		                layer.close(index);
		            }
				});
			}
			
			function dealHiddenData(atom, checkStatus) {
				if (checkStatus) {
					bxkc[bxkc.length] = atom.value ? atom.value : JSON.stringify(atom);
 				} else {
 					let zxjxjhh = (atom.termCode || atom.zxjxjhh);
					let kch = (atom.courseNum || atom.kch);
					let kxh = (atom.classNum || atom.kxh);
					for(var i=0; i<bxkc.length; i++) {
						var json = JSON.parse(bxkc[i]);
	 					let zxjxjhh_tmp = (json.termCode || json.zxjxjhh);
						let kch_tmp = (json.courseNum || json.kch);
						let kxh_tmp = (json.classNum || json.kxh);
						
						var atomIds = (atom.value ? atom.id : kch+"_"+kxh+"_"+zxjxjhh).split("_");
						if (kch_tmp == atomIds[0] 
							&& kxh_tmp == atomIds[1] 
							&& zxjxjhh_tmp == atomIds[2]) {
							bxkc.splice(i, 1);
							break;
						}
					}
				}
			}
			
			//勾选或取消选择处理
			function dealkc(atom){
			   	var checkStatus = atom.checked;
			   	dealHiddenData(atom, checkStatus);
			   	if((xkfs!="cxxk" && xkfs != "fxxk") || (xkfs=="cxxk" && "${byscxpdct}" != "1" && "${cxpctfs}" == "any") || (xkfs=="fxxk" && "${fxpct}" == "1")){
    				dealxtsjkc1();
			   	}
			}
           	
			//去除前台时间冲突校验
           	function dealxtsjkc1() {
       			var allkc = $("input[type=checkbox][name=kcId]");
       			$(allkc).prop("disabled",false);
       			$(allkc).prop("checked",false);
       			for(var i=0; i<bxkc.length; i++) {
       				var bxkcjson = JSON.parse(bxkc[i]);
       				let zxjxjhh = (bxkcjson.termCode || bxkcjson.zxjxjhh);
					let kch = (bxkcjson.courseNum || bxkcjson.kch);
					let kxh = (bxkcjson.classNum || bxkcjson.kxh);
					
       				var bxkcId = kch + "_" + kxh + "_" + zxjxjhh + (xkfs=="cxxk" || xkfs=="fxxk" ? "_" +bxkcjson.tdkch+"_"+bxkcjson.cxfsdm : "");
           			$("#" + bxkcId).prop("checked", true);
           			allkc = $(allkc).not("#" + bxkcId);
       			}
      			if(allkc!=undefined && allkc.length>0){
      				for(var i=0; i<allkc.length; i++){
      					var tempallkcdata = eval("("+allkc[i].value+")");
  						let kch = (tempallkcdata.courseNum || tempallkcdata.kch);
  						
     					var zkxh = tempallkcdata.zkxh;
     					if(zkxh == "" && $("#" + allkc[i].id + "_fsTab").length > 0) {
     						$(allkc[i]).prop("disabled", true);
      						$(allkc[i]).parent().attr("title", "选择附属课堂会连带选择主课堂");
     					}
       					if(bxkc!=undefined && bxkc.length>0){
           					nextkc:
           					for(var j=0; j<bxkc.length; j++){
           						var tempyxkcdata = JSON.parse(bxkc[j]);
           						let kch_tmp = (tempyxkcdata.courseNum || tempyxkcdata.kch);
           						
           						if(kch==kch_tmp 
           								|| (tempyxkcdata.zkch != "" && zkch == tempyxkcdata.zkch)){
           							$(allkc[i]).prop("disabled",true);
           							break nextkc;
           						}
           					}
           				}
           			}
           		}
           	}
			
			function changeKyl(obj){
				if($(obj).is(":checked")){
					$("#kyl").val(1);
				} else{
					$("#kyl").val(0);
				}
			}


			function viewXkCount(zxjxjhh, kch, kxh, obj){
				 $.ajax({
	                url: "/student/courseSelect/selectCourse/viewXkCount/"+zxjxjhh+"/"+kch+"/"+kxh,
	                cache: false,
	                type: "post",
	                data: "",
	                dataType: "json",
	                beforeSend: function () {
	                },
	                complete: function () {
	                },
	                success: function (d) {
		                $(obj).html(d);
		                $(obj).attr("title", "点击更新");
	                },
	                error: function (xhr) {
	                    urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
	                }
				});
			}
			
			function clearSelected() {
				$("input[type=checkbox][name=kcId]:checked").each((i, o) => {
					$(o).click();
				});
			}
			
			function changedtj(value){
				if(value!=""){
					$("#span-clear").css("display","inline-block");
				}else{
					$("#span-clear").css("display","none");
				}
			}
			
			function cleartj(obj){
				$("input[name=searchtj]")[0].value="";
				$(obj).css("display","none");
			}
			
			function tsxx(val, width) {
		        var mod = addslidersModel("right-menu", width, "right");
		        $(".modal-content").html("<div id='tsDiv' style=' border-bottom: 0; word-break: break-all;'>" +
		                "<embed src='/student/courseSelect/selectCourse/tip' width='100%' height='100%'></embed></div>");
		        $(".modal-content").append("<div style='border: 3px solid #6fb3e0; background: #6fb3e0; position: absolute; bottom: 0; width: 100%;' class='center'>\
					<label><input class='ace' type='checkbox' onclick='$(this).parent().siblings(\"button\").attr(\"disabled\", !this.checked);'>\
					<span class='lbl'>同意并遵守     </span></label>\
					<button class='btn btn-round btn-xs no-border' disabled id='tsxxClose' onclick='$(\"#right-menu\").modal(\"hide\");'>\
						<i class='fa fa-arrow-right'></i> 继续\
					</button>\
					</div>");
		        var wHeight = $(window).height();
		        $(".modal-content").css("top", "30px");
		        $(".modal-content").css("height", wHeight - 120);
		        $("embed").css("height", wHeight - 155);
		        mod.modal({
		            backdrop: 'static',
		            keyboard: false
		        }).on('hide.bs.modal', function () {
		            $("body").removeClass('modal-open');
		            $("body").css('padding-right', '0px');
		            mod.remove();
		            $('.modal-backdrop').remove();
		            return false;
		        });
		    }
		
		    function addslidersModel(id, width, placement) {
		        var modal = '<div id="' + id + '" class="modal right fade" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" tabindex="-1">\
		          <div class="modal-dialog">\
		              <div class="modal-content" style="border: 3px solid #6fb3e0;">\
		                  <div class="center">\
		                         <img src="/img/icon/pageloading.gif" style="width:28px;height:28px;"></img>\
		                     </div>\
		              </div>\
		          </div>\
		      </div>';
		        var modal = $(modal).appendTo('body');
		        $(".modal-dialog").css("width", width);
		        return modal;
		    }
		    
		    function closeModel() {
				$(".modal-backdrop").remove();
				$("#search-modal").css("display", "none");
			}
		</script>
		<style>
			body{
				background-color:white;
				margin:0;
				padding:0;
			}
			
			.div-sxl{
				position:absolute;
				padding:0;
				margin:-1px 0 0;
				width:150px;
				height:20px;
				background-color:#7DB4D8;
				right:0px;
    			border-radius: 0 0 0 20px;
				-webkit-border-radius: 0px 0 0 20px;
			}
			#myselectTable{
				background-color:white;
				border:1px solid #7DB4D8;
				margin:0;
				padding:0;
			}
			.value_ele{
				width:150px;
			}
			#courseList_table td, #courseList_table th {
				padding: 4px !important;
			}
			input[type=checkbox].ace+.lbl::before, input[type=radio].ace+.lbl::before {
				border-radius: 8px;
			}
		</style>
	</head>
	<body>
		<h5 class="phone-header smaller lighter grey" style="height: 38px; line-height: 38px;">
			<span class="ace-icon fa fa-angle-left bigger-130 phone-header-left" style="font-weight: bold; font-size: 20px !important; top: auto;" onclick="location.href = '/student/courseSelect/courseSelect/index?mobile=true'">&lt;</span>
			<span class="phone-header-center">选课</span>
			<c:if test="${xkfs != 'cxxk' && xkfs != 'fxxk'}">
				<span style="margin-right: 12px; top: 0; font-size: 20px; float: right;" onclick="$('#search-modal').addClass('in'); $('body').append('&lt;div class=&quot;modal-backdrop fade in&quot;&gt;&lt;/div&gt;'); $('#search-modal').css('display', 'block'); centerModals();">🔍</span>
			</c:if>
		</h5>
		<div id="search-modal" class="modal fade" tabindex="-1">
	        <div class="modal-dialog" style="width: 90%;">
	            <div class="modal-content">
	                <div class="modal-header no-padding">
	                    <div class="table-header">
	                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true" onclick="closeModel();">
	                            <span class="white">×</span>
	                        </button>
							查询条件
	                    </div>
	                </div>
	                <div class="modal-body">
	                    <div class="row" style="margin-top: 6px;">
	                        <div class="col-sm-12 self-margin">
	                            <form name="query_info_form" id="query_info_form">
	                            	<c:if test="${xkfs == 'jhxk'}">
		                                <div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">任务面向班级</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <label>
										            <input type="checkbox" class="ace" checked id="filterClassCourses" style="top: 12px; position: relative;"/>
										            <span class="lbl"></span>
										        </label>
		                                    </label>
		                                </div>
									</c:if>
									<c:if test="${xkfs == 'faxk'}">
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">计划学年学期</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <select id='jhxn' class="phone-control-select">
													<option value="">全部</option>
												 	<c:forEach var="xnxq" items="${xnxqlist}">
											 			<option value="${xnxq.id.zxjxjhh}" 
											 				<c:if test="${xnxq.id.zxjxjhh == zxjxjhh}"> selected </c:if>
											 			>${xnxq.zxjxjhm}</option>
												 	</c:forEach>
												</select>
		                                    </label>
		                                </div>
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">课程属性</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <select id='kcsxdm' class="phone-control-select">
													<option value="">全部</option>
												 	<c:forEach var="kcsx" items="${kcsxlist}">
												 		<option value="${kcsx.kcsxdm}" 
												 			<c:if test='${kcsxdm==kcsx.kcsxdm}'>selected</c:if>>${kcsx.kcsxmc}</option>
												 	</c:forEach>
												</select>
		                                    </label>
		                                </div>
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">课程号</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                    	<input type="text" id="kch" class="phone-control-input"/>
		                                    </label>
		                                </div>
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">课程名</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                    	<input type="text" id="kcm" class="phone-control-input"/>
		                                    </label>
		                                </div>
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">课序号</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                    	<input type="text" id="kxh" class="phone-control-input"/>
		                                    </label>
		                                </div>
		                                <div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">课程类别</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <select id='kclbdm' name="kclbdm" class="phone-control-select">
													<option value="">全部</option>
												 	<cache:query var="kclbs" region="CODE_KCLB" orderby="KCLBMC"/>
													<c:forEach items="${kclbs}" var="kclb">
														<option value="${kclb.kclbdm}">${kclb.kclbmc}</option>
													</c:forEach>
												</select>
		                                    </label>
		                                </div>
		                                <div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">课组名称</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <select id='kzh' name="kzh" class="phone-control-select">
													<option value="">全部</option>
												 	<c:forEach items="${kzList}" var="kz">
														<option value="${kz[0]}">${kz[1]}</option>
													</c:forEach>
												</select>
		                                    </label>
		                                </div>
		                                <div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">上课校区</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <select id='xqh' name="xqh" class="phone-control-select">
													<option value="">全部</option>
												 	<cache:query var="xqs" region="CODE_xaqB" orderby="xqh"/>
													<c:forEach items="${xqs}" var="xq">
														<option value="${xq.xqh}">${xq.xqm}</option>
													</c:forEach>
												</select>
		                                    </label>
		                                </div>
		                                <div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">有课余量</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <input type="hidden" id='kyl' value="1"/>
												<label>
													<input checked type="checkbox" class="ace" onclick='changeKyl(this);' style="top: 12px; position: relative;"/>
													<span class="lbl"></span>
												</label>
		                                    </label>
		                                </div>
									</c:if>
									<c:if test="${xkfs == 'xirxk' || xkfs == 'xarxk'}">
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">模糊匹配</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <input type="text" name='searchtj' placeholder="输入课程号、课程名或教师名查询" onkeyup="changedtj(this.value);"
													id="searchtj" autocomplete="off" class="phone-control-input">
		                                    </label>
		                                </div>
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">课程类别</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <select id='kclbdm' name="kclbdm" class="phone-control-select">
													<option value="">全部</option>
												 	<cache:query var="kclbs" region="CODE_KCLB" orderby="KCLBMC"/>
													<c:forEach items="${kclbs}" var="kclb">
														<option value="${kclb.kclbdm}">${kclb.kclbmc}</option>
													</c:forEach>
												</select>
		                                    </label>
		                                </div>
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">有课余量</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <input type="hidden" id='kyl' value="1"/>
												<label>
													<input checked type="checkbox" class="ace" onclick='changeKyl(this);' style="top: 12px; position: relative;"/>
													<span class="lbl"></span>
												</label>
		                                    </label>
		                                </div>
		                                <c:if test="${xxbm == '100018' && xkfs == 'xarxk'}">
		                                	<div class="form-group">
			                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">课组名称</label>
			                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
			                                        <select id='kzmc' name="kzmc" class="phone-control-select">
														<option value="">全部</option>
														<option value="自然与人类">自然与人类</option>
														<option value="经济与社会">经济与社会</option>
														<option value="思想政治">思想政治</option>
														<option value="心理与健康">心理与健康</option>
														<option value="创新与创业">创新与创业</option>
														<option value="工程与科技">工程与科技</option>
														<option value="公共艺术">公共艺术</option>
														<option value="文化与哲学">文化与哲学</option>
													</select>
			                                    </label>
			                                </div>
		                                </c:if>
									</c:if>
									<c:if test="${xkfs == 'zyxk'}">
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">开课院系</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <select id='kkxsh' name="kkxsh" class="phone-control-select">
													<option value="">全部</option>
												 	<cache:query var="xsbs" region="code_xsb_jxdw" orderby="xsh"/>
													<c:forEach items="${xsbs}" var="xsb">
														<option value="${xsb.xsh}">${xsb.xsm}</option>
													</c:forEach>
												</select>
		                                    </label>
		                                </div>
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">课程号</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                    	<input type="text" id="kch" class="phone-control-input"/>
		                                    </label>
		                                </div>
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">课程名</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                    	<input type="text" id="kcm" class="phone-control-input"/>
		                                    </label>
		                                </div>
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">课程类别</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <select id='kclbdm' name="kclbdm" class="phone-control-select">
													<option value="">全部</option>
												 	<cache:query var="kclbs" region="CODE_KCLB" orderby="KCLBMC"/>
													<c:forEach items="${kclbs}" var="kclb">
														<option value="${kclb.kclbdm}">${kclb.kclbmc}</option>
													</c:forEach>
												</select>
		                                    </label>
		                                </div>
		                                <div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">上课教师</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                    	<input type="text" id="skjs" class="phone-control-input"/>
		                                    </label>
		                                </div>
										<div class="form-group">
		                                    <label class="control-label col-sm-3 no-padding-right" style="width: 30%;">有课余量</label>
		                                    <label class="control-label col-sm-9 no-padding-right" style="width: 66%;; padding-left: 10px;">
		                                        <input type="hidden" id='kyl' value="1"/>
												<label>
													<input checked type="checkbox" class="ace" onclick='changeKyl(this);' style="top: 12px; position: relative;"/>
													<span class="lbl"></span>
												</label>
		                                    </label>
		                                </div>
									</c:if>
	                            </form>
	                            <div style="clear: both; position: relative; top: 5px;">
	                                <div style="width: calc(45vw - 18px); display: inline-block; text-align: center;">
	                                    <button class="btn btn-xs btn-default btn-block" style="border-radius: 15px; width: 100%;" onclick="closeModel();"><i class="ace-icon fa fa-times"></i> ✕关闭</button>
	                                </div>
	                                <div style="width: calc(45vw - 18px); display: inline-block; text-align: center;">
	                                    <button class="btn btn-xs btn-info btn-block" style="border-radius: 15px; width: 100%;" onclick="guolv();closeModel();return false;"><i class="glyphicon glyphicon-search"></i> 查询</button>
	                                </div>
	                            </div>
	                        </div>
	                    </div>
	                </div>
	            </div>
	        </div>
	    </div>
		<div style="padding: 0 12px;">
			<input type="hidden" id="tokenValue" value="${token_in_session}"/>
			<div class="alert alert-block alert-success" style="margin-bottom: 0;">
				<i class="ace-icon fa fa-exclamation-triangle red"></i>
				<span id="xkfs_title"></span>选课 (${zxjxjh.yearName}${zxjxjh.semesterName}) <br>&lt;<font color="blue">${fajh.famc}</font> <font color="red">${fajh.xdlxmc}</font>&gt;
			</div>
			<div class="col-xs-12" id="div_kc_tj" style="padding:0;">
				<form action="/student/courseSelect/selectCourses/waitingfor" name="frm" method="POST">
					<input type="hidden" name="dealType" value="${xkfs=='jhxk' ? '1' : (xkfs=='faxk' ? '2' : (xkfs=='xarxk' ? '3' : (xkfs=='xirxk' ? '4' : (xkfs=='zyxk' ? '5' : (xkfs=='cxxk' ? '6' : (xkfs=='fxxk' ? '7' : ''))))))}">
					<input type="hidden" name="mobile" value="true">
					<input type="hidden" name="fajhh" value="${fajhh}">
					<input type="hidden" name="kcIds" id="kcIds" value="">
					<input type="hidden" name="kcms" id="kcms" value="">
					<input type="hidden" name="sj" id="sj" value="0_0">
				</form>
				
				<div class="center" style="padding: 5px 0; text-align: right;">
					<button type="button" class="btn btn-gray btn-xs btn-round" onclick="clearSelected();">
						 <i class="fa fa-undo"></i> 重选
					</button>
					<c:if test="${xsyzm == '1'}">
						<div id="yzm_area" style="display: inline-block;">
							<img title="看不清，换一张" alt="yzm" src="/student/courseSelect/selectCourse/getYzmPic" 
								style="width: 60px; height: 25px; margin-top: -3px; cursor: pointer;" 
								onclick="$(this).attr('src', $(this).attr('src') + '?time=' + new Date().getMilliseconds)">
							<input id="submitCode" type="text" style="width: 50px; height: 25px; vertical-align: bottom;"/>
						</div>
					</c:if>
					<button type="button" class="btn btn-purple btn-xs btn-round" onclick="xuanze();">
						 <i class="fa fa-check"></i> 提交
					</button>
				</div>
				
				<div id="courseList_div" style="overflow: auto; height: calc(100vh - 160px); border-top: 1px dashed #afafaf;">
				</div>
			</div>
		</div>
	</body>
</html>
