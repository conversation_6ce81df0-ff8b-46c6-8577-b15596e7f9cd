<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>提示</title>
<style type="text/css">
	.alert {
	    font-size: 14px;
	}
	
	.alert, .well {
	    border-radius: 0;
	}
	.alert-danger {
	    background-color: #f2dede;
	    border-color: #ebccd1;
	    color: #a94442;
	}
	.alert {
	    padding: 15px;
	    border: 1px solid transparent;
	    border-radius: 4px;
	}
	.alert, .thumbnail {
	    margin-bottom: 20px;
	}
	
	.alert .close {
	    font-size: 16px;
	}
	
	button.close {
	    padding: 0;
	    cursor: pointer;
	    background: 0 0;
	    border: 0;
	    -webkit-appearance: none;
	}
	.close {
	    float: right;
	    font-size: 21px;
	    color: #000;
	    text-shadow: 0 1px 0 #fff;
	    opacity: .2;
	    filter: alpha(opacity=20);
	}
	.alert .alert-link, .close {
	    font-weight: 700;
	}
	.badge, .close, .label {
	    line-height: 1;
	}
</style>
<script src="/js/jQuery/jquery-3.4.1.min.js"></script>
<c:if test="${xxbm == '100027'}">
	<script type="text/javascript" src="/assets/layer/layer.js"></script>
</c:if>
<c:if test="${mobile}">
	<link rel="stylesheet" href="/css/phone/phone.css" type="text/css"></link>
</c:if>
<script type="text/javascript">
	<c:if test="${xxbm =='100027'}">
		$(function () {
			queryArrears();
		});
		function queryArrears(){
			$.ajax({
				url: "/main/queryArrears",
				type: "post",
				dataType: "json",
				success: function (data) {
					if(data["count"] != 0){
						var rad = "";
						if('${xxbm}'=='100027'){
							var info = "<div style='max-height:300px; overflow: auto;'>";
							var je = 0;
							$.each(data["list"],function(i,v){
								info += "<p>收费项目名称：" + v.sfxmmc + ",欠费金额："+v.qfje+"元</p>";
								je += parseFloat(v.qfje);
							});
							info +="<p style='color:red;'>无法正常使用选课功能</p>";
							info += "</div>";
							rad = "欠费金额：" + je +"元，请及时缴费,详细信息如下:<br>" + info;
						}
						layer.confirm(rad, {
							title: '欠费情况提示',
							btn: ['确定'] //按钮
						},function (index, layero){
							layer.close(index);
						});
					}
				},
				error: function () {
					layer.msg("获取欠费信息失败！");
				}
			});
		}
	</c:if>
</script>
</head>
<body>
	<c:if test="${mobile}">
		<h5 class="phone-header smaller lighter grey" style="height: 38px; line-height: 38px; margin: -10px; top: -10px; position: relative;">
			<span class="ace-icon fa fa-angle-left bigger-130 phone-header-left" style="font-weight: bold; font-size: 20px !important; top: auto;" onclick="location.href = '/student/courseSelect/courseSelect/index?mobile=true'">&lt;</span>
			<span class="phone-header-center">选课</span>
		</h5>
	</c:if>
	<div class="alert alert-block alert-danger">
		<button type="button" class="close" data-dismiss="alert" onclick="$(this).parent().remove(); $('#view-table').modal('hide');;return false;">
			<i class="ace-icon fa fa-times"></i> ✕
		</button>
		<i class="ace-icon fa fa-exclamation-triangle red"></i>
		<c:out value="${err}" />
	</div>
</body>
</html>
