<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<!DOCTYPE html>
<html>
<head>
    <title id="pageTitle">选课</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="/wapjsp/css/mobile-framework.css">
    <link rel="stylesheet" href="/wapjsp/css/font-awesome.min.css">
    <script src="/wapjsp/js/jquery.min.js"></script>
    <script src="/wapjsp/js/layer.js"></script>
    <script src="/wapjsp/js/urp.js"></script>
    <script src="/js/json/json2.js"></script>
    <style>
        .search-panel {
            background: var(--card-background);
            border-radius: 12px;
            margin-bottom: 16px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
        }

        .search-header {
            background: var(--primary-color);
            color: white;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .search-title {
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .collapse-icon {
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .collapse-icon.collapsed {
            transform: rotate(180deg);
        }

        .search-content {
            padding: 16px;
            display: none;
        }

        .search-content.show {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .form-group-full {
            grid-column: 1 / -1;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 6px;
        }

        .form-input, .form-select {
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            background: var(--background-primary);
            color: var(--text-primary);
            min-height: 44px;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }

        .search-actions {
            display: flex;
            gap: 8px;
            justify-content: center;
            padding-top: 16px;
            border-top: 1px solid var(--border-color);
        }

        .btn-search {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            min-height: 44px;
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            justify-content: center;
        }

        .course-card {
            background: var(--card-background);
            border-radius: 12px;
            margin-bottom: 12px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .course-card.selected {
            border-color: var(--primary-color);
            box-shadow: 0 4px 20px rgba(24, 144, 255, 0.2);
        }

        .course-header {
            background: var(--background-secondary);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .course-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--background-primary);
            transition: all 0.3s ease;
        }

        .course-checkbox.checked {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .course-title {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .course-code {
            font-size: 12px;
            color: var(--text-secondary);
            background: var(--background-primary);
            padding: 4px 8px;
            border-radius: 6px;
        }

        .course-body {
            padding: 16px;
        }

        .course-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 12px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            font-size: 14px;
        }

        .info-label {
            color: var(--text-secondary);
            font-size: 12px;
            margin-bottom: 2px;
        }

        .info-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        .course-schedule {
            background: var(--background-secondary);
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
        }

        .schedule-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .schedule-item {
            font-size: 13px;
            color: var(--text-secondary);
            line-height: 1.4;
            margin-bottom: 4px;
        }

        .course-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .btn-detail {
            background: var(--info-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            flex: 1;
        }

        .floating-actions {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: var(--card-background);
            border-radius: 12px;
            padding: 12px;
            box-shadow: var(--card-shadow-hover);
            display: none;
            z-index: 100;
        }

        .floating-actions.show {
            display: block;
        }

        .action-row {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .selected-count {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
            flex: 1;
        }

        .btn-clear {
            background: var(--background-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
        }

        .btn-submit {
            background: linear-gradient(135deg, var(--success-color), var(--success-light));
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .verification-row {
            display: flex;
            gap: 8px;
            align-items: center;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid var(--border-color);
        }

        .captcha-img {
            width: 60px;
            height: 30px;
            border-radius: 4px;
            cursor: pointer;
        }

        .captcha-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 8px;
            height: 120px;
            margin-bottom: 12px;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .course-availability {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .availability-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .available {
            background: var(--success-color);
        }

        .full {
            background: var(--error-color);
        }

        .limited {
            background: var(--warning-color);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title" id="navTitle">选课</div>
            <div class="navbar-action" onclick="refreshCourses();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-mobile">
            <!-- 课程信息标题 -->
            <div class="section-header-mobile">
                <h3 class="section-title-mobile" id="courseTypeTitle">
                    <i class="fa fa-check-square-o"></i>
                    <span id="xkfs_title"></span>选课
                </h3>
                <div class="course-plan-info">
                    <span class="plan-semester">(${zxjxjh.yearName}${zxjxjh.semesterName})</span>
                    <div class="plan-details">
                        &lt;<span class="plan-name">${fajh.famc}</span>
                        <span class="plan-type">${fajh.xdlxmc}</span>&gt;
                    </div>
                </div>
            </div>

            <!-- 搜索面板 -->
            <div class="search-panel" id="searchPanel">
                <div class="search-header" onclick="toggleSearchPanel()">
                    <div class="search-title">
                        <i class="fa fa-search"></i>
                        搜索条件
                    </div>
                    <i class="fa fa-angle-down collapse-icon" id="collapseIcon"></i>
                </div>

                <div class="search-content" id="searchContent">
                    <!-- 不同选课方式的搜索表单 -->
                    <c:if test="${xkfs == 'faxk'}">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">计划学年学期</label>
                                <select class="form-select" id="jhxn">
                                    <option value="">全部</option>
                                    <c:forEach var="xnxq" items="${xnxqlist}">
                                        <option value="${xnxq.id.zxjxjhh}"
                                            <c:if test="${xnxq.id.zxjxjhh == zxjxjhh}">selected</c:if>>
                                            ${xnxq.zxjxjhm}
                                        </option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">课程属性</label>
                                <select class="form-select" id="kcsxdm">
                                    <option value="">全部</option>
                                    <c:forEach var="kcsx" items="${kcsxlist}">
                                        <option value="${kcsx.kcsxdm}"
                                            <c:if test='${kcsxdm==kcsx.kcsxdm}'>selected</c:if>>
                                            ${kcsx.kcsxmc}
                                        </option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">课程号</label>
                                <input type="text" class="form-input" id="kch" placeholder="请输入课程号"/>
                            </div>
                            <div class="form-group">
                                <label class="form-label">课程名</label>
                                <input type="text" class="form-input" id="kcm" placeholder="请输入课程名"/>
                            </div>
                            <div class="form-group">
                                <label class="form-label">课序号</label>
                                <input type="text" class="form-input" id="kxh" placeholder="请输入课序号"/>
                            </div>
                            <div class="form-group">
                                <label class="form-label">课程类别</label>
                                <select class="form-select" id="kclbdm">
                                    <option value="">全部</option>
                                    <cache:query var="kclbs" region="CODE_KCLB" orderby="KCLBMC"/>
                                    <c:forEach items="${kclbs}" var="kclb">
                                        <option value="${kclb.kclbdm}">${kclb.kclbmc}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                    </c:if>

                    <c:if test="${xkfs == 'xirxk' || xkfs == 'xarxk'}">
                        <div class="form-grid">
                            <div class="form-group form-group-full">
                                <label class="form-label">模糊匹配</label>
                                <input type="text" class="form-input" name="searchtj" id="searchtj"
                                       placeholder="输入课程号、课程名或教师名查询"/>
                            </div>
                            <div class="form-group">
                                <label class="form-label">课程类别</label>
                                <select class="form-select" id="kclbdm">
                                    <option value="">全部</option>
                                    <cache:query var="kclbs" region="CODE_KCLB" orderby="KCLBMC"/>
                                    <c:forEach items="${kclbs}" var="kclb">
                                        <option value="${kclb.kclbdm}">${kclb.kclbmc}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                    </c:if>

                    <c:if test="${xkfs == 'zyxk'}">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">开课院系</label>
                                <select class="form-select" id="kkxsh">
                                    <option value="">全部</option>
                                    <cache:query var="xsbs" region="code_xsb_jxdw" orderby="xsh"/>
                                    <c:forEach items="${xsbs}" var="xsb">
                                        <option value="${xsb.xsh}">${xsb.xsm}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">课程号</label>
                                <input type="text" class="form-input" id="kch" placeholder="请输入课程号"/>
                            </div>
                            <div class="form-group">
                                <label class="form-label">课程名</label>
                                <input type="text" class="form-input" id="kcm" placeholder="请输入课程名"/>
                            </div>
                            <div class="form-group">
                                <label class="form-label">上课教师</label>
                                <input type="text" class="form-input" id="skjs" placeholder="请输入教师姓名"/>
                            </div>
                        </div>
                    </c:if>

                    <!-- 通用选项 -->
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="kylFilter" checked onchange="changeKyl(this)">
                            只显示有课余量的课程
                        </label>
                        <input type="hidden" id="kyl" value="1"/>
                    </div>

                    <div class="search-actions">
                        <button type="button" class="btn-search" onclick="searchCourses()">
                            <i class="fa fa-search"></i>
                            查询课程
                        </button>
                    </div>
                </div>
            </div>

            <!-- 课程列表 -->
            <div class="section-mobile">
                <div id="courseListContainer">
                    <!-- 加载中状态 -->
                    <div id="loadingState" class="loading-container">
                        <div class="loading-skeleton"></div>
                        <div class="loading-skeleton"></div>
                        <div class="loading-skeleton"></div>
                    </div>

                    <!-- 课程列表 -->
                    <div id="courseList"></div>

                    <!-- 空状态 -->
                    <div id="emptyState" class="empty-state" style="display: none;">
                        <div class="empty-icon">
                            <i class="fa fa-folder-open"></i>
                        </div>
                        <p>未查询到课程</p>
                        <small>请尝试调整搜索条件</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 浮动操作栏 -->
        <div class="floating-actions" id="floatingActions">
            <div class="action-row">
                <div class="selected-count" id="selectedCount">已选择 0 门课程</div>
                <button type="button" class="btn-clear" onclick="clearSelected()">
                    <i class="fa fa-undo"></i> 重选
                </button>
                <button type="button" class="btn-submit" onclick="submitSelection()">
                    <i class="fa fa-check"></i> 提交
                </button>
            </div>

            <!-- 验证码行 -->
            <c:if test="${xsyzm == '1'}">
                <div class="verification-row">
                    <img class="captcha-img" id="captchaImg"
                         src="/student/courseSelect/selectCourse/getYzmPic"
                         onclick="refreshCaptcha()"
                         title="点击刷新验证码"/>
                    <input type="text" class="captcha-input" id="submitCode"
                           placeholder="请输入验证码" maxlength="4"/>
                </div>
            </c:if>
        </div>
    </div>

    <!-- 隐藏表单 -->
    <form action="/student/courseSelect/selectCourses/waitingfor" name="frm" method="POST" target="_parent" style="display: none;">
        <input type="hidden" name="dealType" value="${xkfs=='jhxk' ? '1' : (xkfs=='faxk' ? '2' : (xkfs=='xarxk' ? '3' : (xkfs=='xirxk' ? '4' : (xkfs=='zyxk' ? '5' : (xkfs=='cxxk' ? '6' : (xkfs=='fxxk' ? '7' : ''))))))}">
        <input type="hidden" name="mobile" value="true">
        <input type="hidden" name="fajhh" value="${fajhh}">
        <input type="hidden" name="kcIds" id="kcIds" value="">
        <input type="hidden" name="kcms" id="kcms" value="">
        <input type="hidden" name="sj" id="sj" value="0_0">
        <input type="hidden" id="tokenValue" value="${token_in_session}"/>
    </form>

    <script>
        // 全局变量
        let yxkc = "";
        let yxkch = "";
        let bxkc = [];
        let weekZw = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"];
        let xxbm = "${xxbm}";
        let xkfs = "${xkfs}";
        let xkfsm = xkfs=="jhxk" ? "计划" : (xkfs=="faxk" ? "方案" : (xkfs=="xarxk" ? "校任" : (xkfs=="xirxk" ? "系任" : (xkfs=="zyxk" ? "自由" : (xkfs=="cxxk" ? "重修" : (xkfs=="fxxk" ? "复修" : ""))))));
        let xkjdlx = "${xkjdlx}";
        let courseData = [];
        let isLoading = false;

        // 页面加载完成后初始化
        $(document).ready(function() {
            initializePage();
            bindEvents();
            loadCourses();
        });

        // 初始化页面
        function initializePage() {
            // 设置页面标题
            $("#pageTitle").text(xkfsm + "选课");
            $("#navTitle").text(xkfsm + "选课");
            $("#xkfs_title").text(xkfsm);

            // 根据选课方式显示/隐藏搜索面板
            if (xkfs == "jhxk" || xkfs == "cxxk" || xkfs == "fxxk") {
                $("#searchPanel").hide();
            }

            // 特殊学校处理
            if (xxbm == "100010" && xkjdlx == "005") {
                if (xkfs == "faxk") {
                    $("#jhxn").val("");
                }
            }
        }

        // 绑定事件
        function bindEvents() {
            // 搜索输入框回车事件
            $('.form-input').on('keypress', function(e) {
                if (e.which === 13) {
                    searchCourses();
                }
            });

            // 课程卡片点击事件
            $(document).on('click', '.course-card', function(e) {
                if (!$(e.target).closest('.btn-detail').length) {
                    toggleCourseSelection(this);
                }
            });
        }

        // 切换搜索面板
        function toggleSearchPanel() {
            const content = $('#searchContent');
            const icon = $('#collapseIcon');

            if (content.hasClass('show')) {
                content.removeClass('show');
                icon.addClass('collapsed');
            } else {
                content.addClass('show');
                icon.removeClass('collapsed');
            }
        }

        // 搜索课程
        function searchCourses() {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            loadCourses();
        }

        // 刷新课程
        function refreshCourses() {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            loadCourses();
        }

        // 加载课程列表
        function loadCourses() {
            if (isLoading) return;

            isLoading = true;
            showLoading();

            // 构建请求URL
            let url = getApiUrl();

            // 构建请求数据
            let requestData = buildRequestData();

            $.ajax({
                url: url,
                method: "post",
                data: requestData,
                beforeSend: function() {
                    showLoading();
                },
                success: function(data) {
                    isLoading = false;
                    hideLoading();

                    yxkc = eval("(" + data["yxkclist"] + ")");
                    yxkch = data["kchlist"];
                    renderCourseList(data);
                },
                error: function() {
                    isLoading = false;
                    hideLoading();
                    showError("查询课程失败，请稍后再试...");
                }
            });
        }

        // 获取API URL
        function getApiUrl() {
            switch(xkfs) {
                case "jhxk": return "/student/courseSelect/intentCourse/courseList";
                case "faxk": return "/student/courseSelect/planCourse/courseList";
                case "xarxk": return "/student/courseSelect/schoolCourse/courseList";
                case "xirxk": return "/student/courseSelect/departCourse/courseList";
                case "zyxk": return "/student/courseSelect/freeCourse/courseList";
                case "cxxk": return "/student/courseSelect/relearnCourse/courseList";
                case "fxxk": return "/student/courseSelect/reViewCourse/courseList";
                default: return "";
            }
        }

        // 构建请求数据
        function buildRequestData() {
            let data = {
                mobile: "true",
                fajhh: "${fajhh}"
            };

            // 根据不同选课方式添加参数
            if (xkfs == "jhxk") {
                data.mxbj = $("#filterClassCourses").length > 0 && $("#filterClassCourses").is(":checked") ? "1" : "0";
            }

            if (xkfs == "faxk") {
                data.jhxn = $("#jhxn").val();
                data.kcsxdm = $("#kcsxdm").val();
                data.kch = $("#kch").val();
                data.kcm = $("#kcm").val();
                data.kxh = $("#kxh").val();
                data.kclbdm = $("#kclbdm").val();
                data.kzh = $("#kzh").val();
                data.xqh = $("#xqh").val();
            }

            if (xkfs == "faxk" || xkfs == "xirxk" || xkfs == "xarxk" || xkfs == "zyxk") {
                data.xq = "0";
                data.jc = "0";
            }

            if (xkfs == "xirxk" || xkfs == "xarxk") {
                data.searchtj = $("#searchtj").val();
                data.kclbdm = $("#kclbdm").val();
            }

            if (xkfs == "zyxk") {
                data.kkxsh = $("#kkxsh").val();
                data.kch = $("#kch").val();
                data.kcm = $("#kcm").val();
                data.skjs = $("#skjs").val();
                data.kclbdm = $("#kclbdm").val();
            }

            return data;
        }
    </script>
</body>
</html>