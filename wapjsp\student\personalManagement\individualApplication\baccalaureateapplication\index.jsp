<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学士学位申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学士学位申请页面样式 */
        .degree-header {
            background: linear-gradient(135deg, var(--primary-color), var(--warning-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .degree-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .degree-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .action-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-add-application {
            width: 100%;
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-add-application:hover {
            background: var(--success-dark);
        }
        
        .btn-add-application:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .applications-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .applications-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .applications-section-title i {
            color: var(--warning-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-index {
            background: var(--warning-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .application-content {
            flex: 1;
        }
        
        .application-number {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .application-student {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            align-items: flex-start;
        }
        
        .detail-label {
            font-weight: 500;
            min-width: 60px;
            margin-right: var(--margin-sm);
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
            word-break: break-word;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-draft {
            background: var(--text-disabled);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-processing {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-completed {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-revoked {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .approval-result {
            margin-top: var(--margin-sm);
            padding: var(--padding-sm);
            border-radius: 6px;
            font-size: var(--font-size-small);
            text-align: center;
        }
        
        .approval-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .approval-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .approval-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .application-reason {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .application-note {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .signature-image {
            max-width: 100px;
            max-height: 40px;
            border-radius: 4px;
            border: 1px solid var(--divider-color);
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-application-action {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-revoke {
            background: var(--error-color);
            color: white;
        }
        
        .btn-print {
            background: var(--success-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .warning-alert {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .warning-alert i {
            color: var(--warning-color);
            font-size: 20px;
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .degree-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .action-section,
            .applications-section,
            .warning-alert {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .application-details {
                grid-template-columns: 1fr;
            }
            
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .detail-value {
                text-align: left;
                margin-top: 4px;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学士学位申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 警告信息 -->
        <c:if test="${count == 0}">
            <div class="warning-alert">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>学士学位申请无审批流程，请联系管理员！</span>
            </div>
        </c:if>
        
        <c:if test="${count != 0}">
            <!-- 学士学位申请头部 -->
            <div class="degree-header">
                <div class="degree-title">学士学位申请</div>
                <div class="degree-desc">管理您的学士学位申请记录</div>
            </div>
            
            <!-- 添加申请按钮 -->
            <c:if test="${flag == 'showAdd'}">
                <div class="action-section">
                    <button class="btn-add-application" onclick="addApply();">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>填写学士学位申请</span>
                    </button>
                </div>
            </c:if>
            
            <!-- 申请列表 -->
            <div class="applications-section">
                <div class="applications-section-header">
                    <div class="applications-section-title">
                        <i class="ace-icon fa fa-list"></i>
                        学士学位申请列表
                    </div>
                </div>
                
                <div id="applicationsList">
                    <!-- 动态加载申请列表 -->
                </div>
                
                <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                    <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreApplications();">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>加载更多</span>
                    </button>
                </div>
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-graduation-cap"></i>
                <div class="empty-state-title">暂无申请记录</div>
                <div class="empty-state-desc">您还没有提交任何学士学位申请</div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 申请须知模态框 -->
    <div class="notice-modal" id="noticeModal" style="display: none;">
        <div class="notice-modal-content">
            <div class="notice-modal-header">
                <div class="notice-modal-title">申请须知</div>
                <button class="btn-close" onclick="closeNoticeModal();">
                    <i class="ace-icon fa fa-times"></i>
                </button>
            </div>
            <div class="notice-modal-body" id="noticeContent">
                <!-- 动态加载须知内容 -->
            </div>
            <div class="notice-modal-footer">
                <button class="btn-continue" id="btnContinue" onclick="continueApplication();" disabled>
                    <i class="ace-icon fa fa-arrow-right"></i>
                    <span>继续</span>
                    <span id="countdown"></span>
                </button>
                <button class="btn-cancel" onclick="closeNoticeModal();">
                    <i class="ace-icon fa fa-times"></i>
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 打印预览模态框 -->
    <div class="print-modal" id="printModal" style="display: none;">
        <div class="print-modal-content">
            <div class="print-modal-header">
                <div class="print-modal-title">申请单预览</div>
                <button class="btn-close" onclick="closePrintModal();">
                    <i class="ace-icon fa fa-times"></i>
                </button>
            </div>
            <div class="print-modal-body">
                <div id="printContent">
                    <!-- 动态加载打印内容 -->
                </div>
            </div>
        </div>
    </div>

    <style>
        /* 模态框样式 */
        .notice-modal,
        .print-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow-y: auto;
        }

        .notice-modal-content,
        .print-modal-content {
            background: var(--bg-primary);
            margin: 20px;
            border-radius: 8px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        .notice-modal-header,
        .print-modal-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1001;
        }

        .notice-modal-title,
        .print-modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }

        .btn-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
        }

        .notice-modal-body {
            padding: var(--padding-md);
            line-height: 1.6;
            color: var(--text-primary);
        }

        .notice-modal-footer {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .btn-continue {
            background: var(--info-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-md);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-continue:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }

        .btn-cancel {
            background: var(--text-disabled);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-md);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
        }

        .print-modal-body {
            padding: 0;
        }

        .print-embed {
            width: 100%;
            height: 70vh;
            border: none;
            border-radius: 0 0 8px 8px;
        }

        @media (max-width: 480px) {
            .notice-modal-content,
            .print-modal-content {
                margin: 10px;
            }

            .print-embed {
                height: 60vh;
            }
        }
    </style>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let applicationData = [];
        let countdownTimer = null;

        $(function() {
            initPage();
            loadApplications(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 判断字符是否为空
        function isEmpty(obj) {
            return typeof obj == "undefined" || obj == null || obj == "";
        }

        // 加载申请列表
        function loadApplications(page, conditionChanged) {
            if (conditionChanged) {
                currentPage = 1;
            }

            showLoading(true);

            const url = "/student/personalManagement/individualApplication/baccalaureateapplication/query/page";

            $.ajax({
                url: url,
                cache: false,
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records) {
                        applicationData = data.records;
                        totalCount = data.pageContext ? data.pageContext.totalCount : 0;

                        if (applicationData.length > 0) {
                            renderApplications();
                        } else {
                            showEmptyState();
                        }
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplications() {
            const container = $('#applicationsList');
            container.empty();

            if (applicationData.length === 0) {
                showEmptyState();
                return;
            }

            applicationData.forEach(function(application, index) {
                const applicationHtml = createApplicationItem(application, index);
                container.append(applicationHtml);
            });

            hideEmptyState();
        }

        // 创建申请项目HTML
        function createApplicationItem(application, index) {
            const statusInfo = getStatusInfo(application.APPLY_STATUS);
            const approvalInfo = getApprovalInfo(application.EA_RSLT);
            const schoolcode = "${schoolcode}";

            return `
                <div class="application-item">
                    <div class="application-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="application-index">${index + 1}</div>
                            <div class="application-content">
                                <div class="application-number">${application.APPLY_ID || ''}</div>
                                <div class="application-student">${application.XH || ''} - ${application.XM || ''}</div>
                            </div>
                        </div>
                        <div class="status-badge ${statusInfo.class}">${statusInfo.text}</div>
                    </div>

                    <div class="application-details">
                        ${schoolcode === '100017' ? `
                            <div class="detail-item">
                                <span class="detail-label">WAVG</span>
                                <span class="detail-value">${application.WAVG || ''}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">学士学位</span>
                                <span class="detail-value">${application.XWM || ''}</span>
                            </div>
                        ` : `
                            <div class="detail-item">
                                <span class="detail-label">GPA</span>
                                <span class="detail-value">${application.GPA || ''}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">签名</span>
                                <span class="detail-value">
                                    ${!isEmpty(application.DZQMSTR) ?
                                        `<img class="signature-image" src="${application.DZQMSTR}" alt="签名"/>` :
                                        '无'
                                    }
                                </span>
                            </div>
                        `}
                        <div class="detail-item">
                            <span class="detail-label">申请日期</span>
                            <span class="detail-value">${application.CZSJ || ''}</span>
                        </div>
                    </div>

                    ${application.SQLY ? `
                        <div class="application-reason">
                            <strong>申请理由：</strong>${application.SQLY}
                        </div>
                    ` : ''}

                    <div class="approval-result ${approvalInfo.class}">
                        ${approvalInfo.text}
                    </div>

                    ${application.NOTE ? `
                        <div class="application-note">
                            <strong>备注：</strong>${application.NOTE}
                        </div>
                    ` : ''}

                    <div class="application-actions">
                        ${getActionButtons(application)}
                    </div>
                </div>
            `;
        }

        // 获取状态信息
        function getStatusInfo(status) {
            switch(status) {
                case -1:
                    return { class: 'status-revoked', text: '撤销' };
                case 0:
                    return { class: 'status-draft', text: '待提交' };
                case 1:
                    return { class: 'status-pending', text: '已提交' };
                case 2:
                    return { class: 'status-processing', text: '审批中' };
                case 3:
                    return { class: 'status-completed', text: '审批结束' };
                default:
                    return { class: 'status-draft', text: '未知' };
            }
        }

        // 获取审批结果信息
        function getApprovalInfo(result) {
            switch(result) {
                case "0":
                    return { class: 'approval-rejected', text: '拒绝' };
                case "1":
                    return { class: 'approval-approved', text: '批准' };
                default:
                    return { class: 'approval-pending', text: '待审批' };
            }
        }

        // 获取操作按钮
        function getActionButtons(application) {
            let buttons = [];

            // 查看按钮
            if (application.APPLY_STATUS == 1 || application.APPLY_STATUS == 2 || application.APPLY_STATUS == 3 || application.APPLY_STATUS == -1) {
                buttons.push(`
                    <button class="btn-application-action btn-view" onclick="seeInfo('${application.APPLY_ID}', '${application.APPLY_STATUS}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看</span>
                    </button>
                `);
            }

            // 修改和撤回按钮
            if (application.APPLY_STATUS == 0) {
                buttons.push(`
                    <button class="btn-application-action btn-edit" onclick="openEditPage('${application.APPLY_ID}', 'oneApply');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                `);
                buttons.push(`
                    <button class="btn-application-action btn-revoke" onclick="revokeInfo('${application.APPLY_ID}');">
                        <i class="ace-icon fa fa-reply"></i>
                        <span>撤回</span>
                    </button>
                `);
            }

            // 打印按钮
            if (application.APPLY_STATUS == 3 && application.EA_RSLT == 1) {
                buttons.push(`
                    <button class="btn-application-action btn-print" onclick="doPrint('${application.APPLY_ID}');">
                        <i class="ace-icon fa fa-print"></i>
                        <span>打印</span>
                    </button>
                `);
            }

            return buttons.join('');
        }

        // 查看申请
        function seeInfo(sqbh) {
            const url = "/student/personalManagement/individualApplication/baccalaureateapplication/get/show?sqbh=" + sqbh;

            if (parent && parent.addTab) {
                parent.addTab('查看申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 撤销申请
        function revokeInfo(sqbh) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm("确定要撤销申请？", function(confirmed) {
                    if (confirmed) {
                        doRevokeApplication(sqbh);
                    }
                });
            } else {
                if (confirm("确定要撤销申请？")) {
                    doRevokeApplication(sqbh);
                }
            }
        }

        // 执行撤销申请
        function doRevokeApplication(sqbh) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/baccalaureateapplication/revoke/revokeInfo",
                type: "post",
                data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data.result.indexOf("/logout") !== -1) {
                        window.location.href = data.result;
                    } else {
                        if (data.result === "ok") {
                            showSuccess("撤销成功！");
                            loadApplications(1, true);
                        } else {
                            showError(data.result);
                        }
                    }
                    $("#tokenValue").val(data.token);
                },
                error: function() {
                    showError("撤销失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 添加申请
        function addApply() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/baccalaureateapplication/query/check",
                cache: false,
                type: "post",
                dataType: "json",
                success: function(response) {
                    if (response.msg) {
                        showError(response.msg);
                    } else {
                        if (response.sfxyd === "1") {
                            showNoticeModal(response);
                        } else {
                            openEditPage();
                        }
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示申请须知模态框
        function showNoticeModal(noticeData) {
            $('#noticeContent').html(noticeData.ydnr);
            $('#noticeModal').show();

            const countdownSeconds = noticeData.qzydms;
            if (countdownSeconds > 0) {
                startCountdown(countdownSeconds);
            } else {
                enableContinueButton();
            }
        }

        // 开始倒计时
        function startCountdown(seconds) {
            let remainingSeconds = seconds;
            $('#btnContinue').prop('disabled', true);

            countdownTimer = setInterval(function() {
                $('#countdown').text(`（${remainingSeconds}s）`);
                remainingSeconds--;

                if (remainingSeconds < 0) {
                    clearInterval(countdownTimer);
                    enableContinueButton();
                }
            }, 1000);
        }

        // 启用继续按钮
        function enableContinueButton() {
            $('#btnContinue').prop('disabled', false);
            $('#countdown').text('');
        }

        // 继续申请
        function continueApplication() {
            closeNoticeModal();
            setTimeout(function() {
                openEditPage();
            }, 500);
        }

        // 关闭须知模态框
        function closeNoticeModal() {
            $('#noticeModal').hide();
            if (countdownTimer) {
                clearInterval(countdownTimer);
                countdownTimer = null;
            }
        }

        // 打开编辑页面
        function openEditPage(sqbh) {
            const url = "/student/personalManagement/individualApplication/baccalaureateapplication/get/add?sqbh=" + (sqbh ? sqbh : "");

            if (parent && parent.addTab) {
                parent.addTab('学士学位申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 打印申请单
        function doPrint(sqbh) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/baccalaureateapplication/print/checkTemplate",
                cache: false,
                type: "post",
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data === "ok") {
                        openPrintModal(sqbh);
                    } else {
                        showError("当前数据还没有申请模板，请联系管理员！");
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 打开打印模态框
        function openPrintModal(sqbh) {
            const printUrl = `/student/personalManagement/individualApplication/baccalaureateapplication/print?sqbh=${sqbh}`;
            const embedHtml = `<embed class="print-embed" src="${printUrl}" onload="embedOnload(this);"></embed>`;

            $('#printContent').html(embedHtml);
            $('#printModal').show();
        }

        // 关闭打印模态框
        function closePrintModal() {
            $('#printModal').hide();
            $('#printContent').empty();
        }

        // 嵌入内容加载完成
        function embedOnload(obj) {
            // 可以在这里添加加载完成后的处理
        }

        // 显示空状态
        function showEmptyState() {
            $('#emptyState').show();
            $('#loadMoreContainer').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 返回首页
        function returnIndex() {
            location.href = "/student/application/index";
        }

        // 刷新数据
        function refreshData() {
            loadApplications(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#noticeModal, #printModal').click(function(e) {
            if (e.target === this) {
                if (this.id === 'noticeModal') {
                    closeNoticeModal();
                } else {
                    closePrintModal();
                }
            }
        });
    </script>
</body>
</html>
