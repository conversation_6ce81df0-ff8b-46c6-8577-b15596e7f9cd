<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<!DOCTYPE html>
<html>
<head>
    <title>奖惩信息</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="/wapjsp/css/mobile-framework.css">
    <link rel="stylesheet" href="/wapjsp/css/font-awesome.min.css">
    <script src="/wapjsp/js/jquery.min.js"></script>
    <script src="/wapjsp/js/layer.js"></script>
    <script src="/wapjsp/js/urp.js"></script>
    <style>
        .record-card {
            background: var(--card-background);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }
        
        .record-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--card-shadow-hover);
        }
        
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .record-type {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .record-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-reward {
            background: var(--success-light);
            color: var(--success-color);
        }
        
        .badge-punishment {
            background: var(--error-light);
            color: var(--error-color);
        }
        
        .record-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            font-size: 14px;
        }
        
        .info-label {
            color: var(--text-secondary);
            font-size: 12px;
            margin-bottom: 4px;
        }
        
        .info-value {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .record-reason {
            background: var(--background-secondary);
            border-radius: 8px;
            padding: 12px;
            margin-top: 8px;
        }
        
        .reason-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .reason-text {
            font-size: 14px;
            color: var(--text-primary);
            line-height: 1.4;
        }
        
        .record-note {
            margin-top: 8px;
            padding: 8px 12px;
            background: var(--info-light);
            border-radius: 6px;
            font-size: 13px;
            color: var(--info-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        
        .empty-desc {
            font-size: 14px;
            line-height: 1.5;
        }
        
        .statistics-bar {
            background: var(--background-secondary);
            padding: 16px;
            margin-bottom: 16px;
            border-radius: 8px;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 600;
        }
        
        .stat-value.reward {
            color: var(--success-color);
        }
        
        .stat-value.punishment {
            color: var(--error-color);
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 12px;
        }
        
        .filter-tabs {
            display: flex;
            background: var(--card-background);
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 16px;
            box-shadow: var(--card-shadow);
        }
        
        .filter-tab {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .section-header-mobile {
            padding: 16px;
            background: var(--background-secondary);
            border-bottom: 1px solid var(--border-color);
        }
        
        .section-title-mobile {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title-mobile i {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">奖惩信息</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-mobile">
            <!-- 页面标题 -->
            <div class="section-header-mobile">
                <h3 class="section-title-mobile">
                    <i class="fa fa-info-circle"></i>
                    奖惩信息
                </h3>
            </div>

            <c:if test="${!empty jcxxList}">
                <!-- 统计信息 -->
                <div class="statistics-bar" id="statisticsBar">
                    <div class="stat-item">
                        <div class="stat-value" id="totalCount">0</div>
                        <div class="stat-label">总记录</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value reward" id="rewardCount">0</div>
                        <div class="stat-label">奖励</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value punishment" id="punishmentCount">0</div>
                        <div class="stat-label">惩罚</div>
                    </div>
                </div>

                <!-- 筛选标签 -->
                <div class="filter-tabs">
                    <div class="filter-tab active" data-filter="all" onclick="filterRecords('all')">
                        全部
                    </div>
                    <div class="filter-tab" data-filter="reward" onclick="filterRecords('reward')">
                        奖励
                    </div>
                    <div class="filter-tab" data-filter="punishment" onclick="filterRecords('punishment')">
                        惩罚
                    </div>
                </div>

                <!-- 奖惩记录列表 -->
                <div class="section-mobile">
                    <div id="recordsList">
                        <c:forEach var="t" items="${jcxxList}" varStatus="s">
                            <div class="record-card" data-type="${t.jhc}">
                                <div class="record-header">
                                    <div class="record-type">${t.codeJclb.jclb}</div>
                                    <span class="record-badge ${t.jhc == '奖' ? 'badge-reward' : 'badge-punishment'}">
                                        ${t.jhc}
                                    </span>
                                </div>
                                
                                <div class="record-info">
                                    <div class="info-item">
                                        <span class="info-label">学号</span>
                                        <span class="info-value">${t.xh}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">奖惩日期</span>
                                        <span class="info-value">${t.jcrq}</span>
                                    </div>
                                </div>
                                
                                <c:if test="${!empty t.jcyy}">
                                    <div class="record-reason">
                                        <div class="reason-label">奖惩原因</div>
                                        <div class="reason-text">${t.jcyy}</div>
                                    </div>
                                </c:if>
                                
                                <c:if test="${!empty t.bz}">
                                    <div class="record-note">
                                        <i class="fa fa-info-circle"></i>
                                        备注：${t.bz}
                                    </div>
                                </c:if>
                            </div>
                        </c:forEach>
                    </div>
                </div>
            </c:if>

            <c:if test="${empty jcxxList}">
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fa fa-file-text-o"></i>
                    </div>
                    <div class="empty-title">暂无奖惩记录</div>
                    <div class="empty-desc">
                        您目前没有任何奖惩信息记录<br>
                        如有疑问请联系相关部门
                    </div>
                </div>
            </c:if>
        </div>
    </div>

    <script>
        // 全局变量
        let currentFilter = 'all';
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            initializePage();
            updateStatistics();
        });
        
        // 初始化页面
        function initializePage() {
            // 添加触摸事件处理
            $('.record-card').on('touchstart', function() {
                $(this).css('transform', 'scale(0.98)');
            }).on('touchend', function() {
                $(this).css('transform', '');
            });
        }
        
        // 筛选记录
        function filterRecords(type) {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            currentFilter = type;
            
            // 更新标签状态
            $('.filter-tab').removeClass('active');
            $(`.filter-tab[data-filter="${type}"]`).addClass('active');
            
            // 筛选记录
            $('.record-card').each(function() {
                const recordType = $(this).data('type');
                let shouldShow = false;
                
                if (type === 'all') {
                    shouldShow = true;
                } else if (type === 'reward' && recordType === '奖') {
                    shouldShow = true;
                } else if (type === 'punishment' && recordType === '惩') {
                    shouldShow = true;
                }
                
                if (shouldShow) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
            
            // 更新统计信息
            updateFilteredStatistics();
        }
        
        // 更新统计信息
        function updateStatistics() {
            let totalCount = 0;
            let rewardCount = 0;
            let punishmentCount = 0;
            
            $('.record-card').each(function() {
                totalCount++;
                const recordType = $(this).data('type');
                if (recordType === '奖') {
                    rewardCount++;
                } else if (recordType === '惩') {
                    punishmentCount++;
                }
            });
            
            $('#totalCount').text(totalCount);
            $('#rewardCount').text(rewardCount);
            $('#punishmentCount').text(punishmentCount);
        }
        
        // 更新筛选后的统计信息
        function updateFilteredStatistics() {
            let visibleCount = $('.record-card:visible').length;
            
            if (currentFilter === 'all') {
                updateStatistics();
            } else {
                $('#totalCount').text(visibleCount);
                
                if (currentFilter === 'reward') {
                    $('#rewardCount').text(visibleCount);
                    $('#punishmentCount').text(0);
                } else if (currentFilter === 'punishment') {
                    $('#rewardCount').text(0);
                    $('#punishmentCount').text(visibleCount);
                }
            }
        }
        
        // 刷新数据
        function refreshData() {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            // 重新加载页面
            window.location.reload();
        }
        
        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                // 页面变为可见时更新统计
                setTimeout(function() {
                    updateStatistics();
                }, 500);
            }
        });
    </script>
</body>
</html>
