<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html>
<html>
<head>
    <title>未缴费课程</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="/wapjsp/css/mobile-framework.css">
    <link rel="stylesheet" href="/wapjsp/css/font-awesome.min.css">
    <script src="/wapjsp/js/jquery.min.js"></script>
    <script src="/wapjsp/js/layer.js"></script>
    <script src="/wapjsp/js/urp.js"></script>
    <style>
        .tab-container {
            background: var(--card-background);
            border-radius: 12px;
            margin-bottom: 16px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }
        
        .tab-header {
            display: flex;
            background: var(--primary-color);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .tab-header::-webkit-scrollbar {
            display: none;
        }
        
        .tab-item {
            flex: 1;
            min-width: 120px;
            padding: 12px 16px;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            white-space: nowrap;
        }
        
        .tab-item:last-child {
            border-right: none;
        }
        
        .tab-item.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            position: relative;
        }
        
        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: white;
            border-radius: 2px;
        }
        
        .tab-content {
            min-height: 400px;
            background: var(--background-primary);
        }
        
        .iframe-container {
            position: relative;
            width: 100%;
            height: 100%;
            min-height: 400px;
            border-radius: 0 0 12px 12px;
            overflow: hidden;
        }
        
        .iframe-mobile {
            width: 100%;
            height: 100%;
            border: none;
            background: var(--background-primary);
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--background-primary);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: var(--text-secondary);
            font-size: 14px;
        }
        
        .error-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }
        
        .error-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: var(--error-color);
        }
        
        .retry-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            margin-top: 16px;
            min-height: 44px;
            min-width: 100px;
        }
        
        .tab-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            background: var(--error-color);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }
        
        .section-header-mobile {
            padding: 16px;
            background: var(--background-secondary);
            border-bottom: 1px solid var(--border-color);
        }
        
        .section-title-mobile {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .section-title-mobile i {
            margin-right: 8px;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">未缴费课程查询</div>
            <div class="navbar-action" onclick="refreshCurrentTab();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-mobile">
            <!-- 页面标题 -->
            <div class="section-header-mobile">
                <h3 class="section-title-mobile">
                    <i class="fa fa-check-square-o"></i>
                    未缴费课程查询
                </h3>
            </div>

            <!-- 标签页容器 -->
            <div class="tab-container">
                <!-- 标签页头部 -->
                <div class="tab-header">
                    <div class="tab-item active" data-tab="exam" data-url="/student/examinationManagement/notPayCost/gradeExamination/index">
                        考试报名未缴费
                        <span class="tab-indicator" id="examIndicator" style="display: none;">!</span>
                    </div>
                    <div class="tab-item" data-tab="course" data-url="/student/examinationManagement/notPayCost/selectCourse/index">
                        重、复、补修选课未缴费
                        <span class="tab-indicator" id="courseIndicator" style="display: none;">!</span>
                    </div>
                </div>

                <!-- 标签页内容 -->
                <div class="tab-content">
                    <div class="iframe-container">
                        <!-- 加载状态 -->
                        <div class="loading-overlay" id="loadingOverlay">
                            <div class="loading-spinner"></div>
                            <div class="loading-text">正在加载...</div>
                        </div>
                        
                        <!-- 错误状态 -->
                        <div class="error-state" id="errorState" style="display: none;">
                            <div class="error-icon">
                                <i class="fa fa-exclamation-triangle"></i>
                            </div>
                            <p>加载失败，请检查网络连接</p>
                            <button class="retry-button" onclick="retryLoad()">重试</button>
                        </div>
                        
                        <!-- iframe内容 -->
                        <iframe id="contentFrame" class="iframe-mobile" scrolling="auto"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 隐藏表单 -->
    <form action="/xktempjump" name="xktempjump" style="display: none;"></form>
    <input type="hidden" id="tokenValue" value="${token_in_session}"/>

    <script>
        // 全局变量
        let currentTab = 'exam';
        let loadTimeout;
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            initializePage();
            bindEvents();
            loadDefaultTab();
        });
        
        // 初始化页面
        function initializePage() {
            // 根据权限控制显示的标签页
            if("${xkjdlx}" == "005" && "${xxbm}" == "100010") {
                // 特殊学校只显示特定标签
                $('.tab-item').hide();
                $('.tab-item[data-tab="exam"]').show();
                $('.tab-item[data-tab="course"]').show();
            } else {
                // 根据权限控制标签显示
                var xsfaxk = '${xsfaxk}';
                var xszyxk = '${xszyxk}';
                var xsfxxk = '${xsfxxk}';
                
                // 这里可以根据权限隐藏相应的标签页
                // 暂时保持所有标签可见
            }
            
            // 调整iframe高度
            adjustIframeHeight();
        }
        
        // 绑定事件
        function bindEvents() {
            // 标签页切换事件
            $('.tab-item').on('click', function() {
                if (!$(this).hasClass('active')) {
                    switchTab($(this));
                }
            });
            
            // iframe加载事件
            $('#contentFrame').on('load', function() {
                hideLoading();
                adjustIframeHeight();
                
                // 检查iframe内容是否加载成功
                try {
                    const iframeDoc = this.contentDocument || this.contentWindow.document;
                    if (iframeDoc.body && iframeDoc.body.innerHTML.trim() === '') {
                        showError();
                    }
                } catch (e) {
                    // 跨域访问限制，忽略错误
                }
            });
            
            // iframe加载错误事件
            $('#contentFrame').on('error', function() {
                hideLoading();
                showError();
            });
            
            // 窗口大小变化事件
            $(window).on('resize', function() {
                adjustIframeHeight();
            });
        }
        
        // 加载默认标签页
        function loadDefaultTab() {
            const activeTab = $('.tab-item.active');
            loadTabContent(activeTab.data('url'));
        }
        
        // 切换标签页
        function switchTab($tabItem) {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            // 更新标签状态
            $('.tab-item').removeClass('active');
            $tabItem.addClass('active');
            
            // 更新当前标签
            currentTab = $tabItem.data('tab');
            
            // 加载标签内容
            const url = $tabItem.data('url');
            loadTabContent(url);
        }
        
        // 加载标签内容
        function loadTabContent(url) {
            if (!url) return;
            
            showLoading();
            hideError();
            
            // 设置加载超时
            clearTimeout(loadTimeout);
            loadTimeout = setTimeout(function() {
                showError();
            }, 10000); // 10秒超时
            
            // 加载iframe内容
            $('#contentFrame').attr('src', url);
        }
        
        // 显示加载状态
        function showLoading() {
            $('#loadingOverlay').show();
            $('#errorState').hide();
        }
        
        // 隐藏加载状态
        function hideLoading() {
            clearTimeout(loadTimeout);
            $('#loadingOverlay').hide();
        }
        
        // 显示错误状态
        function showError() {
            clearTimeout(loadTimeout);
            $('#loadingOverlay').hide();
            $('#errorState').show();
        }
        
        // 隐藏错误状态
        function hideError() {
            $('#errorState').hide();
        }
        
        // 重试加载
        function retryLoad() {
            const activeTab = $('.tab-item.active');
            const url = activeTab.data('url');
            loadTabContent(url);
        }
        
        // 刷新当前标签页
        function refreshCurrentTab() {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            retryLoad();
        }
        
        // 调整iframe高度
        function adjustIframeHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const headerHeight = $('.section-header-mobile').outerHeight();
            const tabHeaderHeight = $('.tab-header').outerHeight();
            const padding = 32; // 额外的内边距
            
            const iframeHeight = windowHeight - navbarHeight - headerHeight - tabHeaderHeight - padding;
            
            $('.iframe-container').css('min-height', Math.max(iframeHeight, 400) + 'px');
            $('#contentFrame').css('height', Math.max(iframeHeight, 400) + 'px');
        }
        
        // 显示标签指示器
        function showTabIndicator(tabName) {
            $('#' + tabName + 'Indicator').show();
        }
        
        // 隐藏标签指示器
        function hideTabIndicator(tabName) {
            $('#' + tabName + 'Indicator').hide();
        }
        
        // 页面可见性变化时刷新数据
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                // 页面变为可见时刷新当前标签
                setTimeout(function() {
                    refreshCurrentTab();
                }, 500);
            }
        });
        
        // 监听iframe内的消息（如果需要）
        window.addEventListener('message', function(event) {
            // 处理来自iframe的消息
            if (event.data && event.data.type) {
                switch (event.data.type) {
                    case 'resize':
                        adjustIframeHeight();
                        break;
                    case 'notification':
                        // 显示通知指示器
                        if (event.data.tab) {
                            showTabIndicator(event.data.tab);
                        }
                        break;
                }
            }
        });
    </script>
</body>
</html>
