<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>历年成绩变更申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 成绩变更申请页面样式 */
        .grade-change-header {
            background: linear-gradient(135deg, var(--primary-color), var(--error-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .grade-change-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .grade-change-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .time-info-section {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--info-dark);
            font-size: var(--font-size-small);
            line-height: 1.5;
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-title i {
            color: var(--primary-color);
        }
        
        .filter-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }
        
        .filter-item {
            display: flex;
            flex-direction: column;
        }
        
        .filter-label {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .filter-input {
            padding: var(--padding-sm);
            border: 1px solid var(--divider-color);
            border-radius: 6px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
        }
        
        .filter-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .btn-search {
            flex: 1;
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-search:hover {
            background: var(--info-dark);
        }
        
        .btn-add-application {
            flex: 1;
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-add-application:hover {
            background: var(--success-dark);
        }
        
        .btn-add-application:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .applications-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .applications-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .applications-section-title i {
            color: var(--error-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-index {
            background: var(--error-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .application-content {
            flex: 1;
        }
        
        .application-number {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-draft {
            background: var(--text-disabled);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-processing {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-completed {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-revoked {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .approval-result {
            margin-top: var(--margin-sm);
            padding: var(--padding-sm);
            border-radius: 6px;
            font-size: var(--font-size-small);
            text-align: center;
        }
        
        .approval-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .approval-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .approval-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .course-info {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .course-section {
            margin-bottom: var(--margin-sm);
        }
        
        .course-section:last-child {
            margin-bottom: 0;
        }
        
        .course-section-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xs);
        }
        
        .course-detail-item {
            font-size: var(--font-size-mini);
        }
        
        .application-reason {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .application-attachments {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .application-note {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-application-action {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-revoke {
            background: var(--error-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .warning-alert {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .warning-alert i {
            color: var(--warning-color);
            font-size: 20px;
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .grade-change-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .time-info-section,
            .filter-section,
            .applications-section,
            .warning-alert {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .filter-row {
                grid-template-columns: 1fr;
            }
            
            .filter-actions {
                flex-direction: column;
            }
            
            .course-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">历年成绩变更申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 成绩变更申请头部 -->
        <div class="grade-change-header">
            <div class="grade-change-title">历年成绩变更申请</div>
            <div class="grade-change-desc">管理您的历年成绩变更申请记录</div>
        </div>
        
        <!-- 申请时间信息 -->
        <c:if test="${flag == 'showAdd'}">
            <div class="time-info-section">
                <strong>申请时间：</strong>
                ${fn:substring(kzkg.kssj, 0, 4)}-${fn:substring(kzkg.kssj, 4, 6)}-${fn:substring(kzkg.kssj, 6, 8)} ${fn:substring(kzkg.kssj, 8, 10)}:${fn:substring(kzkg.kssj, 10, 12)}:${fn:substring(kzkg.kssj, 12, 14)}~${fn:substring(kzkg.jssj, 0, 4)}-${fn:substring(kzkg.jssj, 4, 6)}-${fn:substring(kzkg.jssj, 6, 8)} ${fn:substring(kzkg.jssj, 8, 10)}:${fn:substring(kzkg.jssj, 10, 12)}:${fn:substring(kzkg.jssj, 12, 14)}
            </div>
        </c:if>
        
        <!-- 状态提示信息 -->
        <c:if test="${flag == 'nonparametric'}">
            <div class="warning-alert">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>申请参数未维护，请联系管理员处理</span>
            </div>
        </c:if>
        
        <c:if test="${flag == 'notenabled'}">
            <div class="warning-alert">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>申请未启用，请联系管理员处理</span>
            </div>
        </c:if>
        
        <c:if test="${flag == 'nottime'}">
            <div class="warning-alert">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>不在申请时间范围或申请开关关闭</span>
            </div>
        </c:if>
        
        <!-- 查询条件 -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            <form id="queryInfo" name="queryInfo" class="filter-form">
                <div class="filter-item">
                    <label class="filter-label">学年学期</label>
                    <select id="zxjxjhh_cx" name="zxjxjhh_cx" class="filter-input">
                        <cache:query var="xnxq" region="jh_zxjxjhb_view" orderby="zxjxjhh desc"/>
                        <c:forEach items="${xnxq}" var="xnxq" varStatus="index">
                            <option value="${xnxq.zxjxjhh}" <c:if test="${xnxq.zxjxjhh == zxjxjhh}"> selected </c:if>>${xnxq.zxjxjhm}</option>
                        </c:forEach>
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">开课院系</label>
                    <select name="kkxsh_cx" id="kkxsh_cx" class="filter-input">
                        <option value="">全部</option>
                        <cache:query var="xsb" region="code_xsb_jxdw" orderby="xsh asc"/>
                        <c:forEach items="${xsb}" var="xsb">
                            <option value="${xsb.xsh}">${xsb.xsm}</option>
                        </c:forEach>
                    </select>
                </div>
                <div class="filter-row">
                    <div class="filter-item">
                        <label class="filter-label">课程号</label>
                        <input type="text" name="kch_cx" id="kch_cx" class="filter-input" placeholder="请输入课程号">
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">课程名</label>
                        <input type="text" name="kcm_cx" id="kcm_cx" class="filter-input" placeholder="请输入课程名">
                    </div>
                </div>
                <div class="filter-actions">
                    <button type="button" class="btn-search" onclick="getApplysList(1, '30_sl', true);">
                        <i class="ace-icon fa fa-search"></i>
                        <span>查询</span>
                    </button>
                    <c:if test="${flag == 'showAdd'}">
                        <button type="button" class="btn-add-application" onclick="addApply();">
                            <i class="ace-icon fa fa-plus"></i>
                            <span>新增申请</span>
                        </button>
                    </c:if>
                </div>
            </form>
        </div>
        
        <!-- 申请列表 -->
        <div class="applications-section">
            <div class="applications-section-header">
                <div class="applications-section-title">
                    <i class="ace-icon fa fa-list"></i>
                    申请信息
                </div>
            </div>
            
            <div id="applicationsList">
                <!-- 动态加载申请列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreApplications();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-edit"></i>
            <div class="empty-state-title">暂无申请记录</div>
            <div class="empty-state-desc">您还没有提交任何成绩变更申请</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let applicationData = [];
        let params = "";

        $(function() {
            initPage();
            getApplysList(1, "30_sl", true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 分页查询
        function getApplysList(page, pageSizeVal, conditionChanged) {
            if (pageSizeVal == undefined) {
                pageSizeVal = "30_sl";
                page = "1";
            }

            if (conditionChanged) {
                params = $(document.queryInfo).serialize();
            }

            var parr = (pageSizeVal + "").split("_");
            var pageSize = parseInt(parr[0]);

            showLoading(true);

            const url = "/student/application/gradeChange/getApplyList";

            $.ajax({
                url: url,
                cache: false,
                type: "post",
                data: params + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records) {
                        applicationData = data.records;
                        totalCount = data.pageContext ? data.pageContext.totalCount : 0;

                        if (applicationData.length > 0) {
                            renderApplications();
                        } else {
                            showEmptyState();
                        }
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplications() {
            const container = $('#applicationsList');
            container.empty();

            if (applicationData.length === 0) {
                showEmptyState();
                return;
            }

            applicationData.forEach(function(application, index) {
                const applicationHtml = createApplicationItem(application, index);
                container.append(applicationHtml);
            });

            hideEmptyState();
        }

        // 创建申请项目HTML
        function createApplicationItem(application, index) {
            const statusInfo = getStatusInfo(application.APPLY_STATUS);
            const approvalInfo = getApprovalInfo(application.EA_RSLT);

            return `
                <div class="application-item">
                    <div class="application-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="application-index">${index + 1}</div>
                            <div class="application-content">
                                <div class="application-number">${application.APPLY_ID || ''}</div>
                            </div>
                        </div>
                        <div class="status-badge ${statusInfo.class}">${statusInfo.text}</div>
                    </div>

                    <div class="course-info">
                        <div class="course-section">
                            <div class="course-section-title">课程信息</div>
                            <div class="course-details">
                                <div class="course-detail-item">课程号：${application.KCH || ''}</div>
                                <div class="course-detail-item">课程名：${application.KCM || ''}</div>
                                <div class="course-detail-item">课序号：${application.KXH || ''}</div>
                                <div class="course-detail-item">开课院系：${application.KKXSM || ''}</div>
                                <div class="course-detail-item">学年学期：${application.XNXQ || ''}</div>
                            </div>
                        </div>
                    </div>

                    ${application.SQYY ? `
                        <div class="application-reason">
                            <strong>申请原因：</strong>${application.SQYY}
                        </div>
                    ` : ''}

                    ${application.FJ ? `
                        <div class="application-attachments">
                            <strong>附件：</strong>${application.FJ.replace(/,/g, '<br>')}
                        </div>
                    ` : ''}

                    <div class="approval-result ${approvalInfo.class}">
                        ${approvalInfo.text}
                    </div>

                    ${application.NOTE ? `
                        <div class="application-note">
                            <strong>备注：</strong>${application.NOTE}
                        </div>
                    ` : ''}

                    <div class="application-actions">
                        ${getActionButtons(application)}
                    </div>
                </div>
            `;
        }

        // 获取状态信息
        function getStatusInfo(status) {
            switch(status) {
                case -1:
                    return { class: 'status-revoked', text: '撤销' };
                case 0:
                    return { class: 'status-draft', text: '待提交' };
                case 1:
                    return { class: 'status-pending', text: '已提交' };
                case 2:
                    return { class: 'status-processing', text: '审批中' };
                case 3:
                    return { class: 'status-completed', text: '审批结束' };
                default:
                    return { class: 'status-draft', text: '未知' };
            }
        }

        // 获取审批结果信息
        function getApprovalInfo(result) {
            switch(result) {
                case "0":
                    return { class: 'approval-rejected', text: '拒绝' };
                case "1":
                    return { class: 'approval-approved', text: '批准' };
                default:
                    return { class: 'approval-pending', text: '待审批' };
            }
        }

        // 获取操作按钮
        function getActionButtons(application) {
            let buttons = [];

            // 查看按钮
            if (application.APPLY_STATUS == 1 || application.APPLY_STATUS == 2 || application.APPLY_STATUS == 3 || application.APPLY_STATUS == -1) {
                buttons.push(`
                    <button class="btn-application-action btn-view" onclick="seeApply('${application.APPLY_ID}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看</span>
                    </button>
                `);
            }

            // 修改和撤回按钮
            if (application.APPLY_STATUS == 0) {
                buttons.push(`
                    <button class="btn-application-action btn-edit" onclick="openEditPage('${application.APPLY_ID}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                `);
                buttons.push(`
                    <button class="btn-application-action btn-revoke" onclick="revokeApply('${application.APPLY_ID}');">
                        <i class="ace-icon fa fa-reply"></i>
                        <span>撤回</span>
                    </button>
                `);
            }

            return buttons.join('');
        }

        // 查看申请
        function seeApply(sqbh) {
            const url = "/student/application/scoreCheck/seeApply?sqbh=" + sqbh;

            if (parent && parent.addTab) {
                parent.addTab('查看申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 撤回申请
        function revokeApply(sqbh) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm("确定要撤回申请？", function(confirmed) {
                    if (confirmed) {
                        doRevokeApplication(sqbh);
                    }
                });
            } else {
                if (confirm("确定要撤回申请？")) {
                    doRevokeApplication(sqbh);
                }
            }
        }

        // 执行撤回申请
        function doRevokeApplication(sqbh) {
            showLoading(true);

            $.ajax({
                url: "/student/application/scoreCheck/revokeApply",
                type: "post",
                data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                dataType: "json",
                success: function(response) {
                    if (response.status != 200) {
                        showError(response.msg);
                    } else {
                        const data = response.data;
                        $("#tokenValue").val(data.token);
                        if (data.result.indexOf("/") !== -1) {
                            showError("页面已过期，请刷新页面！");
                        } else {
                            if (data.result === "ok") {
                                showSuccess("撤销成功！");
                                getApplysList(1, "30_sl", true);
                            } else {
                                showError(data.result);
                            }
                        }
                    }
                },
                error: function() {
                    showError("撤销失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 新增申请
        function addApply() {
            showLoading(true);

            const url = "/student/application/gradeChange/checkaddApply";

            $.ajax({
                url: url,
                cache: false,
                type: "post",
                dataType: "json",
                success: function(response) {
                    if (response.msg) {
                        showError(response.msg);
                    } else {
                        if (response.sfxyd == "1") {
                            // 需要显示申请须知
                            showApplicationNotice(response);
                        } else {
                            openEditPage();
                        }
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示申请须知
        function showApplicationNotice(data) {
            let noticeHtml = `
                <div style="background: var(--bg-primary); border-radius: 8px; padding: var(--padding-md); margin: var(--margin-md);">
                    <h3 style="margin-bottom: var(--margin-md); color: var(--text-primary);">申请须知</h3>
                    <div style="margin-bottom: var(--margin-md); line-height: 1.6;">${data.ydnr}</div>
                    <div style="text-align: center;">
                        <button onclick="proceedToApplication();" style="background: var(--info-color); color: white; border: none; padding: var(--padding-sm) var(--padding-lg); border-radius: 6px; margin-right: var(--margin-sm);">
                            继续申请
                        </button>
                        <button onclick="closeNotice();" style="background: var(--text-disabled); color: white; border: none; padding: var(--padding-sm) var(--padding-lg); border-radius: 6px;">
                            关闭
                        </button>
                    </div>
                </div>
            `;

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(noticeHtml);
            } else {
                // 简单的alert显示
                if (confirm("申请须知:\n" + data.ydnr + "\n\n是否继续申请？")) {
                    openEditPage();
                }
            }
        }

        // 继续申请
        function proceedToApplication() {
            closeNotice();
            setTimeout(function() {
                openEditPage();
            }, 500);
        }

        // 关闭须知
        function closeNotice() {
            // 关闭弹窗的逻辑
            if (typeof urp !== 'undefined' && urp.closeAlert) {
                urp.closeAlert();
            }
        }

        // 打开编辑页面
        function openEditPage(sqbh) {
            const url = "/student/application/gradeChange/addApply?sqbh=" + (sqbh ? sqbh : "");

            if (parent && parent.addTab) {
                parent.addTab(sqbh ? '修改申请' : '新增申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 显示空状态
        function showEmptyState() {
            $('#emptyState').show();
            $('#loadMoreContainer').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 返回首页
        function returnIndex() {
            location.href = "/student/application/index";
        }

        // 刷新数据
        function refreshData() {
            getApplysList(1, "30_sl", true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
