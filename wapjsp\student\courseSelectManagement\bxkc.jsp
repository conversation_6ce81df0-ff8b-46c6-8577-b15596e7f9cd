<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<!DOCTYPE html>
<html>
<head>
    <title>补修选课</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="/wapjsp/css/mobile-framework.css">
    <link rel="stylesheet" href="/wapjsp/css/font-awesome.min.css">
    <script src="/wapjsp/js/jquery.min.js"></script>
    <script src="/wapjsp/js/layer.js"></script>
    <script src="/wapjsp/js/urp.js"></script>
    <style>
        .course-card {
            background: var(--card-background);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }
        
        .course-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--card-shadow-hover);
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .course-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            flex: 1;
            margin-right: 12px;
        }
        
        .course-code {
            font-size: 12px;
            color: var(--text-secondary);
            background: var(--background-secondary);
            padding: 4px 8px;
            border-radius: 6px;
            white-space: nowrap;
        }
        
        .course-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        
        .info-label {
            color: var(--text-secondary);
            margin-right: 8px;
            min-width: 60px;
        }
        
        .info-value {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .course-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-applied {
            background: var(--info-light);
            color: var(--info-color);
        }
        
        .status-selected {
            background: var(--success-light);
            color: var(--success-color);
        }
        
        .btn-select-course {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            min-height: 44px;
            min-width: 100px;
            transition: all 0.3s ease;
        }
        
        .btn-select-course:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .alert-mobile {
            background: var(--success-light);
            border: 1px solid var(--success-color);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .alert-icon {
            color: var(--success-color);
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">补修选课</div>
            <div class="navbar-action" onclick="refreshCourseList();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-mobile">
            <!-- 提示信息 -->
            <div class="alert-mobile">
                <i class="ace-icon fa fa-info-circle alert-icon"></i>
                特别提示：补修选课即按照个人培养方案要求，以前学期有仍未修读的课程，根据本学期课程开设情况进行补修。
            </div>

            <!-- 课程列表 -->
            <div class="section-mobile">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fa fa-list"></i>
                        可补修的课程列表
                    </h3>
                </div>
                
                <div id="courseListContainer">
                    <!-- 加载中状态 -->
                    <div id="loadingState" class="loading-container">
                        <div class="course-card loading-skeleton" style="height: 120px;"></div>
                        <div class="course-card loading-skeleton" style="height: 120px;"></div>
                        <div class="course-card loading-skeleton" style="height: 120px;"></div>
                    </div>
                    
                    <!-- 课程列表容器 -->
                    <div id="courseList" style="display: none;"></div>
                    
                    <!-- 空状态 -->
                    <div id="emptyState" class="empty-state" style="display: none;">
                        <div class="empty-icon">
                            <i class="fa fa-book"></i>
                        </div>
                        <p>暂无可补修的课程</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 隐藏表单 -->
    <form action="/student/courseSelect/planCourse/waitingfor?dealType=2" name="frm" method="POST" target="_parent" style="display: none;">
        <input type="hidden" name="tokenValue" value="${token_in_session}">
    </form>

    <script>
        // 全局变量
        let courseData = [];
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            queryList();
        });
        
        // 查询课程列表
        function queryList() {
            showLoading();
            
            var url = "";
            if(${xxbm == '100020'}){
                url = "/student/courseSelect/fixCourseSelectByHnlg/queryList";
            }else if(${xxbm == '100045'} || ${xxbm == '100014'}){
                url = "/student/courseSelect/fixCourseSelectByQqhe/queryList";
            }else{
                url = "/student/courseSelect/fixCourseSelect/queryList";
            }

            $.ajax({
                url: url,
                type: "post",
                dataType: "json",
                success: function (d) {
                    hideLoading();
                    if(d["result"] == undefined || d["result"] == "") {
                        courseData = d["list"] || [];
                        renderCourseList(d);
                    } else {
                        showError(d["result"]);
                    }
                },
                error: function (xhr) {
                    hideLoading();
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
                }
            });
        }
        
        // 渲染课程列表
        function renderCourseList(data) {
            const courseList = data["list"] || [];
            const tdksqbs = data["Tdksqbs"] || [];
            const yxkc = data["yxkc"] || [];
            
            if (courseList.length === 0) {
                showEmptyState();
                return;
            }
            
            let html = '';
            courseList.forEach(function(course, index) {
                const courseCode = course[1];
                let status = "0"; // 0: 可选课, 1: 已申请课程替代, 2: 已选课
                
                if(${xxbm != '100020'}) {
                    // 检查是否已申请课程替代
                    if (tdksqbs.includes(courseCode)) {
                        status = "1";
                    } else if (yxkc.includes(courseCode)) {
                        status = "2";
                    }
                }
                
                html += generateCourseCard(course, index + 1, status);
            });
            
            $('#courseList').html(html).show();
            $('#emptyState').hide();
        }
        
        // 生成课程卡片HTML
        function generateCourseCard(course, index, status) {
            const [semester, courseCode, courseName, credits] = course;
            
            let actionHtml = '';
            if (status === "0") {
                actionHtml = `<button class="btn-select-course" onclick="enterCourseSelection('${courseCode}')">
                    <i class="fa fa-plus-circle"></i> 进入选课
                </button>`;
            } else if (status === "1") {
                actionHtml = `<span class="status-badge status-applied">已申请课程替代</span>`;
            } else if (status === "2") {
                actionHtml = `<span class="status-badge status-selected">已选课</span>`;
            }
            
            return `
                <div class="course-card" data-course-code="${courseCode}">
                    <div class="course-header">
                        <h4 class="course-title">${courseName}</h4>
                        <span class="course-code">${courseCode}</span>
                    </div>
                    
                    <div class="course-info">
                        <div class="info-item">
                            <span class="info-label">学期:</span>
                            <span class="info-value">${semester}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">学分:</span>
                            <span class="info-value">${credits}</span>
                        </div>
                    </div>
                    
                    <div class="course-actions">
                        <span class="course-index">第 ${index} 门课程</span>
                        ${actionHtml}
                    </div>
                </div>
            `;
        }
        
        // 进入选课
        function enterCourseSelection(courseCode) {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            // 显示加载状态
            const button = event.target.closest('.btn-select-course');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 进入中...';
            button.disabled = true;
            
            // 跳转到选课页面
            setTimeout(function() {
                window.location = "/student/courseSelect/fixCourseSelect/queryCurrentCourse/" + courseCode;
            }, 500);
        }
        
        // 刷新课程列表
        function refreshCourseList() {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            queryList();
        }
        
        // 显示加载状态
        function showLoading() {
            $('#loadingState').show();
            $('#courseList').hide();
            $('#emptyState').hide();
        }
        
        // 隐藏加载状态
        function hideLoading() {
            $('#loadingState').hide();
        }
        
        // 显示空状态
        function showEmptyState() {
            $('#loadingState').hide();
            $('#courseList').hide();
            $('#emptyState').show();
        }
        
        // 显示错误信息
        function showError(message) {
            layer.alert(message, {
                icon: 2,
                title: '错误提示',
                skin: 'layer-mobile'
            });
        }
        
        // 页面可见性变化时刷新数据
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                // 页面变为可见时刷新数据
                setTimeout(queryList, 500);
            }
        });
    </script>
</body>
</html>
