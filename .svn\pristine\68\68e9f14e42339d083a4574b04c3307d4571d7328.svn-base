<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>选课结果</title>
<c:if test="${mobile}">
	<link rel="stylesheet" href="/css/phone/phone.css" type="text/css"></link>
</c:if>
<style type="text/css">
	.self-margin .header {
	    margin-top: 6px !important;
	    margin-bottom: 10px !important;
	    padding-bottom: 4px !important;
	    border-bottom: 1px solid #CCC;
	    line-height: 28px;
	}
	
	.header.grey {
	    border-bottom-color: #c3c3c3;
	}
	h4.smaller {
	    font-size: 17px;
	}
	.header {
	    line-height: 28px;
	    margin-bottom: 16px;
	    margin-top: 18px;
	    padding-bottom: 4px;
	    border-bottom: 1px solid #CCC;
	}
	.grey {
	    color: #777 !important;
	}
	.lighter {
	    font-weight: lighter;
	}
          
          .btn.btn-round {
	    border-radius: 4px !important;
	}
	
	.btn.btn-bold, .btn.btn-round {
	    border-bottom-width: 2px;
	}
	
	.btn-group-xs>.btn, .btn-xs {
	    padding-top: 3px;
	    padding-bottom: 3px;
	    border-width: 3px;
	}
	.btn {
	    color: #FFF !important;
	    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);
	    background-image: none !important;
	    border: 5px solid #FFF;
	    border-radius: 0;
	    box-shadow: none !important;
	    -webkit-transition: background-color .15s, border-color .15s, opacity .15s;
	    -o-transition: background-color .15s,border-color .15s,opacity .15s;
	    transition: background-color .15s, border-color .15s, opacity .15s;
	    vertical-align: middle;
	    margin: 0;
	    position: relative;
	    font-weight: 400;
	}
	.breadcrumb, .breadcrumb>li>a, .btn {
	    display: inline-block;
	}
	.btn, .dropdown-colorpicker a {
	    cursor: pointer;
	}
	.btn-group-xs>.btn, .btn-xs {
	    padding: 1px 5px;
	    font-size: 12px;
	    line-height: 1.3;
	    border-radius: 3px;
	}
	.btn, .btn-danger.active, .btn-danger:active, .btn-default.active, .btn-default:active, .btn-info.active, .btn-info:active, .btn-primary.active, .btn-primary:active, .btn-warning.active, .btn-warning:active, .btn.active, .btn:active, .dropdown-menu>.disabled>a:focus, .dropdown-menu>.disabled>a:hover, .form-control, .navbar-toggle, .open>.dropdown-toggle.btn-danger, .open>.dropdown-toggle.btn-default, .open>.dropdown-toggle.btn-info, .open>.dropdown-toggle.btn-primary, .open>.dropdown-toggle.btn-warning {
	    background-image: none;
	}
	button, input, select, textarea {
	    font-family: inherit;
	    font-size: inherit;
	    line-height: inherit;
	}
	button, html input[type=button], input[type=reset], input[type=submit] {
	    -webkit-appearance: button;
	    cursor: pointer;
	}
	button, select {
	    text-transform: none;
	}
	button {
	    overflow: visible;
	}
	button, input, optgroup, select, textarea {
	    color: inherit;
	    font: inherit;
	    margin: 0;
	}
	.btn, .btn-default, .btn-default.focus, .btn-default:focus, .btn.focus, .btn:focus {
	    background-color: #ABBAC3 !important;
	    border-color: #ABBAC3;
	}
	.btn-info, .btn-info.focus, .btn-info:focus {
	    background-color: #6FB3E0 !important;
	    border-color: #6FB3E0;
	}
	.btn-success, .btn-success.focus, .btn-success:focus {
	    background-color: #87B87F !important;
	    border-color: #87B87F;
	}
	.btn-warning, .btn-warning.focus, .btn-warning:focus {
	    background-color: #FFB752 !important;
	    border-color: #FFB752;
	}

	.alert {
	    font-size: 14px;
	}
	
	.alert, .well {
	    border-radius: 0;
	}
	.alert-success {
	    background-color: #dff0d8;
	    border-color: #d6e9c6;
	    color: #3c763d;
	}
	.alert {
	    padding: 15px;
	    border: 1px solid transparent;
	    border-radius: 4px;
	}
	.alert, .thumbnail {
	    margin-bottom: 20px;
	}
	
	.alert .close {
	    font-size: 16px;
	}
	
	button.close {
	    padding: 0;
	    cursor: pointer;
	    background: 0 0;
	    border: 0;
	    -webkit-appearance: none;
	}
	.close {
	    float: right;
	    font-size: 21px;
	    color: #000;
	    text-shadow: 0 1px 0 #fff;
	    opacity: .2;
	    filter: alpha(opacity=20);
	}
	.alert .alert-link, .close {
	    font-weight: 700;
	}
	.badge, .close, .label {
	    line-height: 1;
	}
	.table-bordered, td, th {
	    border-radius: 0 !important;
	}
	
	.table-bordered, .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
	    border: 1px solid #ddd;
	}
	.table {
	    width: 100%;
	    max-width: 100%;
	    margin-bottom: 20px;
	}
	pre code, table {
	    background-color: transparent;
	}
	table {
	    border-collapse: collapse;
	    border-spacing: 0;
	}
	.table>thead>tr {
	    color: #707070;
	    font-weight: 400;
	    background: repeat-x #F2F2F2;
	    background-image: -webkit-linear-gradient(top, #F8F8F8 0, #ECECEC 100%);
	    background-image: -o-linear-gradient(top,#F8F8F8 0,#ECECEC 100%);
	    background-image: linear-gradient(to bottom, #F8F8F8 0, #ECECEC 100%);
	    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff8f8f8', endColorstr='#ffececec', GradientType=0);
	}
	.table.table-bordered>thead>tr>th:first-child {
	    border-left-color: #ddd;
	}
	
	.table>caption+thead>tr:first-child>td, .table>caption+thead>tr:first-child>th, .table>colgroup+thead>tr:first-child>td, .table>colgroup+thead>tr:first-child>th, .table>thead:first-child>tr:first-child>td, .table>thead:first-child>tr:first-child>th {
	    border-top: 0;
	}
	.table.table-bordered>thead>tr>th {
	    vertical-align: middle;
	}
	.table>thead>tr>th:first-child {
	    border-left-color: #F1F1F1;
	}
	.table>thead>tr>th {
	    border-color: #ddd;
	    font-weight: 700;
	}
	.table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
	    border-bottom-width: 2px;
	}
	.table-bordered, .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
	    border: 1px solid #ddd;
	}
	.table>thead>tr>th {
	    vertical-align: bottom;
	    border-bottom: 2px solid #ddd;
	}
	.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
	    padding: 8px;
	    line-height: 1.42857143;
	    vertical-align: top;
	    border-top: 1px solid #ddd;
	}
	.table-bordered, td, th {
	    border-radius: 0 !important;
	}
	caption, th {
	    text-align: left;
	}
	.table-striped>tbody>tr:nth-of-type(odd) {
	    background-color: #f9f9f9;
	}
	
	.btn.btn-app.no-radius > .badge, .btn.btn-app.radius-4 > .badge {
	    border-radius: 3px;
	}
	.btn.btn-app > .badge, .btn.btn-app > .label {
	    top: -2px;
	    right: -2px;
	    text-align: center;
	    font-size: 12px;
	    color: rgb(255, 255, 255);
	    position: absolute !important;
	    padding: 1px 3px;
	}
	.btn .badge, .btn .label {
	    top: -1px;
	    position: relative;
	}
	.btn-default .badge {
	    color: #fff;
	    background-color: #333;
	}
	.badge-success, .badge.badge-success, .label-success, .label.label-success {
	    background-color: rgb(130, 175, 111);
	}
	.badge-danger, .badge-important, .badge.badge-danger, .badge.badge-important, .label-danger, .label-important, .label.label-danger, .label.label-important {
	    background-color: rgb(209, 91, 71);
	}
	.badge {
	    padding-top: 1px;
	    padding-bottom: 3px;
	    line-height: 15px;
	}
	.badge, .label {
	    font-size: 12px;
	}
	.badge, .label {
	    font-weight: 400;
	    background-color: rgb(171, 186, 195);
	    text-shadow: none;
	}
	.badge {
	    display: inline-block;
	    min-width: 10px;
	    padding: 3px 7px;
	    font-size: 12px;
	    color: #fff;
	    vertical-align: middle;
	    background-color: #777;
	    border-radius: 10px;
	}
	.badge, .close, .label {
	    line-height: 1;
	}
	.badge, .label {
	    font-weight: 700;
	    white-space: nowrap;
	    text-align: center;
	}
</style>
<script src="/js/jQuery/jquery-3.4.1.min.js"></script>
<script type="text/javascript" src="/assets/layer/layer.js"></script>
<script type="text/javascript">
	function Urp() {
	};
	
	Urp.prototype = {
		"alert": function (msg, callback) {
			layer.msg(msg);
			if (typeof callback === 'function') {
				setTimeout(callback, 1000);
			}
		},
		"fixedheader": function (tableDivId, divHeight) {
			var h = (divHeight == undefined || divHeight == null) ? '\'450\'' : divHeight;
	
			if ($("#" + tableDivId).length > 0) {
				$("#" + tableDivId).addClass('table-cont');
				$("#" + tableDivId + " table").css({"margin-top": "-2px", "display": "table", "border-collapse": "separate"});
				//$("#"+tableDivId).css({"max-height":h,"overflow":"auto"});
				var tableCont = document.querySelector('#' + tableDivId);
	
				function scrollHandle(e) {
					var scrollTop = this.scrollTop - 1;
					this.querySelector('thead').style.transform = 'translateY(' + scrollTop + 'px)';
				}
	
				tableCont.addEventListener('scroll', scrollHandle);
			}
		}
	};
	urp = new Urp();

	var redisKey = "${redisKey}";
	var kcIds = "${kcIds}";
	var kcms = "${kcms}";
	var intervalf = 0;
	var times = 0;
	
	$(function(){
		var resCont = "";
		if(kcIds!="" || kcms!=""){
			var kcIdArr = kcIds.split(",");
			var xx = kcms.split(",");
			var kcm = "";
			for(var i=0;i<xx.length;i++){
				if(xx[i] != "") {
					kcm += String.fromCharCode(xx[i]);
				}
			}
			var kcmArr = kcm.split(",");
			for(var i=0;i<kcIdArr.length;i++){
				var ids = kcIdArr[i].split("_");
				resCont += "<tr><td width='35%' align='right'>"+kcmArr[i]+"</td><td id='"+ids[0]+"_"+ids[1]+"'><div class='none-result'></div></td></tr>";
			}
		}
		$("#xkresult").html(resCont);
		
		
		getXkResult();
		intervalf = setInterval(getXkResult,3000);
	});
	
	function getXkResult(){
		times++;
		$("#xkMsg").html("第"+times+"次获取选课结果...");
		$.ajax({
			url:"/student/courseSelect/selectResult/query",
			method:"post",
			data:"kcNum="+ $("#xkresult tr").length +"&redisKey=" + redisKey,
			dataType:"json",
			success:function(data) {
				let drs = data["result"];
				if(drs && drs.length > 0) {
					$.each(drs,function(i, it) {
						if(!it || it.length == 0) return true;
						var keyval = it.split(":");
						if($("#"+ keyval[0] + " div.none-result").length == 0) return true;
						
						var resC;
						if(keyval[1].indexOf("选课成功！") != -1) {							
							resC = "<span class='badge badge-success'>"+keyval[1].replace("已自动选中课程教材，可前往选课管理--教材--选定教材功能自行调整", "<span style='color: black'>已自动选中课程教材，可前往选课管理--教材--选定教材功能自行调整</span>");
							if(data["schoolId"]=="100010"){
								urp.alert("选课成功必须选教材！");
								resC +="<a href='/student/courseSelect/books/dealBooks/index'>去选教材</a>";
							} else if(data["schoolId"]=="100020" && ${dealType == '6' || dealType == '7'}){
								urp.alert("选课成功，请您尽快缴费！");
								resC +="<a href='/student/examinationManagement/notPayCost/index'>查看未缴费课程</a>";
							}
							resC +="</span>";
						} else {
							resC = "<span class='badge badge-danger'>"+keyval[1]+"</span>";
						}
						$("#"+keyval[0]).html(resC);
					});
				}
				if(data["isFinish"]) {
					window.clearInterval(intervalf);
					$("#tip").hide();
					$("#reget").remove();
					if(data["zxf"] > 0 && data["schoolId"]=="100027"){
						var tcount = "";
						$("#xm").text(data["xm"]);
						$("#zxf").text(data["zxf"]);
						$("#zje").text(data["zje"]);
						$.each(data["kclist"],function(i,v){
							tcount += "<tr>";
							tcount += "<td>"+(i+1)+"</td>";
							tcount += "<td>"+v[2]+"("+v[0]+")</td>";
							tcount += "<td>"+v[1]+"</td>";
							tcount += "<td>"+v[3]+"</td>";
							tcount += "<td>"+v[4]+"</td>";
							tcount += "</tr>";
						});
						$("#xfmxtbody").html(tcount);
						$("#xfmx").show();
					}
				}
			},
			error:function(){
				urp.alert("查询选课结果出错！");
			}
		});
		
		if(times==3){
			window.clearInterval(intervalf);
			$("#reget").removeClass("disabled");
			urp.alert("您选择的课程已提交，系统正在处理中。<br>请稍后从“选课结果”中查看结果。");
		}
	}
	
	function getAgein() {
		$("#reget").addClass("disabled");
		times = 0;
		getXkResult();
		intervalf = setInterval(getXkResult,5000);
	}
</script>
<style>
body {
	background-color: white;
}
.right_top_oper1 {
	float:right;
	font-size:14px;
	margin:0 20px;
	position: relative;
	top:-6px;
}
</style>
</head>
<body>
	<c:if test="${mobile}">
		<h5 class="phone-header smaller lighter grey" style="height: 38px; line-height: 38px; margin: -10px; top: -10px; position: relative;">
			<span class="ace-icon fa fa-angle-left bigger-130 phone-header-left" style="font-weight: bold; font-size: 20px !important; top: auto;" onclick="location.href = '/student/courseSelect/courseSelect/index?mobile=true'">&lt;</span>
			<span class="phone-header-center">选课</span>
		</h5>
	</c:if>
	<div class=".container-fluid">
		<div class="widget widget-table">
			<h4 class="header smaller lighter grey">
				<i class="fa fa-lightbulb-o"></i> ℹ️选课结果
				<c:if test="${!mobile}">
					<button type="button" class="btn btn-round btn-xs btn-info" onclick="location.href='/student/courseSelect/courseSelectNotice/index'">去选课公告</button>
					<button type="button" class="btn btn-round btn-xs btn-info" onclick="location.href='/student/courseSelect/courseSelect/index'">去选课</button>
					<button type="button" class="btn btn-round btn-xs btn-info" onclick="location.href='/student/courseSelect/quitCourse/index'">去退课</button>
					<button type="button" class="btn btn-round btn-xs btn-info" onclick="location.href='/student/courseSelect/courseSelectResult/index'">去看选课结果</button>
				</c:if>
				<span id="btn-submit-xk" class="right_top_oper1">
					<button title='重新获取' type="button" id="reget"
						class="btn btn-xs btn-round btn-warning disabled"
						onclick="getAgein();return false;">
						<i class="ace-icon fa fa-refresh bigger-120"></i> 重新获取
					</button>
					<c:if test="${!mobile}">
						<button title='返回' type="button"
							class="btn btn-xs btn-round btn-default"
							onclick="window.location.href='/student/courseSelect/courseSelect/index'">
							<i class="ace-icon fa fa-undo bigger-120"></i> 返回
						</button>
						<button type="button" class="btn btn-round btn-xs btn-info" onclick="location.href='/'">关闭</button>
					</c:if>
				</span>
			</h4>
		</div>
		
		<div id="tip" class="alert alert-block alert-success">
			<button type="button" class="close" data-dismiss="alert">
				<i class="ace-icon fa fa-times"></i>
			</button>
			<i class="ace-icon fa fa-info-circle green"></i>
			<span id="xkMsg">正在处理选课，请稍候...</span>
		</div>
		<div class="col-md-6 col-md-offset-3">
			<table class="table table-bordered table-striped">
				<thead>
					<tr>
						<th>课程名</th>
						<th>选课结果</th>
					</tr>
				<thead>
				<tbody id="xkresult"></tbody>
			</table>
		</div>
		<div class="col-xs-6 col-xs-offset-3" id = "xfmx" style="display: none;">
			<span id="xm"></span>同学，你好:根据学分制度收费管理规定,每学期学分学费=实际选课总学分*100，你本学期实际选课总学分为<span id="zxf"></span> ,学分学费为<span id="zje"></span>,望周知。具体缴费金额以财务处批扣为准.如有问题请咨询：教务处(6902412 )、财务处(6901915 )。
			<table id="xfmxtable" class="table table-bordered table-striped" width="100%">
				<thead>
				<tr>
					<th>序号</th>
					<th>课程</th>
					<th>课序号</th>
					<th>学分</th>
					<th>缴费金额</th>
				</tr>
				</thead>
				<tbody id="xfmxtbody">
				</tbody>
			</table>
		</div>
	</div>
</body>
</html>
