<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<!DOCTYPE html>
<html>
<head>
    <title>重、复、补修选课缴费</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="/wapjsp/css/mobile-framework.css">
    <link rel="stylesheet" href="/wapjsp/css/font-awesome.min.css">
    <script src="/wapjsp/js/jquery.min.js"></script>
    <script src="/wapjsp/js/layer.js"></script>
    <script src="/wapjsp/js/urp.js"></script>
    <script src="/js/customjs/coursetable.js"></script>
    <script src="/js/json/json2.js"></script>
    <style>
        .search-container {
            background: var(--card-background);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: var(--card-shadow);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 6px;
        }
        
        .form-input, .form-select {
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            background: var(--background-primary);
            color: var(--text-primary);
            min-height: 44px;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
        
        .search-button {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .search-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .search-button:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        .course-card {
            background: var(--card-background);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }
        
        .course-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--card-shadow-hover);
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .course-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            flex: 1;
            margin-right: 12px;
        }
        
        .course-code {
            font-size: 12px;
            color: var(--text-secondary);
            background: var(--background-secondary);
            padding: 4px 8px;
            border-radius: 6px;
            white-space: nowrap;
        }
        
        .course-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            font-size: 14px;
        }
        
        .info-label {
            color: var(--text-secondary);
            font-size: 12px;
            margin-bottom: 2px;
        }
        
        .info-value {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .course-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 12px;
            border-top: 1px solid var(--border-color);
        }
        
        .payment-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .payment-amount {
            font-size: 16px;
            font-weight: 600;
            color: var(--error-color);
        }
        
        .payment-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .status-unpaid {
            background: var(--error-light);
            color: var(--error-color);
        }
        
        .status-paid {
            background: var(--success-light);
            color: var(--success-color);
        }
        
        .course-type {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .type-cx {
            background: var(--warning-light);
            color: var(--warning-color);
        }
        
        .type-fx {
            background: var(--info-light);
            color: var(--info-color);
        }
        
        .type-bx {
            background: var(--primary-light);
            color: var(--primary-color);
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 8px;
            height: 20px;
            margin-bottom: 8px;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .pagination-mobile {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            padding: 16px;
            margin-top: 16px;
        }
        
        .pagination-button {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--background-primary);
            color: var(--text-primary);
            font-size: 14px;
            min-height: 44px;
            min-width: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .pagination-button.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .pagination-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .load-more-button {
            width: 100%;
            background: var(--background-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            margin-top: 16px;
            min-height: 44px;
        }
        
        .statistics-bar {
            background: var(--background-secondary);
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">重、复、补修选课缴费</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-mobile">
            <!-- 搜索条件 -->
            <div class="search-container">
                <form name="frm" method="POST" target="_parent">
                    <input type="hidden" id="tokenValue" value="${token_in_session}"/>
                    
                    <div class="search-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">缴费类型</label>
                                <select class="form-select" name="bmlx" id="bmlx">
                                    <option value="">--全部--</option>
                                    <option value="cx">重修</option>
                                    <option value="fx">复修</option>
                                    <option value="bx">补修</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">课程号</label>
                                <input type="text" class="form-input" id="kch" name="kch" placeholder="请输入课程号"/>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">课程名</label>
                            <input type="text" class="form-input" id="kcm" name="kcm" placeholder="请输入课程名"/>
                        </div>
                        
                        <button type="button" class="search-button" id="queryButton" onclick="searchCourses()">
                            <i class="fa fa-search"></i>
                            查询
                        </button>
                    </div>
                </form>
            </div>

            <!-- 统计信息 -->
            <div class="statistics-bar" id="statisticsBar" style="display: none;">
                <div class="stat-item">
                    <div class="stat-value" id="totalCount">0</div>
                    <div class="stat-label">总记录</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="unpaidCount">0</div>
                    <div class="stat-label">未缴费</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalAmount">¥0</div>
                    <div class="stat-label">总金额</div>
                </div>
            </div>

            <!-- 课程列表 -->
            <div class="section-mobile">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="glyphicon glyphicon-list"></i>
                        重、复、补修未缴费记录
                    </h3>
                </div>
                
                <div id="courseListContainer">
                    <!-- 加载中状态 -->
                    <div id="loadingState" class="loading-container">
                        <div class="course-card">
                            <div class="loading-skeleton" style="height: 20px; width: 60%;"></div>
                            <div class="loading-skeleton" style="height: 16px; width: 40%;"></div>
                            <div class="loading-skeleton" style="height: 16px; width: 80%;"></div>
                        </div>
                        <div class="course-card">
                            <div class="loading-skeleton" style="height: 20px; width: 70%;"></div>
                            <div class="loading-skeleton" style="height: 16px; width: 50%;"></div>
                            <div class="loading-skeleton" style="height: 16px; width: 90%;"></div>
                        </div>
                    </div>
                    
                    <!-- 课程列表容器 -->
                    <div id="courseList"></div>
                    
                    <!-- 空状态 -->
                    <div id="emptyState" class="empty-state" style="display: none;">
                        <div class="empty-icon">
                            <i class="fa fa-file-text-o"></i>
                        </div>
                        <p>暂无未缴费记录</p>
                        <small>请尝试调整搜索条件</small>
                    </div>
                    
                    <!-- 加载更多按钮 -->
                    <button id="loadMoreButton" class="load-more-button" onclick="loadMoreCourses()" style="display: none;">
                        <i class="fa fa-plus-circle"></i> 加载更多
                    </button>
                </div>
            </div>

            <!-- 分页容器 -->
            <div id="urppagebar" class="pagination-mobile"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let totalCount = 0;
        let courseData = [];
        let isLoading = false;
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            initializePage();
        });
        
        // 初始化页面
        function initializePage() {
            // 加载初始数据
            getResultsList(1, "10_sl", true);
            
            // 绑定回车搜索
            $('.form-input').on('keypress', function(e) {
                if (e.which === 13) {
                    searchCourses();
                }
            });
        }
        
        // 搜索课程
        function searchCourses() {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            currentPage = 1;
            getResultsList(1, "10_sl", true);
        }
        
        // 刷新数据
        function refreshData() {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            currentPage = 1;
            getResultsList(1, "10_sl", true);
        }
        
        // 加载更多课程
        function loadMoreCourses() {
            if (isLoading || currentPage * pageSize >= totalCount) return;
            
            currentPage++;
            getResultsList(currentPage, "10_sl", false);
        }
        
        // 获取结果列表
        function getResultsList(page, pageSizeParam, conditionChanged) {
            if (isLoading) return;
            
            isLoading = true;
            const params = $(document.frm).serialize();
            const url = "/student/examinationManagement/notPayCost/selectCourse/queryAll";
            
            // 显示加载状态
            if (conditionChanged) {
                showLoading();
            } else {
                showLoadMoreLoading();
            }
            
            $.ajax({
                url: url,
                cache: false,
                data: params,
                type: "post",
                dataType: "json",
                success: function (d) {
                    isLoading = false;
                    hideLoading();
                    
                    const data = d.data;
                    totalCount = data["pageContext"].totalCount;
                    
                    if (data["records"] != null && data["records"].length != 0) {
                        const isScroll = pageSizeParam.indexOf("_") != -1 && page != 1;
                        fillResultsTable(data["records"], isScroll, page, pageSizeParam);
                        updateStatistics(data["records"]);
                    } else {
                        if (conditionChanged) {
                            showEmptyState();
                        }
                    }
                    
                    updatePagination();
                },
                error: function (xhr) {
                    isLoading = false;
                    hideLoading();
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                }
            });
        }
        
        // 填充结果表格
        function fillResultsTable(data, isScroll, page, pageSizeParam) {
            let html = '';
            
            data.forEach(function(course, index) {
                const tableId = isScroll ? (page - 1) * pageSize + 1 + index : index + 1;
                html += generateCourseCard(course, tableId);
            });
            
            if (isScroll) {
                $('#courseList').append(html);
            } else {
                $('#courseList').html(html);
                $('#courseList').show();
                $('#emptyState').hide();
            }
        }
        
        // 生成课程卡片HTML
        function generateCourseCard(course, index) {
            const courseCode = course.KCH || '';
            const courseName = course.KCM || '';
            const courseSeq = course.KXH || '';
            const credits = course.XF || '';
            const semester = course.ZXJXJHM || '';
            const paymentType = course.BMLX || '';
            const amount = course.JFJE || '';
            const paymentStatus = course.JFZT || '';
            
            const typeClass = paymentType === 'cx' ? 'type-cx' : 
                             paymentType === 'fx' ? 'type-fx' : 
                             paymentType === 'bx' ? 'type-bx' : '';
            
            const statusClass = paymentStatus === 'Y' ? 'status-paid' : 'status-unpaid';
            const statusText = paymentStatus === 'Y' ? '已缴费' : '未缴费';
            
            return `
                <div class="course-card" data-course-code="${courseCode}">
                    <div class="course-header">
                        <h4 class="course-title">${courseName}</h4>
                        <span class="course-code">${courseCode}</span>
                    </div>
                    
                    <div class="course-info">
                        <div class="info-item">
                            <span class="info-label">学年学期</span>
                            <span class="info-value">${semester}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">课序号</span>
                            <span class="info-value">${courseSeq}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">学分</span>
                            <span class="info-value">${credits}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">序号</span>
                            <span class="info-value">${index}</span>
                        </div>
                    </div>
                    
                    <div class="course-footer">
                        <div class="payment-info">
                            <div class="payment-amount">¥${amount}</div>
                            <span class="payment-status ${statusClass}">${statusText}</span>
                        </div>
                        <span class="course-type ${typeClass}">${paraseJfLX(paymentType)}</span>
                    </div>
                </div>
            `;
        }
        
        // 解析缴费类型
        function paraseJfLX(jflx) {
            if ("cx" == jflx) {
                return "重修";
            } else if ("fx" == jflx) {
                return "复修";
            } else if ("bx" == jflx) {
                return "补修";
            } else {
                return "";
            }
        }
        
        // 更新统计信息
        function updateStatistics(data) {
            let unpaidCount = 0;
            let totalAmount = 0;
            
            data.forEach(function(course) {
                if (course.JFZT === 'N') {
                    unpaidCount++;
                }
                totalAmount += parseFloat(course.JFJE || 0);
            });
            
            $('#totalCount').text(totalCount);
            $('#unpaidCount').text(unpaidCount);
            $('#totalAmount').text('¥' + totalAmount.toFixed(2));
            $('#statisticsBar').show();
        }
        
        // 更新分页
        function updatePagination() {
            const hasMore = currentPage * pageSize < totalCount;
            
            if (hasMore) {
                $('#loadMoreButton').show();
            } else {
                $('#loadMoreButton').hide();
            }
        }
        
        // 显示加载状态
        function showLoading() {
            $('#loadingState').show();
            $('#courseList').hide();
            $('#emptyState').hide();
            $('#statisticsBar').hide();
        }
        
        // 显示加载更多状态
        function showLoadMoreLoading() {
            $('#loadMoreButton').html('<i class="fa fa-spinner fa-spin"></i> 加载中...');
            $('#loadMoreButton').prop('disabled', true);
        }
        
        // 隐藏加载状态
        function hideLoading() {
            $('#loadingState').hide();
            $('#loadMoreButton').html('<i class="fa fa-plus-circle"></i> 加载更多');
            $('#loadMoreButton').prop('disabled', false);
        }
        
        // 显示空状态
        function showEmptyState() {
            $('#loadingState').hide();
            $('#courseList').hide();
            $('#emptyState').show();
            $('#statisticsBar').hide();
        }
        
        // 显示错误信息
        function showError(message) {
            layer.alert(message, {
                icon: 2,
                title: '错误提示',
                skin: 'layer-mobile'
            });
        }
        
        // 页面可见性变化时刷新数据
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                setTimeout(refreshData, 500);
            }
        });
    </script>
</body>
</html>
