<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>项目终止申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 项目终止申请页面样式 */
        .termination-innovation-header {
            background: linear-gradient(135deg, var(--primary-color), var(--error-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .termination-innovation-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .termination-innovation-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .time-info-section {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--info-dark);
            font-size: var(--font-size-small);
            line-height: 1.5;
        }
        
        .action-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-add-application {
            width: 100%;
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-add-application:hover {
            background: var(--success-dark);
        }
        
        .btn-add-application:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .applications-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .applications-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .applications-section-title i {
            color: var(--error-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-index {
            background: var(--error-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .application-content {
            flex: 1;
        }
        
        .project-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            align-items: flex-start;
        }
        
        .detail-label {
            font-weight: 500;
            min-width: 60px;
            margin-right: var(--margin-sm);
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
            word-break: break-word;
        }
        
        .termination-reason {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-application-action {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-revoke {
            background: var(--error-color);
            color: white;
        }
        
        .btn-export {
            background: var(--warning-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .warning-alert {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .warning-alert i {
            color: var(--warning-color);
            font-size: 20px;
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .termination-innovation-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .time-info-section,
            .action-section,
            .applications-section,
            .warning-alert {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .application-details {
                grid-template-columns: 1fr;
            }
            
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .detail-value {
                text-align: left;
                margin-top: 4px;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">项目终止申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 项目终止申请头部 -->
        <div class="termination-innovation-header">
            <div class="termination-innovation-title">项目终止申请</div>
            <div class="termination-innovation-desc">管理您的项目终止申请记录</div>
        </div>
        
        <!-- 申请时间信息 -->
        <c:if test="${flag == 'showAdd'}">
            <div class="time-info-section">
                <strong>申请时间：</strong>
                ${fn:substring(kzkg.kssj, 0, 4)}-${fn:substring(kzkg.kssj, 4, 6)}-${fn:substring(kzkg.kssj, 6, 8)} ${fn:substring(kzkg.kssj, 8, 10)}:${fn:substring(kzkg.kssj, 10, 12)}:${fn:substring(kzkg.kssj, 12, 14)}~${fn:substring(kzkg.jssj, 0, 4)}-${fn:substring(kzkg.jssj, 4, 6)}-${fn:substring(kzkg.jssj, 6, 8)} ${fn:substring(kzkg.jssj, 8, 10)}:${fn:substring(kzkg.jssj, 10, 12)}:${fn:substring(kzkg.jssj, 12, 14)}
            </div>
        </c:if>
        
        <!-- 状态提示信息 -->
        <c:if test="${flag == 'nonparametric'}">
            <div class="warning-alert">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>项目终止参数未维护，请联系管理员处理</span>
            </div>
        </c:if>
        
        <c:if test="${flag == 'notenabled'}">
            <div class="warning-alert">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>项目终止未启用，请联系管理员处理</span>
            </div>
        </c:if>
        
        <c:if test="${flag == 'nottime'}">
            <div class="warning-alert">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>不在可项目终止时间范围或项目终止开关关闭</span>
            </div>
        </c:if>
        
        <!-- 添加申请按钮 -->
        <c:if test="${flag == 'showAdd'}">
            <div class="action-section">
                <button class="btn-add-application" onclick="addApply();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>新增申请</span>
                </button>
            </div>
        </c:if>
        
        <!-- 申请列表 -->
        <div class="applications-section">
            <div class="applications-section-header">
                <div class="applications-section-title">
                    <i class="ace-icon fa fa-list"></i>
                    项目终止申请列表
                </div>
            </div>
            
            <div id="applicationsList">
                <!-- 动态加载申请列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreApplications();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-stop-circle"></i>
            <div class="empty-state-title">暂无申请记录</div>
            <div class="empty-state-desc">您还没有提交任何项目终止申请</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let applicationData = [];

        $(function() {
            initPage();
            getPageList(1, "30_sl", true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 分页查询
        function getPageList(page, pageSizeVal, conditionChanged) {
            if (pageSizeVal == undefined) {
                pageSizeVal = "30_sl";
                page = "1";
            }

            var parr = (pageSizeVal + "").split("_");
            var pageSize = parseInt(parr[0]);

            showLoading(true);

            const url = "/students/studentsInnovation/terminationInnovationApplication/queryPageInfo";

            $.ajax({
                url: url,
                cache: false,
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records) {
                        applicationData = data.records;
                        totalCount = data.pageContext ? data.pageContext.totalCount : 0;

                        if (applicationData.length > 0) {
                            renderApplications();
                        } else {
                            showEmptyState();
                        }
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplications() {
            const container = $('#applicationsList');
            container.empty();

            if (applicationData.length === 0) {
                showEmptyState();
                return;
            }

            applicationData.forEach(function(application, index) {
                const applicationHtml = createApplicationItem(application, index);
                container.append(applicationHtml);
            });

            hideEmptyState();
        }

        // 创建申请项目HTML
        function createApplicationItem(application, index) {
            return `
                <div class="application-item">
                    <div class="application-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="application-index">${index + 1}</div>
                            <div class="application-content">
                                <div class="project-name">${application.XMMC || ''}</div>
                            </div>
                        </div>
                    </div>

                    <div class="application-details">
                        <div class="detail-item">
                            <span class="detail-label">申请时间</span>
                            <span class="detail-value">${application.SQSJ || ''}</span>
                        </div>
                    </div>

                    ${application.ZZYY ? `
                        <div class="termination-reason">
                            <strong>终止原因：</strong>${application.ZZYY}
                        </div>
                    ` : ''}

                    <div class="application-actions">
                        ${getActionButtons(application)}
                    </div>
                </div>
            `;
        }

        // 获取操作按钮
        function getActionButtons(application) {
            let buttons = [];

            // 查看按钮
            if (application.JSSPJL != '待审批' || application.YXSPJL != '待审批' || application.JWCSPJL != '待审批') {
                buttons.push(`
                    <button class="btn-application-action btn-view" onclick="seeInfo('${application.ZZID}', '${application.APPLY_STATUS}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看</span>
                    </button>
                `);
            }

            // 修改和撤回按钮
            if ((application.JSSPJL == '待审批' && application.YXSPJL == '待审批' && application.JWCSPJL == '待审批') ||
                (application.JSSPJL == '不通过' || application.YXSPJL == '不通过' || application.JWCSPJL == '不通过')) {
                buttons.push(`
                    <button class="btn-application-action btn-edit" onclick="openEditPage('${application.ZZID}', 'oneApply');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                `);
                buttons.push(`
                    <button class="btn-application-action btn-revoke" onclick="revokeInfo('${application.ZZID}');">
                        <i class="ace-icon fa fa-reply"></i>
                        <span>撤回</span>
                    </button>
                `);
            }

            // 导出按钮
            buttons.push(`
                <button class="btn-application-action btn-export" onclick="exportTerminationSqFile('${application.ZZID}');">
                    <i class="ace-icon fa fa-download"></i>
                    <span>导出</span>
                </button>
            `);

            return buttons.join('');
        }

        // 查看数据
        function seeInfo(sqbh) {
            const url = "/student/application/index/seeInfo?applyId=" + sqbh + "&applyType=CHXM2";

            if (parent && parent.addTab) {
                parent.addTab('查看申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 撤销数据
        function revokeInfo(sqbh) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm("确定要撤销申请？", function(confirmed) {
                    if (confirmed) {
                        doRevokeApplication(sqbh);
                    }
                });
            } else {
                if (confirm("确定要撤销申请？")) {
                    doRevokeApplication(sqbh);
                }
            }
        }

        // 执行撤销申请
        function doRevokeApplication(sqbh) {
            showLoading(true);

            $.ajax({
                url: "/students/studentsInnovation/terminationInnovationApplication/index/revokeInfo",
                type: "post",
                data: "zzid=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    $("#tokenValue").val(data.token);
                    if (data.result.indexOf("/") !== -1) {
                        window.location.href = data.result;
                    } else {
                        if (data.result === "success") {
                            showSuccess("撤销成功！");
                            getPageList(1, "30_sl", true);
                        } else {
                            showError(data.result);
                        }
                    }
                },
                error: function() {
                    showError("撤销失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 导出项目终止申请表
        function exportTerminationSqFile(sqid) {
            const name = encodeURIComponent("/students/studentsInnovation/terminationInnovationApplication/index/exportTerminationApplyFile?zzid=" + sqid);
            const url = "/pdf/web/viewer.html?file=" + name;

            if (parent && parent.addTab) {
                parent.addTab('导出申请表', url);
            } else {
                window.open(url, '_blank');
            }
        }

        // 新增申请
        function addApply() {
            showLoading(true);

            const url = "/students/studentsInnovation/terminationInnovationApplication/checkaddApply";

            $.ajax({
                url: url,
                cache: false,
                type: "post",
                dataType: "json",
                success: function(response) {
                    if (response.msg) {
                        showError(response.msg);
                    } else {
                        if (response.sfxyd == "1") {
                            // 需要显示申请须知
                            showApplicationNotice(response);
                        } else {
                            openEditPage('');
                        }
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示申请须知
        function showApplicationNotice(data) {
            let noticeHtml = `
                <div style="background: var(--bg-primary); border-radius: 8px; padding: var(--padding-md); margin: var(--margin-md);">
                    <h3 style="margin-bottom: var(--margin-md); color: var(--text-primary);">申请须知</h3>
                    <div style="margin-bottom: var(--margin-md); line-height: 1.6;">${data.ydnr}</div>
                    <div style="text-align: center;">
                        <button onclick="proceedToApplication();" style="background: var(--info-color); color: white; border: none; padding: var(--padding-sm) var(--padding-lg); border-radius: 6px; margin-right: var(--margin-sm);">
                            继续申请<span id="djs"></span>
                        </button>
                        <button onclick="closeNotice();" style="background: var(--text-disabled); color: white; border: none; padding: var(--padding-sm) var(--padding-lg); border-radius: 6px;">
                            关闭
                        </button>
                    </div>
                </div>
            `;

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(noticeHtml);
            } else {
                // 简单的alert显示
                if (confirm("申请须知:\n" + data.ydnr + "\n\n是否继续申请？")) {
                    openEditPage('');
                }
            }

            // 倒计时处理
            if (data.qzydms > 0) {
                let qzydms = data.qzydms;
                $("#djs").html("（" + qzydms + "s）");
                const interval = setInterval(function() {
                    qzydms--;
                    if (qzydms == 0) {
                        clearInterval(interval);
                        $("#djs").html("");
                        $("#djs").parents("button").attr("disabled", false);
                    } else {
                        $("#djs").parents("button").attr("disabled", true);
                        $("#djs").html("（" + qzydms + "s）");
                    }
                }, 1000);
            }
        }

        // 继续申请
        function proceedToApplication() {
            closeNotice();
            setTimeout(function() {
                openEditPage('');
            }, 500);
        }

        // 关闭须知
        function closeNotice() {
            // 关闭弹窗的逻辑
            if (typeof urp !== 'undefined' && urp.closeAlert) {
                urp.closeAlert();
            }
        }

        // 打开编辑页面
        function openEditPage(zzid) {
            const url = "/students/studentsInnovation/terminationInnovationApplication/toEdit?zzid=" + zzid;

            if (parent && parent.addTab) {
                parent.addTab(zzid ? '修改申请' : '新增申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 显示空状态
        function showEmptyState() {
            $('#emptyState').show();
            $('#loadMoreContainer').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 返回首页
        function returnIndex() {
            location.href = "/student/application/index";
        }

        // 刷新数据
        function refreshData() {
            getPageList(1, "30_sl", true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
