package educationalAdministration.student.courseSelectMangement.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.code.kaptcha.Constants;
import com.rabbitMq.SendMessage;
import com.rabbitMq.XkToRabbitObject;
import com.rabbitMq.exception.SendRefuseException;
import com.urpSoft.business.createtree.TreeNode;
import com.urpSoft.business.utils.UrpResult;
import com.urpSoft.core.cache.redis.RedisUtil;
import com.urpSoft.core.data.orm.dao.IBaseDao;
import com.urpSoft.core.data.orm.jpql.param.Param;
import com.urpSoft.core.data.query.component.QueryInfo;
import com.urpSoft.core.license.LicenseManger;
import com.urpSoft.core.pagination.page.Page;
import com.urpSoft.core.pagination.service.IPageService;
import com.urpSoft.core.util.AuthUtil;
import com.urpSoft.core.util.CSRFToken;
import com.urpSoft.core.web.aware.SessionAware;

import educationalAdministration.dictionary.entity.CodeKcb;
import educationalAdministration.dictionary.entity.CodeTermSection;
import educationalAdministration.dictionary.entity.ExecutiveEducationPlanView;
import educationalAdministration.dictionary.entity.SessionTimeTable;
import educationalAdministration.dictionary.entity.XkJdView;
import educationalAdministration.student.common.service.CommonService;
import educationalAdministration.student.common.utils.CommonUtils;
import educationalAdministration.student.courseSelectMangement.entity.CourseTimeAndPlaceView;
import educationalAdministration.student.courseSelectMangement.entity.Kcsjdd;
import educationalAdministration.student.courseSelectMangement.entity.KwBmjfKcb;
import educationalAdministration.student.courseSelectMangement.entity.KwBmjfKcbPk;
import educationalAdministration.student.courseSelectMangement.entity.RwCxxk;
import educationalAdministration.student.courseSelectMangement.entity.RwRxk;
import educationalAdministration.student.courseSelectMangement.entity.RwXarxk;
import educationalAdministration.student.courseSelectMangement.entity.RwXarxkTjgy;
import educationalAdministration.student.courseSelectMangement.entity.RwXirxk;
import educationalAdministration.student.courseSelectMangement.entity.Rwfa;
import educationalAdministration.student.courseSelectMangement.entity.Rwjh;
import educationalAdministration.student.courseSelectMangement.entity.SelectCourseTableView;
import educationalAdministration.student.courseSelectMangement.entity.StudentTeachingSchoolView;
import educationalAdministration.student.courseSelectMangement.entity.SyXkbView;
import educationalAdministration.student.courseSelectMangement.entity.WeekSessionsTable;
import educationalAdministration.student.courseSelectMangement.entity.XkCxXnxqView;
import educationalAdministration.student.courseSelectMangement.entity.XkXkwcgbView;
import educationalAdministration.student.courseSelectMangement.entity.XsPybView;
import educationalAdministration.student.courseSelectMangement.entity.XsPybViewId;
import educationalAdministration.student.courseSelectMangement.entity.XsxkView;
import educationalAdministration.student.courseSelectMangement.service.CourseManagementService;
import educationalAdministration.student.courseSelectMangement.service.CurriculumTableService;
import educationalAdministration.student.myAttention.entity.XxfbTzView;
import educationalAdministration.student.personalManagement.entity.CodeKcsxb;
import educationalAdministration.student.personalManagement.entity.PxCsb;

@Controller
public class CourseManagementController {

	@Resource
	private CourseManagementService courseManagementService;

	@Resource
	private CurriculumTableService curriculumTableService;

	@Resource
	private IBaseDao baseDao;

	private CSRFToken csrfToken = CSRFToken.getInstance();

	@Resource
	private IPageService pageService;

	@Resource
	private CommonService commonService;

	private static Log log = LogFactory.getLog(CourseManagementController.class);

	/**
	 * 选方案页面
	 */
	@RequestMapping(value = {"/student/courseSelect/courseSelect/index","/tjgydx/student/courseSelect/courseSelect/index"})
	public String selectFa(Model model, HttpSession session, String mobile) {
		model.addAttribute("mobile", mobile);
		// 获得用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();

		// 获得执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();

		// 学生学籍信息
		StudentTeachingSchoolView jxxj = courseManagementService.queryStudentTeachingSchool(id);

		// 选课阶段
		XkJdView jdb = courseManagementService.queryJd(zxjxjhh);

		// 学生培养信息
		List<XsPybView> pyblist = courseManagementService.queryXsPyb(id, jdb);

		int pynum = pyblist.size();

		// 星期节次
		WeekSessionsTable xqjc = courseManagementService.queryWeekSession(zxjxjhh);

		// 节次时间
		List<SessionTimeTable> jcsj = courseManagementService.querySessionTimeTable(zxjxjhh);

		// 是否可以选课
		String err = courseManagementService.checkXk(id, jxxj, pyblist, jdb, zxjxjhh, xqjc, jcsj, session, "xk");

		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		String cktz = jdb.getCktz();
		if("1".equals(cktz)) {
			List<XxfbTzView> tzViews = courseManagementService.queryTzView();
			if(CollectionUtils.isNotEmpty(tzViews)) {
				String tzId = tzViews.get(0).getTzid();
				String qzdt = jdb.getQzdt();
				qzdt = StringUtils.isBlank(qzdt) ? "0" : qzdt;
				return "forward:/student/courseSelect/viewSelectCoursePaper/index?qzdt=" + qzdt + "&tzId=" + tzId;
			}
		}
		if (pynum == 0) {
			model.addAttribute("err", "未查询到可以选课的方案，请联系管理员处理！");
			return "student/courseSelectManagement/error";
		}
		if (pynum == 1 && (StringUtils.isBlank(mobile) || !"true".equals(mobile))) {
			session.setAttribute("xk_fajhh", pyblist.get(0).getId().getFajhh());
			return "forward:/student/courseSelect/selectCourse/index?xxbm=" + 
			session.getAttribute("xxbm") + "&xkjdlx=" + session.getAttribute("xkjdlx");
		}

		model.addAttribute("zxjxjhh", zxjxjhh);
		model.addAttribute("pyblist", JSONArray.fromObject(pyblist).toString().replaceAll("null", "\"\"").replaceAll("\\\\r",""));
		if(StringUtils.isNotBlank(mobile) && "true".equals(mobile)) {
			//是否显示方案选课
			String xsfaxk = "";
			PxCsb pxcs = courseManagementService.loadPxcs("web_xkjmcs","xsfaxk");
			if (pxcs != null && pxcs.getCsz() != null && pxcs.getCsz().equals("0")) {
				xsfaxk = "0";
			}

			//是否显示自由选课
			String xszyxk = "";
			pxcs = courseManagementService.loadPxcs("web_xkjmcs","xszyxk");
			if (pxcs != null && pxcs.getCsz() != null && pxcs.getCsz().equals("0")) {
				xszyxk = "0";
			}

			//是否显示复修选课
			String xsfxxk = "";
			pxcs = courseManagementService.loadPxcs("xkgl","fxxkkg");
			if (pxcs != null && pxcs.getCsz() != null && pxcs.getCsz().equals("1")) {
				xsfxxk = "1";
			}
			
			//是否只允许重修复修选课
			model.addAttribute("yxcxfx", "0");
			err = courseManagementService.checkCxkc();
			if (err != null) {
				model.addAttribute("yxcxfx", "1");
			}
			model.addAttribute("xsfaxk", xsfaxk);
			model.addAttribute("xszyxk", xszyxk);
			model.addAttribute("xsfxxk", xsfxxk);
			model.addAttribute("xxbm", session.getAttribute("xxbm"));
			model.addAttribute("xkjdlx", session.getAttribute("xkjdlx"));
			return "student/courseSelectManagement/mobile/page1";
		} else {
			return "student/courseSelectManagement/selectFa";
		}
	}

	@RequestMapping("/student/courseSelect/viewSelectCoursePaper/index")
	public String viewSelectCoursePaperIndex(Model model, String qzdt, String tzId, String mobile) {
		model.addAttribute("qzdt", qzdt);
		model.addAttribute("mobile", mobile);
		if("1".equals(qzdt)) {
			Number num = courseManagementService.queryHasPass(tzId);
			if(num.intValue() == 0) {
				model.addAttribute("hasPass", "0");
				model.addAttribute("question", JSONArray.fromObject(courseManagementService.queryQuestion(tzId)));
			} else {
				model.addAttribute("hasPass", "1");
			}
		}
		XxfbTzView xxfbTzView = commonService.queryEntityById(XxfbTzView.class, tzId);
		model.addAttribute("xxfbTzView", xxfbTzView);
		return "/student/courseSelectManagement/selectCourseNoticeDetail";
	}

	@RequestMapping("/student/courseSelect/viewSelectCoursePaper/checkDa")
	@ResponseBody
	public Map<String, Object> checkDa(String info, HttpServletRequest request) {
		Map<String, Object> map = new HashMap<String, Object>();
		String ip = CommonUtils.getRemoteHost(request);
		map.put("result", courseManagementService.checkDa(info, ip));
		return map;
	}

	@RequestMapping("/student/courseSelect/gotoSelect/index")
	public String gotoSelect(Model model, HttpSession session, String mobile) {
		model.addAttribute("mobile", mobile);
		// 获得执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();
		// 选课阶段
		XkJdView jdb = courseManagementService.queryJd(zxjxjhh);
		String id = AuthUtil.getCurrentUser().getIdNumber();
		String cktz = jdb.getCktz();
		String qzdt = jdb.getQzdt();

		if("1".equals(cktz) && "1".equals(qzdt)) {
			List<XxfbTzView> tzViews = courseManagementService.queryTzView();
			Number num = courseManagementService.queryHasPass(tzViews.get(0).getTzid());
			if(num.intValue() == 0) {
				model.addAttribute("err", "当前选课需答题，您还没有答过哦！");
				return "student/courseSelectManagement/error";
			}
		}

		// 学生培养信息
		List<XsPybView> pyblist = courseManagementService.queryXsPyb(id, jdb);
		int pynum = pyblist.size();

		if (pynum == 1 && (StringUtils.isBlank(mobile) || !"true".equals(mobile))) {
			session.setAttribute("xk_fajhh", pyblist.get(0).getId().getFajhh());
			return "forward:/student/courseSelect/selectCourse/index?xxbm=" + 
			session.getAttribute("xxbm") + "&xkjdlx=" + session.getAttribute("xkjdlx");
		}

		model.addAttribute("zxjxjhh", zxjxjhh);
		model.addAttribute("pyblist", JSONArray.fromObject(pyblist).toString().replaceAll("null", "\"\"").replaceAll("\\\\r",""));

		if(StringUtils.isNotBlank(mobile) && "true".equals(mobile)) {
			//是否显示方案选课
			String xsfaxk = "";
			PxCsb pxcs = courseManagementService.loadPxcs("web_xkjmcs","xsfaxk");
			if (pxcs != null && pxcs.getCsz() != null && pxcs.getCsz().equals("0")) {
				xsfaxk = "0";
			}

			//是否显示自由选课
			String xszyxk = "";
			pxcs = courseManagementService.loadPxcs("web_xkjmcs","xszyxk");
			if (pxcs != null && pxcs.getCsz() != null && pxcs.getCsz().equals("0")) {
				xszyxk = "0";
			}

			//是否显示复修选课
			String xsfxxk = "";
			pxcs = courseManagementService.loadPxcs("xkgl","fxxkkg");
			if (pxcs != null && pxcs.getCsz() != null && pxcs.getCsz().equals("1")) {
				xsfxxk = "1";
			}
			
			//是否只允许重修复修选课
			model.addAttribute("yxcxfx", "0");
			String err = courseManagementService.checkCxkc();
			if (err != null) {
				model.addAttribute("yxcxfx", "1");
			}
			model.addAttribute("xsfaxk", xsfaxk);
			model.addAttribute("xszyxk", xszyxk);
			model.addAttribute("xsfxxk", xsfxxk);
			model.addAttribute("xxbm", session.getAttribute("xxbm"));
			model.addAttribute("xkjdlx", session.getAttribute("xkjdlx"));
			return "student/courseSelectManagement/mobile/page1";
		} else {
			return "student/courseSelectManagement/selectFa";
		}
	}
	
	/**
	 * 选课程页面
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/student/courseSelect/selectCourse/index")
	public String selectKc(Model model, HttpServletRequest request,
			HttpSession session, String xxbm, String xkjdlx, String mobile, String xkfs) {
		model.addAttribute("xxbm", xxbm);
		model.addAttribute("xkjdlx", xkjdlx);
		model.addAttribute("xkfs", xkfs);
		model.addAttribute("mobile", mobile);

		// 获得用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();

		// 方案计划号
		String fajhh = request.getParameter("fajhh");

		if (fajhh == null) {
			fajhh = (String) session.getAttribute("xk_fajhh");
		}
		// 执行教学计划
		ExecutiveEducationPlanView plan = courseManagementService.queryExecutiveEducationPlan();

		Number num = courseManagementService.queryCheckCt(plan.getExecutiveEducationPlanNumber(), id, fajhh);
		if(num.intValue() > 0) {
			model.addAttribute("checkCt", "1");
		}

		// 培养方案
		XsPybView pyb = courseManagementService.queryXsPybById(new XsPybViewId(id, fajhh));
		
		//是否显示方案选课
		String xsfaxk = "";
		PxCsb pxcs = courseManagementService.loadPxcs("web_xkjmcs","xsfaxk");
		if (pxcs != null && pxcs.getCsz() != null && pxcs.getCsz().equals("0")) {
			xsfaxk = "0";
		}

		//是否显示自由选课
		String xszyxk = "";
		pxcs = courseManagementService.loadPxcs("web_xkjmcs","xszyxk");
		if (pxcs != null && pxcs.getCsz() != null && pxcs.getCsz().equals("0")) {
			xszyxk = "0";
		}

		//是否显示复修选课
		String xsfxxk = "";
		pxcs = courseManagementService.loadPxcs("xkgl","fxxkkg");
		if (pxcs != null && pxcs.getCsz() != null && pxcs.getCsz().equals("1")) {
			xsfxxk = "1";
		}
		
		//是否显示按方案提交选课
		String xsafatjxk = "";
		pxcs = courseManagementService.loadPxcs("xkgl","xsafatjxk");
		if (pxcs != null && pxcs.getCsz() != null && pxcs.getCsz().equals("1")) {
			xsafatjxk = "1";
		}
		
		if("true".equals(mobile)) {
			String err = courseManagementService.checkCxkc();
			if ((err != null || ("005".equals(xkjdlx) && "100010".equals(xxbm))) && !"cxxk".equals(xkfs) && !"fxxk".equals(xkfs)) {
				model.addAttribute("err", err);
				return "student/courseSelectManagement/error";
			} else {
				if ("0".equals(xsfaxk) && "faxk".equals(xkfs)) {
					model.addAttribute("err", "不允许方案选课");
					return "student/courseSelectManagement/error";
				}
				if ("0".equals(xszyxk) && "zyxk".equals(xkfs)) {
					model.addAttribute("err", "不允许自由选课");
					return "student/courseSelectManagement/error";
				}
				if (!"1".equals(xsfxxk) && "fxxk".equals(xkfs)) {
					model.addAttribute("err", "不允许复修选课");
					return "student/courseSelectManagement/error";
				}
			}
			
			if("faxk".equals(xkfs)) {
				List<Object[]> kzList = courseManagementService.queryFaKzList(fajhh);
				model.addAttribute("kzList", kzList);
				
				String key = "";
				List<CodeKcsxb> kcsxlist = null;
				List<XkCxXnxqView> xnxqlist = null;
				String jsonSection = null;
				try {
					key = "xk_pad_kcsxlb";
					if (RedisUtil.exists(key.getBytes())) {
						kcsxlist = (List<CodeKcsxb>) RedisUtil.unserialize(RedisUtil.get(key.getBytes()));
					} else {
						// 课程属性集合
						kcsxlist = courseManagementService.queryKcsxList();
						RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(kcsxlist));
					}

					key = "xk_pad_xnxq" + fajhh;
					if (RedisUtil.exists(key.getBytes())) {
						xnxqlist = (List<XkCxXnxqView>) RedisUtil.unserialize(RedisUtil.get(key.getBytes()));
					} else {
						// 学年学期集合
						xnxqlist = courseManagementService.queryXnxqList(fajhh);
						RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(xnxqlist));
					}

					key = "xk_pad_currentSectionTime";
					if (RedisUtil.exists(key)) {
						jsonSection = RedisUtil.get(key);
					} else {
						// 获取星期节次时间信息
						CodeTermSection section = courseManagementService.getSection(null);
						jsonSection = JSONObject.fromObject(section).toString();
						RedisUtil.setex(key.getBytes(), 6*60*60, jsonSection.getBytes());
					}
				} catch (Exception e) {
					e.printStackTrace();
				}

				model.addAttribute("kcsxlist", kcsxlist);
				model.addAttribute("xnxqlist", xnxqlist);
			}
			
			if("cxxk".equals(xkfs)) {
				PxCsb csb = courseManagementService.loadPxcs("xkgl", "byscxpdct");
				model.addAttribute("byscxpdct", csb==null ? "" : csb.getCsz());
				
				csb = courseManagementService.loadPxcs("xkgl", "cxctpdfs");
				model.addAttribute("cxpctfs", csb==null ? "" : csb.getCsz());
			}
			if("fxxk".equals(xkfs)) {
				PxCsb csb = courseManagementService.loadPxcs("xkgl", "fxpdct");
				model.addAttribute("fxpct", csb.getCsz());
				
				csb = courseManagementService.loadPxcs("xkgl", "fxpdkyl");
				model.addAttribute("pkyl", csb == null || StringUtils.isBlank(csb.getCsz()) ? "1" : csb.getCsz());
			}
		}

		//是否显示验证码
		String xsyzm = "";
		pxcs = courseManagementService.loadPxcs("xk","xktjjyyzm");

		if (pxcs != null && pxcs.getCsz() != null && !pxcs.getCsz().equals("0")) {
			try {
				String submitTimeKey = "submitTime_" + id;
				if(RedisUtil.exists(submitTimeKey)) {
					int st = Integer.valueOf(RedisUtil.get(submitTimeKey));
					if(st >= Integer.valueOf(pxcs.getCsz())) {
						xsyzm = "1";
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		model.addAttribute("fajhh", fajhh);
		model.addAttribute("fajh", pyb);
		model.addAttribute("zxjxjh", plan);
		model.addAttribute("xsfaxk", xsfaxk);
		model.addAttribute("xszyxk", xszyxk);
		model.addAttribute("xsfxxk", xsfxxk);
		model.addAttribute("xsafatjxk", xsafatjxk);
		model.addAttribute("xsyzm", xsyzm);
		
		if("100020".equals(xxbm)) {
			model.addAttribute("pjf", courseManagementService.queryPjf());
			model.addAttribute("cxkc", courseManagementService.queryCxkc());
		}
		
		if(StringUtils.isNotBlank(mobile) && "true".equals(mobile)) {
			return "student/courseSelectManagement/mobile/page2";
		} else {
			return "student/courseSelectManagement/selectKc";
		}
	}

	/**
	 * 农大重补修选课提示
	 */
	@RequestMapping("/student/courseSelect/selectCourse/tip")
	public void tip(HttpServletResponse response) {
		courseManagementService.getFile(response);
	}

	@RequestMapping("/student/courseSelect/selectCourse/getYzmPic")
	public void getYzmPic(HttpSession session, HttpServletResponse response) {
		courseManagementService.getYzmPic(session, response);
	}

	@RequestMapping(value="/student/courseSelect/selectCourse/checkInputCodeAndSubmit", 
			produces="application/json;charset=UTF-8")
	@ResponseBody
	public Map<String, Object> checkInputCode(HttpSession session, HttpServletRequest request, HttpServletResponse response,
			String inputCode, @RequestParam("dealType") String dealType, Model model) {
		Map<String, Object> map = new HashMap<String, Object>();
		if(!csrfToken.isTokenValid(request)){
			map.put("result", csrfToken.gotoAjaxIndex());
			return map;
		} else {
			map.put("token", session.getAttribute("token_in_session"));
			String id = AuthUtil.getCurrentUser().getIdNumber();
			try {
				PxCsb pxcs = courseManagementService.loadPxcs("xk","xktjjyyzm");
				if(pxcs != null) {
					String submitTimeKey = "submitTime_" + id;
					String limitVal = pxcs.getCsz();
					if(StringUtils.isNotBlank(limitVal) && !"0".equals(limitVal)) {
						int xstjcs = RedisUtil.exists(submitTimeKey) ? Integer.valueOf(RedisUtil.get(submitTimeKey)) : 0;

						if(xstjcs < Integer.valueOf(limitVal)) {
							int submitTime = 1;
							if(RedisUtil.exists(submitTimeKey)) {
								submitTime = Integer.valueOf(RedisUtil.get(submitTimeKey));
								RedisUtil.setrange(submitTimeKey, 0, submitTime + 1 + "");
							} else {
								RedisUtil.setex(submitTimeKey.getBytes(), 24 * 60 * 60, (submitTime + "").getBytes());
							}
						} else {
							if(StringUtils.isBlank(inputCode)) {
								map.put("result","验证码不能为空！");
								return map;
							}
							String sessionCode = (String) session.getAttribute(Constants.KAPTCHA_SESSION_KEY);
							SessionAware.getSession().removeAttribute(Constants.KAPTCHA_SESSION_KEY);
							if(!inputCode.toLowerCase().equals(sessionCode.toLowerCase())) {
								map.put("result", "校验失败！");
								return map;
							}
						}
					}

					String maxVal = pxcs.getCsza();
					if(StringUtils.isNotBlank(maxVal) && !"0".equals(maxVal)) {
						List<Object[]> xzList = courseManagementService.queryXsXzMd(id, "01");
						if(CollectionUtils.isNotEmpty(xzList)) {
							map.put("result","您已被限制提交选课，请联系管理员处理！");
							return map;
						}

						int submitTime = 1;
						if(RedisUtil.exists(submitTimeKey)) {
							submitTime = Integer.valueOf(RedisUtil.get(submitTimeKey));
							if(submitTime + 1 > Integer.valueOf(maxVal)) {
								String xzsj = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
								courseManagementService.saveXzMd(id, "01", xzsj);
								RedisUtil.del(submitTimeKey);
								map.put("result","您已被限制提交选课，请联系管理员处理！");
								return map;
							} else {
								RedisUtil.setrange(submitTimeKey, 0, submitTime + 1 + "");
							}
						} else {
							RedisUtil.setex(submitTimeKey.getBytes(), 24 * 60 * 60, (submitTime + "").getBytes());
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
				map.put("result", "验证码校验失败！");
				return map;
			}

			try{
				// 获得执行教学计划号
				String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();
				// 选课阶段
				XkJdView jdb = courseManagementService.queryJd(zxjxjhh);
				String msg = courseManagementService.checkJdTime(jdb);
				if (msg != "") {
					map.put("result", "/student/courseSelect/courseSelect/index");
					return map;
				} else {
					long tjsj = new Date().getTime();
					String redisKey = id + dealType + ":" + tjsj;
					session.setAttribute("redisKey", redisKey);
					// 方案计划号
					String fajhh = request.getParameter("fajhh");

					// 课程号_课序号
					String kcIds = request.getParameter("kcIds");
					model.addAttribute("kcIds", kcIds);

					if (kcIds != null && !kcIds.equals("")) {
						String[] kcIdarr = kcIds.split(",");
						String uId = AuthUtil.getCurrentUser().getId()+"";
						String ip = CommonUtils.getRemoteHost(request);
						StringBuffer sb = new StringBuffer();
						for (String kcId : kcIdarr) {
							if (kcId != null && !kcId.trim().equals("")) {
								String[] vals = kcId.split("_", -1);
								
								String yxbjkey = "yxbj:" + id + ":" + vals[0] + "_" + vals[1];
								if(RedisUtil.exists(yxbjkey)) {
									RedisUtil.append(redisKey, ";" + vals[0] + "_" + vals[1] + ":该课堂已提交，正在处理中，请勿重复提交！");
									continue;
								}
								RedisUtil.set(yxbjkey, "1", 300);

								sb.setLength(0);
								sb.append(id + "," + fajhh + "," + vals[0] + "," + vals[1] + "," + ip + "," + dealType + "," + tjsj);
								if (dealType.equals("6")) {
									sb.append("," + vals[3] + "," + vals[4]);
								} else if (dealType.equals("7")) {
									sb.append("," + vals[3] + ",");
								} else {
									sb.append(",,");
								}
								SendMessage.sendMessageAsync(sb.toString(), uId);
							}
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
				map.put("result", "选课失败！");
				return map;
			}
			map.put("result", "ok");
			return map;
		}

	}

	/**
	 * 选课失败信息
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/courseSelectFailed/index")
	public String xksbxx(Model model, HttpServletRequest request) {

		// 获得用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();

		// 获得执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();

		// 学生学籍信息
		StudentTeachingSchoolView jxxj = courseManagementService
				.queryStudentTeachingSchool(id);

		// 学生培养信息
		List<XsPybView> pyblist = courseManagementService.queryXsPyb(id, null);

		// 选课进度
		XkJdView jdb = courseManagementService.queryJd(zxjxjhh);

		// 星期节次
		WeekSessionsTable xqjc = courseManagementService.queryWeekSession(zxjxjhh);

		// 节次时间
		List<SessionTimeTable> jcsj = courseManagementService.querySessionTimeTable(zxjxjhh);
		String schoolCode = commonService.queryParamValue();
		if (!"100027".equals(schoolCode)) {
			// 是否可以选课
			String err = courseManagementService.checkXk(id, jxxj, pyblist, jdb, zxjxjhh, xqjc, jcsj, request.getSession(), "xk");

			if (err != null) {
				model.addAttribute("err", err);
				return "student/courseSelectManagement/error";
			}
		}

		// 选课未成功信息
		List<XkXkwcgbView> xkwcgbList = courseManagementService.queryXkXkwcgbList(zxjxjhh, id);

		model.addAttribute("xkwcgbList", xkwcgbList);

		return "student/courseSelectManagement/xksbxx";
	}

	/**
	 * 计划课程页面
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/intentCourse/index")
	public String jhkc(Model model, HttpServletRequest request) {

		// 是否只允许重修选课
		String err = courseManagementService.checkCxkc();
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		// 方案计划号
		String fajhh = request.getParameter("fajhh");
		model.addAttribute("fajhh", fajhh);
		String schoolCode = commonService.queryParamValue();
		model.addAttribute("schoolCode", schoolCode);
		PxCsb csb = courseManagementService.loadPxcs("xsxk", "rwsjly");
		boolean fromRedis = csb == null || StringUtils.isBlank(csb.getCsz()) || "0".equals(csb.getCsz());
		model.addAttribute("fromRedis", fromRedis);
		return "student/courseSelectManagement/jhkc";
	}

	/**
	 * 查询主讲教师个人简介
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/queryTeacherJL")
	@ResponseBody
	public String queryTeacherJL(
			@RequestParam("id") String id) {
		List<Object[]> grjl = courseManagementService.queryTeacherJL(id);
		return JSONArray.fromObject(grjl).toString();
	}

	/**
	 * 计划课程页面课程列表
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/student/courseSelect/intentCourse/courseList")
	@ResponseBody
	public JSONObject jhkckclb(Model model,
			HttpServletRequest request,
			@RequestParam("fajhh") String fajhh, String mxbj, String mobile,
			HttpSession session) {
		Map<String,Object> map = new HashMap<String, Object>();
		// 用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();

		// 执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();

		List<Rwjh> zdjhList = null;
		String json_addedTList = null;
		Map<String, String> kylMap = new HashMap<String, String>();
		try {
			// 选课课程
			json_addedTList = getStudentXkInfo(zxjxjhh, id, session);

			// 任务计划集合
			String key = "";
			String bjh = "";
			if(mxbj.equals("1")) {
				bjh = courseManagementService.queryStudentClass(id);
				key = "xk_pad_rwjh_" + zxjxjhh + "_" + fajhh + "_" + bjh;
			} else {
				bjh = "";
				key = "xk_pad_rwjh_" + zxjxjhh + "_" + fajhh;
			}
			String kylKey = "xk_pad_kyl";
			PxCsb csb = courseManagementService.loadPxcs("xsxk", "rwsjly");
			boolean fromRedis = csb == null || StringUtils.isBlank(csb.getCsz()) || "0".equals(csb.getCsz());
			if(fromRedis && RedisUtil.exists(key.getBytes())) {
				zdjhList = (List<Rwjh>) RedisUtil.unserialize(RedisUtil.get(key.getBytes()));
				kylMap = RedisUtil.hgetAll(kylKey);
			} else {
				zdjhList = courseManagementService.queryZdjh(zxjxjhh, fajhh, mxbj, bjh);
				RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(zdjhList));
				
				if(fromRedis) {
					if(!RedisUtil.exists(kylKey.getBytes())) {
						kylMap.put("kongzhi", "0");
						RedisUtil.hmset(kylKey, kylMap);
						RedisUtil.expire(kylKey, 21600);
					}
					
					for (int i = 0; i < zdjhList.size(); i++) {
						Rwjh rwjh = zdjhList.get(i);
						String tempKey = zxjxjhh + "_" + rwjh.getKch() + "_" + rwjh.getKxh();
						String value = (rwjh.getBkskyl() == null ? 0 : rwjh.getBkskyl()) + "";
						RedisUtil.hset(kylKey, tempKey, value);
						kylMap.put(tempKey, value);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		// 任务计划集合根据时间组装为HashMap
		if(StringUtils.isNotBlank(mobile) && "true".equals(mobile)) {
			List<String> kchlist = (List<String>) session.getAttribute("xk_yxkch");
			map.put("zdjhList", zdjhList);
			map.put("kchlist", kchlist);
		} else {
			map = getZdjhBySj(zdjhList);
		}
		map.put("yxkclist", json_addedTList);
		map.put("kylMap", kylMap);
		JSONObject json_map = JSONObject.fromObject(map);

		return json_map;
	}

	/**
	 * 计划课程查询课余量
	 * 
	 * @param model
	 * @param request
	 * @return
	 * @throws SendRefuseException
	 */
	@RequestMapping(value = "/student/courseSelect/intentCourse/queryRest")
	@ResponseBody
	public int cxkcyl(Model model,
			@RequestParam("zxjxjhh") String zxjxjhh,
			@RequestParam("kch") String kch,
			@RequestParam("kxh") String kxh){
		int kcyl = courseManagementService.cxkcyl(zxjxjhh, kch, kxh);
		return kcyl;
	}

	/**
	 * 选课返回结果页面
	 * 
	 * @param model
	 * @param request
	 * @return
	 * @throws SendRefuseException
	 */
	@RequestMapping("/student/courseSelect/selectCourses/waitingfor")
	public String jhkcXz(Model model, 
			HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam("dealType") String dealType, String fajhh, String mobile) {
		model.addAttribute("mobile", mobile);
		model.addAttribute("dealType", dealType);
		// 用户id

		String kcIds = request.getParameter("kcIds");
		model.addAttribute("kcIds", kcIds);

		// 课程名
		String kcms = request.getParameter("kcms");
		model.addAttribute("kcms", kcms);

		int kcNum = 0;
		if (kcIds != null && !kcIds.equals("")) {
			String[] kcIdarr = kcIds.split(",");
			kcNum = kcIdarr.length;
		}
		model.addAttribute("kcNum", kcNum);

		String xxbm = CommonUtils.queryParamValue();
		model.addAttribute("xxbm", xxbm);
		//天津工业 课组完成汇总
		if("100018".equals(xxbm)){
			String id = AuthUtil.getCurrentUser().getIdNumber();
			List<Object[]> kzwcmxs = courseManagementService.queryKzwcmx(fajhh, id);
			model.addAttribute("kzwcmxs", kzwcmxs);

			return "student/courseSelectManagement/tjgyWaitfor";
		} else {
			return "student/courseSelectManagement/waitfor";
		}
	}


	@RequestMapping(value = "/student/courseSelect/selectResult/getCurrentXkmx", method = RequestMethod.POST)
	@ResponseBody
	public Page<Object>  getCurrentXkmx(Model model,HttpSession session,
			@RequestParam(defaultValue="1")int pageNum,@RequestParam(defaultValue="30")int pageSize){
		String id = AuthUtil.getCurrentUser().getIdNumber();
		String fajhh = (String) session.getAttribute("xk_fajhh");
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();
		String sql="select rownum rn,r.* from (select c.xm,a.famc,a.fkzm,a.kzm,a.kcm,a.xf    "
				+"   from v_ggkz_kzyqb a, xk_xkb b, xs_xjb c                                           "
				+"   where a.kch = b.kch                                                              "
				+"    and b.xh = c.xh                                                                 "
				+"    and a.fajhh = '"+fajhh+"'                                                            "
				+"    and b.zxjxjhh = '"+zxjxjhh+"'                                                 "
				+"   and b.xh = '"+id+"')r"  ;                                                       
		QueryInfo info = new QueryInfo();
		info.setPageNum(pageNum);
		info.setMaxResult(pageSize);
		info.setSql(sql);
		Page <Object> page = pageService.queryPageBySql(info);
		return page;
	}


	/**
	 * 天津工业  进入课组完成明细
	 * @param model
	 * @param fajhh
	 * @param xh
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/kzwcmxck/kzwcmxckBody/{fajhh}/{xh}", method = RequestMethod.GET)
	public String  kzwcmxCk(Model model,HttpSession session,
			@PathVariable String fajhh,@PathVariable String xh){
		session.setAttribute("mx_fajhh", fajhh);
		session.setAttribute("mx_xh", xh);

		return "/student/courseSelectManagement/kzwcmxckBody";
	}

	/**
	 * 课组完成明细
	 * @param model
	 * @param session
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/kzwcmxck/kzwcmxckBodyUI", method = RequestMethod.POST)
	@ResponseBody
	public Page<Object>  kzwcmxCkUI(Model model,HttpSession session,
			@RequestParam(defaultValue="1")int pageNum,@RequestParam(defaultValue="30")int pageSize){
		String fajhh = (String) session.getAttribute("mx_fajhh");
		String xh = (String) session.getAttribute("mx_xh");
		String sql="select rownum rn,a.* from (select *   from v_ggkz_xsxdqk a where fajhh='"+fajhh+"' and xh='"+xh+"')a";
		QueryInfo info = new QueryInfo();
		info.setPageNum(pageNum);
		info.setMaxResult(pageSize);
		info.setSql(sql);
		Page <Object> page = pageService.queryPageBySql(info);
		return page;
	}

	/**
	 * 
	 * @param model
	 * @param session
	 * @param fajhh
	 * @param xh
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/kzwcmxck/kcwcmxckBody/{fajhh}/{xh}/{fakzh}", method = RequestMethod.GET)
	public String kcwcmxCk(Model model,HttpSession session,
			@PathVariable String fajhh,@PathVariable String xh,@PathVariable String fakzh){
		session.setAttribute("kc_fajhh", fajhh);
		session.setAttribute("kc_xh", xh);
		session.setAttribute("zkzh", fakzh);
		return "/student/courseSelectManagement/kcwcmxckBody";
	}

	@RequestMapping(value = "/student/courseSelect/kzwcmxck/kcwcmxckBodyUI", method = RequestMethod.POST)
	@ResponseBody
	public Page<Object>  kcwcmxCkUI(Model model,HttpSession session,
			@RequestParam(defaultValue="1")int pageNum,@RequestParam(defaultValue="30")int pageSize){
		String fajhh = (String) session.getAttribute("mx_fajhh");
		String xh = (String) session.getAttribute("mx_xh");
		String zkzh = (String) session.getAttribute("zkzh");
		String sql="select rownum rn,a.* from (select * from v_ggkz_xsxdqk_mx a where a.fajhh='"+fajhh+"' and xh='"+xh+"' and a.fakzh='"+zkzh+"' )a";
		QueryInfo info = new QueryInfo();
		info.setPageNum(pageNum);
		info.setMaxResult(pageSize);
		info.setSql(sql);
		Page <Object> page = pageService.queryPageBySql(info);
		return page;
	}

	/**
	 * 方案课程页面
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/student/courseSelect/planCourse/index")
	public String fakc(Model model, HttpServletRequest request, HttpSession session) {

		// 是否只允许重修选课
		String err = courseManagementService.checkCxkc();
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		// 方案计划号
		String fajhh = request.getParameter("fajhh");

		if (fajhh == null) {
			fajhh = (String) session.getAttribute("xk_fajhh");
		}

		List<Object[]> kzList = courseManagementService.queryFaKzList(fajhh);
		model.addAttribute("kzList", kzList);

		// 执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();

		String key = "";
		List<CodeKcsxb> kcsxlist = null;
		List<XkCxXnxqView> xnxqlist = null;
		String jsonSection = null;
		try {
			key = "xk_pad_kcsxlb";
			if (RedisUtil.exists(key.getBytes())) {
				kcsxlist = (List<CodeKcsxb>) RedisUtil.unserialize(RedisUtil.get(key.getBytes()));
			} else {
				// 课程属性集合
				kcsxlist = courseManagementService.queryKcsxList();
				RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(kcsxlist));
			}

			key = "xk_pad_xnxq" + fajhh;
			if (RedisUtil.exists(key.getBytes())) {
				xnxqlist = (List<XkCxXnxqView>) RedisUtil.unserialize(RedisUtil.get(key.getBytes()));
			} else {
				// 学年学期集合
				xnxqlist = courseManagementService.queryXnxqList(fajhh);
				RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(xnxqlist));
			}

			key = "xk_pad_currentSectionTime";
			if (RedisUtil.exists(key)) {
				jsonSection = RedisUtil.get(key);
			} else {
				// 获取星期节次时间信息
				CodeTermSection section = courseManagementService.getSection(null);
				jsonSection = JSONObject.fromObject(section).toString();
				RedisUtil.setex(key.getBytes(), 6*60*60, jsonSection.getBytes());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		model.addAttribute("kcsxlist", kcsxlist);
		model.addAttribute("xnxqlist", xnxqlist);
		model.addAttribute("zxjxjhh", zxjxjhh);
		model.addAttribute("fajhh", fajhh);
		model.addAttribute("section", jsonSection);

		PxCsb pxCsb = CommonUtils.queryPxCsbById("xkgl", "FAQK_NJ");
		if(pxCsb != null && StringUtils.isNotBlank(pxCsb.getCsz()) && "1".equals(pxCsb.getCsz()) && StringUtils.isNotBlank(pxCsb.getCsza())) {
			// 学生学籍信息
			String xh = AuthUtil.getCurrentUser().getIdNumber();
			StudentTeachingSchoolView jxxj = courseManagementService.queryStudentTeachingSchool(xh);
			if(pxCsb.getCsza().contains((jxxj.getGradeCode()))) {
				model.addAttribute("yxkzqk", courseManagementService.queryYxKzQk(xh, zxjxjhh));
				model.addAttribute("kzxkqk", courseManagementService.queryKzXkQk(fajhh, xh));
			}
		}
		PxCsb csb = courseManagementService.loadPxcs("xsxk", "rwsjly");
		boolean fromRedis = csb == null || StringUtils.isBlank(csb.getCsz()) || "0".equals(csb.getCsz());
		model.addAttribute("fromRedis", fromRedis);
		return "student/courseSelectManagement/fakc";
	}

	private String getStudentXkInfo(String planNumber, String id, HttpSession session) {
		// 获得学生的选课基本信息
		List<SelectCourseTableView> selectCourseList = curriculumTableService.querySelectCourseList(planNumber, id);

		// 获得学生的选课时间地点
		List<CourseTimeAndPlaceView> timeAndPlaceList = curriculumTableService.queryCourseTimeAndPlaceView(planNumber, id);

		// 选课课程号集合
		List<String> kchlist = courseManagementService.packageKchkxh(selectCourseList);
		session.setAttribute("xk_yxkch", kchlist);

		//筛选时间不合适课程
		List<SelectCourseTableView> addedTList = courseManagementService.packageTPtoKc(selectCourseList,timeAndPlaceList);
		return JSONArray.fromObject(addedTList).toString().replace("null", "\"\"");
	}

	/**
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/student/courseSelect/planCourse/courseList")
	public @ResponseBody Map<String,Object> fakckclb(
			Model model, HttpServletRequest request, HttpSession session,
			String fajhh, String kch, String kcm, String kxh, String kcsxdm, String kclbdm,
			String kzh, int xq, int jc, String jhxn, String xqh) {
		Map<String,Object> map = new HashMap<String, Object>();

		// 用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();

		try {
			List<Rwfa> rwfalist = null;
			Map<String, String> kylMap = new HashMap<String, String>();

			String key = "xk_pad_rwfa_" + jhxn + "_" + fajhh;
			// 任务方案集合
			if (StringUtils.isNotBlank(fajhh)) {
				PxCsb csb = courseManagementService.loadPxcs("xsxk", "rwsjly");
				boolean fromRedis = csb == null || StringUtils.isBlank(csb.getCsz()) || "0".equals(csb.getCsz());
				if(fromRedis && StringUtils.isBlank(kch) && StringUtils.isBlank(kcm) && StringUtils.isBlank(kxh) 
						&& StringUtils.isBlank(kcsxdm) && StringUtils.isBlank(kclbdm) && StringUtils.isBlank(kzh) && xq == 0 && jc == 0 && StringUtils.isBlank(xqh)) {
					Map<String, Object> mm = queryFaList(kylMap, key, fajhh, jhxn, kch, kcm, kxh, kcsxdm, xq, jc, kclbdm, kzh, xqh);
					rwfalist = (List<Rwfa>) mm.get("list");
					kylMap = (Map<String, String>) mm.get("kyl");
				} else {
					// 任务方案集合
					rwfalist = courseManagementService.queryRwfalist(fajhh, jhxn, kch, kcm, kxh, kcsxdm, xq, jc, kclbdm, kzh, xqh);
					if(fromRedis) {
						String kylKey = "xk_pad_kyl";
						if(!RedisUtil.exists(kylKey.getBytes())) {
							kylMap.put("kongzhi", "0");
							RedisUtil.hmset(kylKey, kylMap);
							RedisUtil.expire(kylKey, 21600);
						}
	
						for (int i = 0; i < rwfalist.size(); i++) {
							Rwfa rwfa = rwfalist.get(i);
							String tempKey = rwfa.getTermCode() + "_" + rwfa.getCourseNum() + "_" + rwfa.getClassNum();
							String value = (rwfa.getBkskyl() == null ? 0 : rwfa.getBkskyl())+"";
							RedisUtil.hset(kylKey, tempKey, value);
							kylMap.put(tempKey, value);
						}
					}
				}
			}
			String json_addedTList = getStudentXkInfo(courseManagementService.queryExecutiveEducationPlanNumber(), id, session);
			List<String> kchlist = (List<String>) session.getAttribute("xk_yxkch");

			map.put("rwfalist", rwfalist);
			map.put("kchlist", kchlist);
			map.put("kylMap", kylMap);
			map.put("yxkclist", json_addedTList);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return map;
	}

	private Map<String, Object> queryFaList(Map<String, String> kylMap, String key, 
			String fajhh, String zxjxjhh, String kch, String kcm, String kxh, String kcsxdm, int xq, int jc, String kclbdm, String kzh, String xqh) {
		Map<String, Object> map = new HashMap<String, Object>();
		String kylKey = "xk_pad_kyl";
		if (RedisUtil.exists(key.getBytes())) {
			map.put("list", RedisUtil.unserialize(RedisUtil.get(key.getBytes())));
			map.put("kyl", RedisUtil.hgetAll(kylKey));
			return map;
		} else {
			Long ss = RedisUtil.setnx(key + "_lock", "locked");
			if (ss == 1) {
				RedisUtil.expire(key + "_lock", 10);
				// 任务方案集合
				List<Rwfa> rwfalist = 
						courseManagementService.queryRwfalist(fajhh, zxjxjhh, kch, kcm, kxh, kcsxdm, xq, jc, kclbdm, kzh, xqh);
				// 任务方案集合整理
				RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(rwfalist));

				if(!RedisUtil.exists(kylKey.getBytes())) {
					kylMap.put("kongzhi", "0");
					RedisUtil.hmset(kylKey, kylMap);
					RedisUtil.expire(kylKey, 21600);
				}

				for (int i = 0; i < rwfalist.size(); i++) {
					Rwfa rwfa = rwfalist.get(i);
					String tempKey = rwfa.getTermCode() + "_" + rwfa.getCourseNum() + "_" + rwfa.getClassNum();
					String value = (rwfa.getBkskyl() == null ? 0 : rwfa.getBkskyl())+"";
					RedisUtil.hset(kylKey, tempKey, value);
					kylMap.put(tempKey, value);
				}

				RedisUtil.del(key + "_lock");
				map.put("list", rwfalist);
				map.put("kyl", kylMap);
				return map;
			} else {
				try {
					Thread.sleep(200);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
				return queryFaList(kylMap, key, fajhh, zxjxjhh, kch, kcm, kxh, kcsxdm, xq, jc, kclbdm, kzh, xqh);
			}
		}
	}

	/**
	 * 系任选课页面
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/departCourse/index")
	public String xirxk(Model model, HttpServletRequest request, HttpSession session) {

		// 是否允许选课
		String err = courseManagementService.checkCxkc();
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		String fajhh = request.getParameter("fajhh");

		if (fajhh == null) {
			fajhh = (String) session.getAttribute("xk_fajhh");
		}

		String key = "";
		String jsonSection = null;
		try {
			key = "xk_pad_currentSectionTime";
			if (RedisUtil.exists(key)) {
				jsonSection = RedisUtil.get(key);
			} else {
				// 获取星期节次时间信息
				CodeTermSection section = courseManagementService.getSection(null);
				jsonSection = JSONObject.fromObject(section).toString();
				RedisUtil.setex(key.getBytes(), 6*60*60, jsonSection.getBytes());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		model.addAttribute("fajhh", fajhh);
		model.addAttribute("section", jsonSection.toString().replace("null", "\"\""));

		return "student/courseSelectManagement/xirxk";
	}

	/**
	 * 系任选课页面
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/student/courseSelect/departCourse/courseList")
	@ResponseBody
	public Map<String, Object> xirxkList(
			Model model, HttpServletRequest request, HttpSession session,
			String kclbdm, String searchtj, int xq, int jc) {
		Map<String, Object> map = new HashMap<String, Object>();
		// 用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();
		// 学籍
		StudentTeachingSchoolView jxxj = courseManagementService.queryStudentTeachingSchool(id);
		// 系所号
		String xsh = jxxj.getDepartmentNumber();

		// 执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();

		try {
			List<RwXirxk> rwXirxkList = null;
			Map<String, String> kylMap = new HashMap<String, String>();

			String key = "xk_pad_rwxi_" + zxjxjhh + "_" + xsh;
			String kylKey = "xk_pad_kyl";
			// 任务方案集合
			if (StringUtils.isNotBlank(zxjxjhh) && StringUtils.isNotBlank(xsh)) {
				if (StringUtils.isBlank(searchtj) && StringUtils.isBlank(kclbdm) && xq == 0 && jc == 0) {
					if (RedisUtil.exists(key.getBytes())) {
						rwXirxkList = (List<RwXirxk>) RedisUtil.unserialize(RedisUtil.get(key.getBytes()));
						kylMap = RedisUtil.hgetAll(kylKey);
					} else {
						// 任务方案集合
						rwXirxkList = courseManagementService.queryXirxkList(zxjxjhh, searchtj, xq, jc, xsh, kclbdm);
						RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(rwXirxkList));


						if(!RedisUtil.exists(kylKey.getBytes())) {
							kylMap.put("kongzhi", "0");
							RedisUtil.hmset(kylKey, kylMap);
							RedisUtil.expire(kylKey, 21600);
						}

						for (int i = 0; i < rwXirxkList.size(); i++) {
							RwXirxk rwXirxk = rwXirxkList.get(i);
							String tempKey = zxjxjhh + "_" + rwXirxk.getKch() + "_" + rwXirxk.getKxh();
							String value = (rwXirxk.getBkskyl() == null ? 0 : rwXirxk.getBkskyl())+"";
							RedisUtil.hset(kylKey, tempKey, value);
							kylMap.put(tempKey, value);
						}
					}
				} else {
					// 任务方案集合
					rwXirxkList = courseManagementService.queryXirxkList(zxjxjhh, searchtj, xq, jc, xsh, kclbdm);

					if(!RedisUtil.exists(kylKey.getBytes())) {
						kylMap.put("kongzhi", "0");
						RedisUtil.hmset(kylKey, kylMap);
						RedisUtil.expire(kylKey, 21600);
					}

					for (int i = 0; i < rwXirxkList.size(); i++) {
						RwXirxk rwXirxk = rwXirxkList.get(i);
						String tempKey = zxjxjhh + "_" + rwXirxk.getKch() + "_" + rwXirxk.getKxh();
						String value = (rwXirxk.getBkskyl() == null ? 0 : rwXirxk.getBkskyl())+"";
						RedisUtil.hset(kylKey, tempKey, value);
						kylMap.put(tempKey, value);
					}
				}
			}
			String json_addedTList = getStudentXkInfo(zxjxjhh, id, session);
			List<String> kchlist = (List<String>) session.getAttribute("xk_yxkch");

			//筛选时间不合适课程
			map.put("rwXirxkZlList", rwXirxkList);
			map.put("kchlist", kchlist);
			map.put("kylMap", kylMap);
			map.put("yxkclist", json_addedTList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return map;
	}

	/**
	 * 校任选课页面
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/schoolCourse/index")
	public String xarxk(Model model, HttpServletRequest request, HttpSession session) {

		// 是否允许选课
		String err = courseManagementService.checkCxkc();
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		String fajhh = request.getParameter("fajhh");

		if (fajhh == null) {
			fajhh = (String) session.getAttribute("xk_fajhh");
		}

		String key = "";
		String jsonSection = null;
		try {
			key = "xk_pad_currentSectionTime";
			if (RedisUtil.exists(key)) {
				jsonSection = RedisUtil.get(key);
			} else {
				// 获取星期节次时间信息
				CodeTermSection section = courseManagementService.getSection(null);
				jsonSection = JSONObject.fromObject(section).toString();
				RedisUtil.setex(key.getBytes(), 6*60*60, jsonSection.getBytes());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		model.addAttribute("fajhh", fajhh);
		model.addAttribute("section", jsonSection.toString().replace("null", "\"\""));

		return "student/courseSelectManagement/xarxk";
	}

	/**
	 * 校任选课页面
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/student/courseSelect/schoolCourse/courseList")
	@ResponseBody
	public Map<String, Object> xarxkList(
			Model model, HttpServletRequest request, HttpSession session,
			String searchtj, String kclbdm, String fajhh, String kzmc, int xq, int jc) {
		Map<String, Object> map = new HashMap<String, Object>();
		// 用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();
		// 执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();
		String xxbm = CommonUtils.queryParamValue();
		try {
			List<RwXarxk> rwXarxkList = null;
			List<RwXarxkTjgy> rwXarxkTjgyList = null;
			Map<String, String> kylMap = new HashMap<String, String>();

			String key = "xk_pad_rwxa_" + zxjxjhh;
			String kylKey = "xk_pad_kyl";
			// 任务方案集合
			if (StringUtils.isNotBlank(zxjxjhh)) {
				PxCsb csb = courseManagementService.loadPxcs("xsxk", "rwsjly");
				boolean fromRedis = csb == null || StringUtils.isBlank(csb.getCsz()) || "0".equals(csb.getCsz());
				if(fromRedis && StringUtils.isBlank(searchtj) && StringUtils.isBlank(kclbdm) && StringUtils.isBlank(kzmc) && xq == 0 && jc == 0 && RedisUtil.exists(key.getBytes())) {
					if("100018".equals(xxbm)) {
						rwXarxkTjgyList = (List<RwXarxkTjgy>) RedisUtil.unserialize(RedisUtil.get(key.getBytes()));
					} else {
						rwXarxkList = (List<RwXarxk>) RedisUtil.unserialize(RedisUtil.get(key.getBytes()));
					}
					kylMap = RedisUtil.hgetAll(kylKey);
				} else {
					// 任务方案集合
					if("100018".equals(xxbm)) {
						rwXarxkTjgyList = courseManagementService.queryXarxkList(zxjxjhh, searchtj, xq, jc, kclbdm, fajhh, kzmc);
						RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(rwXarxkTjgyList));
					} else {
						rwXarxkList = courseManagementService.queryXarxkList(zxjxjhh, searchtj, xq, jc, kclbdm, fajhh, kzmc);
						RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(rwXarxkList));
					}

					if(fromRedis) {
						if(!RedisUtil.exists(kylKey.getBytes())) {
							kylMap.put("kongzhi", "0");
							RedisUtil.hmset(kylKey, kylMap);
							RedisUtil.expire(kylKey, 21600);
						}
	
						if("100018".equals(xxbm)) {
							for (int i = 0; i < rwXarxkTjgyList.size(); i++) {
								RwXarxkTjgy rwXarxk = rwXarxkTjgyList.get(i);
								String tempKey = zxjxjhh + "_" + rwXarxk.getKch() + "_" + rwXarxk.getKxh();
								String value = (rwXarxk.getBkskyl() == null ? 0 : rwXarxk.getBkskyl())+"";
								RedisUtil.hset(kylKey, tempKey, value);
								kylMap.put(tempKey, value);
							}
						} else{
							for (int i = 0; i < rwXarxkList.size(); i++) {
								RwXarxk rwXarxk = rwXarxkList.get(i);
								String tempKey = zxjxjhh + "_" + rwXarxk.getKch() + "_" + rwXarxk.getKxh();
								String value = (rwXarxk.getBkskyl() == null ? 0 : rwXarxk.getBkskyl())+"";
								RedisUtil.hset(kylKey, tempKey, value);
								kylMap.put(tempKey, value);
							}
						}
					}
				}
			}
			String json_addedTList = getStudentXkInfo(zxjxjhh, id, session);
			List<String> kchlist = (List<String>) session.getAttribute("xk_yxkch");

			if("100018".equals(xxbm)) {
				map.put("rwXarxkZlList", rwXarxkTjgyList);
			} else {
				map.put("rwXarxkZlList", rwXarxkList);
			}
			map.put("kchlist", kchlist);
			map.put("kylMap", kylMap);
			map.put("yxkclist", json_addedTList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return map;
	}

	/**
	 * 自由选课页面
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/freeCourse/index")
	public String zyxk(Model model, HttpServletRequest request, HttpSession session,
			String val, String label, String type, String fj) {
		model.addAttribute("fj", fj);
		// 是否允许选课
		String err = courseManagementService.checkCxkc();
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		String fajhh = request.getParameter("fajhh");

		if (fajhh == null) {
			fajhh = (String) session.getAttribute("xk_fajhh");
		}

		String key = "";
		String jsonSection = null;
		try {
			key = "xk_pad_currentSectionTime";
			if (RedisUtil.exists(key)) {
				jsonSection = RedisUtil.get(key);
			} else {
				// 获取星期节次时间信息
				CodeTermSection section = courseManagementService.getSection(null);
				jsonSection = JSONObject.fromObject(section).toString();
				RedisUtil.setex(key.getBytes(), 6*60*60, jsonSection.getBytes());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		model.addAttribute("fajhh", fajhh);
		model.addAttribute("val", val);
		model.addAttribute("label", label);
		model.addAttribute("type", type);
		model.addAttribute("section", jsonSection.toString().replace("null", "\"\""));
		
		if("1".equals(fj)) {
			model.addAttribute("xsfj", courseManagementService.queryXsFj());
		}
		PxCsb csb = courseManagementService.loadPxcs("xsxk", "rwsjly");
		boolean fromRedis = csb == null || StringUtils.isBlank(csb.getCsz()) || "0".equals(csb.getCsz());
		model.addAttribute("fromRedis", fromRedis);
		return "student/courseSelectManagement/zyxk";
	}

	/**
	 * 自由选课课程列表
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/student/courseSelect/freeCourse/courseList")
	@ResponseBody
	public Map<String, Object> courseList(
			Model model, HttpServletRequest request, HttpSession session,
			String kkxsh, String kch, String kcm, String skjs, String kclbdm, int xq, int jc, String vt, String fj) {
		Map<String, Object> map = new HashMap<String, Object>();
		// 用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();

		// 执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();

		try {
			List<RwRxk> rwRxkList = null;
			Map<String, String> kylMap = new HashMap<String, String>();

			String key = "xk_pad_rwzy_" + zxjxjhh;
			String kylKey = "xk_pad_kyl";
			// 任务方案集合
			if (StringUtils.isNotBlank(zxjxjhh)) {
				if (StringUtils.isBlank(kkxsh) && StringUtils.isBlank(kch) 
						&& StringUtils.isBlank(kcm) && StringUtils.isBlank(skjs) 
						&& StringUtils.isBlank(kclbdm) && xq == 0 && jc == 0 
						&& StringUtils.isBlank(vt) && "0".equals(fj)) {
					if (RedisUtil.exists(key.getBytes())) {
						rwRxkList = (List<RwRxk>) RedisUtil.unserialize(RedisUtil.get(key.getBytes()));
						kylMap = RedisUtil.hgetAll(kylKey);
					} else {
						// 任选课List
						rwRxkList = courseManagementService.queryRxkList(zxjxjhh, kkxsh, kch, kcm, skjs, xq, jc, kclbdm, vt, fj);
						RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(rwRxkList));

						if(!RedisUtil.exists(kylKey.getBytes())) {
							kylMap.put("kongzhi", "0");
							RedisUtil.hmset(kylKey, kylMap);
							RedisUtil.expire(kylKey, 21600);
						}

						for (int i = 0; i < rwRxkList.size(); i++) {
							RwRxk rwRxk = rwRxkList.get(i);
							String tempKey = zxjxjhh + "_" + rwRxk.getKch() + "_" + rwRxk.getKxh();
							String value = (rwRxk.getBkskyl() == null ? 0 : rwRxk.getBkskyl())+"";
							RedisUtil.hset(kylKey, tempKey, value);
							kylMap.put(tempKey, value);
						}
					}
				} else {
					// 任选课List
					rwRxkList = courseManagementService.queryRxkList(zxjxjhh, kkxsh, kch, kcm, skjs, xq, jc, kclbdm, vt, fj);

					if(!RedisUtil.exists(kylKey.getBytes())) {
						kylMap.put("kongzhi", "0");
						RedisUtil.hmset(kylKey, kylMap);
						RedisUtil.expire(kylKey, 21600);
					}

					for (int i = 0; i < rwRxkList.size(); i++) {
						RwRxk rwRxk = rwRxkList.get(i);
						String tempKey = zxjxjhh + "_" + rwRxk.getKch() + "_" + rwRxk.getKxh();
						String value = (rwRxk.getBkskyl() == null ? 0 : rwRxk.getBkskyl())+"";
						RedisUtil.hset(kylKey, tempKey, value);
						kylMap.put(tempKey, value);
					}
				}
			}
			String json_addedTList = getStudentXkInfo(zxjxjhh, id, session);
			List<String> kchlist = (List<String>) session.getAttribute("xk_yxkch");

			map.put("rwRxkZlList", rwRxkList);
			map.put("kchlist", kchlist);
			map.put("kylMap", kylMap);
			map.put("yxkclist", json_addedTList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return map;
	}


	/**
	 * 删除课程页面
	 * 
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/quitCourse/index")
	public String deleteKcList(Model model, HttpSession session) {

		// 获得用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();

		// 获得执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();

		// 学生学籍信息
		StudentTeachingSchoolView jxxj = courseManagementService.queryStudentTeachingSchool(id);

		// 学生培养信息
		List<XsPybView> pyblist = courseManagementService.queryXsPyb(id, null);

		// 选课进度
		XkJdView jdb = courseManagementService.queryJd(zxjxjhh);

		// 学期节次
		WeekSessionsTable xqjc = courseManagementService.queryWeekSession(zxjxjhh);

		// 节次时间
		List<SessionTimeTable> jcsj = courseManagementService.querySessionTimeTable(zxjxjhh);
		// 是否可以选课/退课
		String err = null;
		String xxbm = LicenseManger.getSchoolId();
		model.addAttribute("xxbm", xxbm);
		//添加学校编码判断
		if("100009".equals(xxbm)){//天津科技要求不再选课阶段，也可以退课
			err = courseManagementService.checkTkTime();
		}else{//其他学校
			err = courseManagementService.checkXk(id, jxxj, pyblist, jdb,
					zxjxjhh, xqjc, jcsj, session, "tk");
		}
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		// 学生选课
		HashMap<String, SelectCourseTableView> xsxkHash = courseManagementService.queryXsxkHash(id, zxjxjhh, jdb);
		if("100010".equals(xxbm)) {
			model.addAttribute("zynum", courseManagementService.queryZynum(id, zxjxjhh));
		} else if("100027".equals(xxbm)){
			//烟台大学 查询当前重修选课阶段
			String xnxq = commonService.queryNowXnxq();
			List<XkJdView> jdbList = courseManagementService.queryCxxkjdList(xnxq,"005","02");
			if(jdbList.size()>0){
				XkJdView xkJdView = jdbList.get(0);
				String kssj = xkJdView.getKssj();
				String jssj = xkJdView.getJssj();
				model.addAttribute("xkkssj", kssj);
				model.addAttribute("xkjssj", jssj);
			}
		}
		// 总学分
		Double zxf = getZxf(xsxkHash);

		model.addAttribute("xsxkHash", xsxkHash);
		model.addAttribute("zxf", zxf);

		return "student/courseSelectManagement/deleteKcList";
	}

	/**
	 * 重修选课index
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/relearnCourse/index")
	public String cxxkCx(Model model, HttpServletRequest request, HttpSession session) {

		// 是否允许重修选课
		String err = courseManagementService.checkSfyxcx();
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		PxCsb csb = courseManagementService.loadPxcs("xkgl", "byscxpdct");
		model.addAttribute("byscxpdct", csb==null ? "" : csb.getCsz());

		csb = courseManagementService.loadPxcs("xkgl", "cxctpdfs");
		model.addAttribute("cxpctfs", csb==null ? "" : csb.getCsz());

		String fajhh = request.getParameter("fajhh");

		if (fajhh == null) {
			fajhh = (String) session.getAttribute("xk_fajhh");
		}
		model.addAttribute("fajhh", fajhh);

		String xxdm = commonService.queryParamValue();
		model.addAttribute("xxdm", xxdm);

		return "student/courseSelectManagement/cxxk";

	}

	/**
	 * 重修选课查询
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/student/courseSelect/relearnCourse/courseList")
	@ResponseBody
	public Map<String, Object> cxxkCourseList(
			Model model,
			HttpServletRequest request,
			HttpSession session) {
		Map<String, Object> map = new HashMap<String, Object>();

		// 用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();

		PxCsb csb = courseManagementService.loadPxcs("0251", "02");
		map.put("pkyl", csb.getCsz());

		// 执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();

		List<RwCxxk> rwCxxkList = null;
		String json_addedTList = null;
		Map<String, String> kylMap = new HashMap<String, String>();
		try {
			String key = "xk_pad_rwcx_" + zxjxjhh + "_" + id;
			String kylKey = "xk_pad_kyl";
			// 重修列表
			if (zxjxjhh != null && !"".equals(zxjxjhh)) {
				if (RedisUtil.exists(key.getBytes())) {
					rwCxxkList = (List<RwCxxk>) RedisUtil.unserialize(RedisUtil.get(key.getBytes()));
					kylMap = RedisUtil.hgetAll(kylKey);
				} else {
					Param[] params = null;
					String xxbm = commonService.queryParamValue();

					String sql = "SELECT rownum id, r.zxjxjhh, r.kch, r.kcm, r.kxh, r.xf, (SELECT pn.kcsxmc(k.kcsxdm) " +
							" FROM xs_pyb f, jh_fajhb j, jh_fajhkcb k WHERE f.xh = '" + id + "' AND f.fajhh = j.fajhh " +
							" AND j.fajhh = k.fajhh AND k.kch = p.kch) AS kcsx, r.skjs, r.kslxdm, r.kslxmc, f.bkskyl, " +
							" nvl(r.xkmsdm, '01'), nvl(r.xkmssm,'直选式'), r.xkkzdm, r.xkkzsm, r.xkxzsm, r.skzc, r.zcsm, " +
							" r.skxq, r.skjc, r.cxjc, r.xqm, r.jxlm, r.jasm, (case when r.kch<>p.kch then p.kch else '' end) as tdkch, " +
							" pn.kcm((case when r.kch<>p.kch then p.kch else '' end)) as tdkcm, (select bz from rw_llb " +
							" where zxjxjhh = r.zxjxjhh and kch = r.kch and kxh = r.kxh) bz, (SELECT k.kcsxdm " +
							" FROM xs_pyb f, jh_fajhb j, jh_fajhkcb k WHERE f.xh = '" + id + "' AND f.fajhh = j.fajhh " +
							" AND j.fajhh = k.fajhh AND k.kch = p.kch) AS kcsxdm, f.cxxkpdctf, f.yxxszxf, f.zcxkpdctf, f.cxfsdm," +
							"(select zkxh from rw_llb where zxjxjhh=r.zxjxjhh and kch=r.kch and kxh=r.kxh) zkxh, r.zkch, f.yxkxqxk, " +
							"(select (select kclbmc from CODE_KCLB_TWO where kclbdm = a.kclbdm2) kclbmc from code_kcb a where a.kch = r.kch ) kclbmc2, k.xmcjhc " +
							"FROM rw_llrw_sjddb r, code_kcb k, rw_xkb f, (select a.kch, nvl(b.tdkch, nvl(c.tdkch, a.kch)) as tdkch " +
							"from (select kch from Xs_Cj_All_Covered where xh = '" + id + "' " + 
							(StringUtils.isNotBlank(xxbm) && (xxbm.equals("100006") || xxbm.equals("100017")) ? " and kcsxdm='001' " : "") + 
							" group by kch having max(kccj) < 60) a left join jh_kctdb b on b.zxjxjhh = '" + zxjxjhh + "' and a.kch = b.btdkch left join code_kctdb c on a.kch = c.btdkch and c.spjg='1') p " +
							"where r.kch=k.kch and r.zxjxjhh=f.zxjxjhh(+) and r.kch=f.kch(+) and r.kxh=f.kxh(+) and r.zxjxjhh = '" + zxjxjhh + "' and (r.kch = p.kch or r.kch = p.tdkch) order by r.zxjxjhh desc, r.kch, zkxh desc, kxh";

					List<Object[]> cxxkList = baseDao.findEntitiesBySQL(sql, params);
					rwCxxkList = castToRwCxxkObj(cxxkList);
					RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(rwCxxkList));

					if(!RedisUtil.exists(kylKey.getBytes())) {
						kylMap.put("kongzhi", "0");
						RedisUtil.hmset(kylKey, kylMap);
						RedisUtil.expire(kylKey, 21600);
					}

					for (int i = 0; i < rwCxxkList.size(); i++) {
						RwCxxk rwCxxk = rwCxxkList.get(i);
						String tempKey = zxjxjhh + "_" + rwCxxk.getKch() + "_" + rwCxxk.getKxh();
						String value = (rwCxxk.getBkskyl() == null ? 0 : rwCxxk.getBkskyl())+"";
						RedisUtil.hset(kylKey, tempKey, value);
						kylMap.put(tempKey, value);
					}
				}
			}
			json_addedTList = getStudentXkInfo(zxjxjhh, id, session);
		} catch (Exception e) {
			e.printStackTrace();
		}

		// 选课课程号集合
		List<String> kchlist = (List<String>) session.getAttribute("xk_yxkch");

		List<RwCxxk> rwCxxkList1 = zlCxxkList(rwCxxkList, kchlist);
		String json_rwzylist = JSONArray.fromObject(rwCxxkList1).toString().replace("\"null\"", "\"\"");

		map.put("rwCxxkList", json_rwzylist);
		map.put("kylMap", kylMap);
		map.put("yxkclist", json_addedTList);

		return map;
	}


	/**
	 * 复修选课index
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/reViewCourse/index")
	public String fxxkCx(Model model, HttpServletRequest request, HttpSession session) {

		// 是否允许选课
		String err = courseManagementService.checkSfyxfx();
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		String fajhh = request.getParameter("fajhh");

		if (fajhh == null) {
			fajhh = (String) session.getAttribute("xk_fajhh");
		}
		model.addAttribute("fajhh", fajhh);

		PxCsb csb = courseManagementService.loadPxcs("xkgl", "fxpdct");
		model.addAttribute("fxpct", csb.getCsz());
		
		csb = courseManagementService.loadPxcs("xkgl", "fxpdkyl");
		model.addAttribute("pkyl", csb == null || StringUtils.isBlank(csb.getCsz()) ? "1" : csb.getCsz());

		String xxdm = commonService.queryParamValue();
		model.addAttribute("xxdm", xxdm);

		return "student/courseSelectManagement/fxxk";
	}

	/**
	 * 复修选课查询
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/student/courseSelect/reViewCourse/courseList")
	@ResponseBody
	public Map<String, Object> fxxkCourseList(
			Model model,
			HttpServletRequest request,
			HttpSession session) {
		Map<String, Object> map = new HashMap<String, Object>();

		// 用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();

		// 执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();

		List<RwCxxk> rwCxxkList = null;
		String json_addedTList = null;
		Map<String, String> kylMap = new HashMap<String, String>();
		try {
			String key = "xk_pad_rwfx_" + zxjxjhh + "_" + id;
			String kylKey = "xk_pad_kyl";
			// 重修列表
			if (zxjxjhh != null && !"".equals(zxjxjhh)) {
				if (RedisUtil.exists(key.getBytes())) {
					rwCxxkList = (List<RwCxxk>) RedisUtil.unserialize(RedisUtil.get(key.getBytes()));
					kylMap = RedisUtil.hgetAll(kylKey);
				} else {
					Param[] params = null;
					String xxbm = commonService.queryParamValue();
					String sql = "";
					if("100008".equals(xxbm)) {
						String kchs = baseDao.findEntityBySQL("select pkg_xk.f_imuCourseList4Refresh('"+ id +"') from dual", params);
						log.info("-------查询到到的课程号串为：" + kchs);
						sql = "SELECT rownum id, r.zxjxjhh, r.kch, r.kcm, r.kxh, r.xf, " +
							"(SELECT pn.kcsxmc(k.kcsxdm) FROM xs_pyb f, jh_fajhb j, jh_fajhkcb k WHERE f.xh = '"+ id +"' " +
							"AND f.fajhh = j.fajhh AND j.fajhh = k.fajhh AND k.kch = r.kch) AS kcsx, r.skjs, r.kslxdm, r.kslxmc, f.bkskyl, nvl(r.xkmsdm, '01'), " +
							"nvl(r.xkmssm, '直选式'), r.xkkzdm, r.xkkzsm, r.xkxzsm, r.skzc, r.zcsm, r.skxq, r.skjc, r.cxjc, r.xqm, r.jxlm, r.jasm, '' as tdkch, " +
							"'' as tdkcm, (select bz from rw_llb where zxjxjhh = r.zxjxjhh and kch = r.kch and kxh = r.kxh) bz, " +
							"(SELECT k.kcsxdm FROM xs_pyb f, jh_fajhb j, jh_fajhkcb k WHERE f.xh = '"+ id +"' AND f.fajhh = j.fajhh AND j.fajhh = k.fajhh AND k.kch = r.kch) AS kcsxdm, " +
							"f.cxxkpdctf, f.yxxszxf, f.zcxkpdctf, f.cxfsdm, (select zkxh from rw_llb where zxjxjhh = r.zxjxjhh and kch = r.kch and kxh = r.kxh) zkxh, " +
							"r.zkch, f.yxkxqxk, (select (select kclbmc from CODE_KCLB_TWO where kclbdm = a.kclbdm2) kclbmc from code_kcb a where a.kch = r.kch) kclbmc2, k.xmcjhc " +
							"FROM rw_llrw_sjddb r, code_kcb k, rw_xkb f where r.kch=k.kch, r.zxjxjhh = f.zxjxjhh(+) and r.kch = f.kch(+) and r.kxh = f.kxh(+) and r.zxjxjhh = '" + zxjxjhh + "' " +
							"and instrb('"+ kchs +"', ','|| r.kch || ',')>1 order by r.zxjxjhh desc, r.kch, zkxh desc, kxh";
					} else {
						sql = "SELECT rownum id, r.zxjxjhh, r.kch, r.kcm, r.kxh, r.xf, (SELECT pn.kcsxmc(k.kcsxdm) " +
								" FROM xs_pyb f, jh_fajhb j, jh_fajhkcb k WHERE f.xh = '" + id + "' AND f.fajhh = j.fajhh " +
								" AND j.fajhh = k.fajhh AND k.kch = p.kch) AS kcsx, r.skjs, r.kslxdm, r.kslxmc, f.bkskyl, " +
								" nvl(r.xkmsdm, '01'), nvl(r.xkmssm,'直选式'), r.xkkzdm, r.xkkzsm, r.xkxzsm, r.skzc, r.zcsm, " +
								" r.skxq, r.skjc, r.cxjc, r.xqm, r.jxlm, r.jasm, (case when r.kch<>p.kch then p.kch else '' end) as tdkch, " +
								" pn.kcm((case when r.kch<>p.kch then p.kch else '' end)) as tdkcm, (select bz from rw_llb " +
								" where zxjxjhh = r.zxjxjhh and kch = r.kch and kxh = r.kxh) bz, (SELECT k.kcsxdm " +
								" FROM xs_pyb f, jh_fajhb j, jh_fajhkcb k WHERE f.xh = '" + id + "' AND f.fajhh = j.fajhh " +
								" AND j.fajhh = k.fajhh AND k.kch = p.kch) AS kcsxdm, f.cxxkpdctf, f.yxxszxf, f.zcxkpdctf, f.cxfsdm," +
								"(select zkxh from rw_llb where zxjxjhh=r.zxjxjhh and kch=r.kch and kxh=r.kxh) zkxh, r.zkch, f.yxkxqxk, " +
								"(select (select kclbmc from CODE_KCLB_TWO where kclbdm = a.kclbdm2) kclbmc from code_kcb a where a.kch = r.kch ) kclbmc2, k.xmcjhc " +
								"FROM rw_llrw_sjddb r, code_kcb k, rw_xkb f, (select a.kch, nvl(b.tdkch, nvl(c.tdkch, a.kch)) as tdkch " +
								"from (select kch from Xs_Cj_All_Covered where xh = '" + id + "' " + 
								(StringUtils.isNotBlank(xxbm) && (xxbm.equals("100006") || xxbm.equals("100017")) ? " and kcsxdm='001' " : "") + 
								" group by kch having max(kccj) >= 60) a left join jh_kctdb b on b.zxjxjhh = '" + zxjxjhh + "' and a.kch = b.btdkch left join code_kctdb c on a.kch = c.btdkch and c.spjg='1') p " +
								"where r.kch=k.kch and r.zxjxjhh=f.zxjxjhh(+) and r.kch=f.kch(+) and r.kxh=f.kxh(+) and r.zxjxjhh = '" + zxjxjhh + "' and (r.kch = p.kch or r.kch = p.tdkch) order by r.zxjxjhh desc, r.kch, zkxh desc, kxh";
					}


					List<Object[]> cxxkList = baseDao.findEntitiesBySQL(sql, params);
					rwCxxkList = castToRwCxxkObj(cxxkList);
					RedisUtil.setex(key.getBytes(), 6*60*60, RedisUtil.serialize(rwCxxkList));

					if(!RedisUtil.exists(kylKey.getBytes())) {
						kylMap.put("kongzhi", "0");
						RedisUtil.hmset(kylKey, kylMap);
						RedisUtil.expire(kylKey, 21600);
					}

					for (int i = 0; i < rwCxxkList.size(); i++) {
						RwCxxk rwCxxk = rwCxxkList.get(i);
						String tempKey = zxjxjhh + "_" + rwCxxk.getKch() + "_" + rwCxxk.getKxh();
						String value = (rwCxxk.getBkskyl() == null ? 0 : rwCxxk.getBkskyl())+"";
						RedisUtil.hset(kylKey, tempKey, value);
						kylMap.put(tempKey, value);
					}
				}
			}
			json_addedTList = getStudentXkInfo(zxjxjhh, id, session);
		} catch (Exception e) {
			e.printStackTrace();
		}

		// 选课课程号集合
		List<String> kchlist = (List<String>) session.getAttribute("xk_yxkch");

		List<RwCxxk> rwCxxkList1 = zlCxxkList(rwCxxkList, kchlist);
		String json_rwzylist = JSONArray.fromObject(rwCxxkList1).toString().replace("\"null\"", "\"\"");

		map.put("rwCxxkList", json_rwzylist);
		map.put("kylMap", kylMap);
		map.put("yxkclist", json_addedTList);

		return map;
	}

	private List<RwCxxk> castToRwCxxkObj(List<Object[]> cxxkList) {
		List<RwCxxk> rcList = new ArrayList<RwCxxk>();
		if(cxxkList!=null && cxxkList.size()>0) {
			for(Object[] obj : cxxkList){
				RwCxxk rwCxxk = new RwCxxk();
				rwCxxk.setId(obj[0]==null?"":obj[0].toString());
				rwCxxk.setZxjxjhh(obj[1]==null?"":obj[1].toString());
				rwCxxk.setKch(obj[2]==null?"":obj[2].toString());
				rwCxxk.setKcm(obj[3]==null?"":obj[3].toString());
				rwCxxk.setKxh(obj[4]==null?"":obj[4].toString());
				rwCxxk.setXf(obj[5]==null?0:Double.parseDouble(obj[5].toString()));
				rwCxxk.setKcsxmc(obj[6]==null?"":obj[6].toString());
				rwCxxk.setSkjs(obj[7]==null?"":obj[7].toString());
				rwCxxk.setKslxdm(obj[8]==null?"":obj[8].toString());
				rwCxxk.setKslxmc(obj[9]==null?"":obj[9].toString());
				rwCxxk.setBkskyl(obj[10]==null?0:Integer.parseInt(obj[10].toString()));
				rwCxxk.setXkmsdm(obj[11]==null?"":obj[11].toString());
				rwCxxk.setXkmssm(obj[12]==null?"":obj[12].toString());
				rwCxxk.setXkkzdm(obj[13]==null?"":obj[13].toString());
				rwCxxk.setXkkzsm(obj[14]==null?"":obj[14].toString());
				rwCxxk.setXkxzsm(obj[15]==null?"":obj[15].toString());
				rwCxxk.setSkzc(obj[16]==null?"":obj[16].toString());
				rwCxxk.setZcsm(obj[17]==null?"":obj[17].toString());
				rwCxxk.setSkxq(obj[18]==null?"":obj[18].toString());
				rwCxxk.setSkjc(obj[19]==null?"":obj[19].toString());
				rwCxxk.setCxjc(obj[20]==null?"":obj[20].toString());
				rwCxxk.setXqm(obj[21]==null?"":obj[21].toString());
				rwCxxk.setJxlm(obj[22]==null?"":obj[22].toString());
				rwCxxk.setJasm(obj[23]==null?"":obj[23].toString());
				rwCxxk.setTdkch(obj[24]==null?"":obj[24].toString());
				rwCxxk.setTdkcm(obj[25]==null?"":obj[25].toString());
				rwCxxk.setBz(obj[26]==null?"":obj[26].toString());
				rwCxxk.setKcsxdm(obj[27]==null?"":obj[27].toString());
				rwCxxk.setCxxkpdctf(obj[28]==null?"":obj[28].toString());
				rwCxxk.setYxxszxf(obj[29]==null?"":obj[29].toString());
				rwCxxk.setZcxkpdctf(obj[30]==null?"":obj[30].toString());
				rwCxxk.setCxfsdm(obj[31]==null?"":obj[31].toString());
				rwCxxk.setZkxh(obj[32]==null?"":obj[32].toString());
				rwCxxk.setZkch(obj[33]==null?"":obj[33].toString());
				rwCxxk.setYxkxqxk(obj[34]==null?"":obj[34].toString());
				rwCxxk.setKclbmc2(obj[35]==null?"":obj[35].toString());
				rcList.add(rwCxxk);
			}
		}
		return rcList;
	}

	/**
	 * 修改志愿课程
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/quitCourse/saveXkzy",produces="application/json;charset=UTF-8")
	@ResponseBody
	public Map<String, Object> saveXkzy(Model model, HttpServletRequest request,
			String kc) {
		Map<String, Object> map = new HashMap<String, Object>();
		if(!csrfToken.isTokenValid(request)){
			map.put("result", csrfToken.gotoAjaxIndex());
		} else {
			// 获得用户id
			String id = AuthUtil.getCurrentUser().getIdNumber();
			String zxjxjhh = CommonUtils.queryRwPkXnxq();;
			map.put("token", request.getSession().getAttribute("token_in_session"));
			map.put("result", courseManagementService.saveXkzy(id, zxjxjhh, kc));
		}
		return map;
	}

	/**
	 * 删除课程
	 * 
	 * @param model
	 * @param request
	 * @param pathflag 程序访问路径，delCourse：正常退课路径  currentWeeklyCourse：开课周退课路径
	 * @return  //
	 */
	@RequestMapping(value = "/student/courseSelect/{pathflag}/deleteOne", produces="application/text;charset=UTF-8")
	@ResponseBody
	public String deleteKc(Model model, HttpServletRequest request,
			@PathVariable("pathflag") String pathflag,
			XkToRabbitObject xkToRabbitObject) {

		if(!csrfToken.isTokenValid(request)){
			return csrfToken.gotoAjaxIndex();
		}
		String mes = "";
		try{
			String ip = getIpAddr(request);
			String xh = AuthUtil.getCurrentUser().getIdNumber();
			String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();
			xkToRabbitObject.setZxjxjhh(zxjxjhh);
			xkToRabbitObject.setXh(xh);
			xkToRabbitObject.setIp(ip);

			while (true) {
				Long ss = RedisUtil.setnx(xh + "_xkHash_lock", "locked");
				if (ss == 1) {
					RedisUtil.expire(xh + "_xkHash_lock", 10);
					break;
				} else {
					Thread.sleep(200);
				}
			}

			//开课周补退选  课堂人数小于XX人，就不允许学生退课 start gk
			if("currentWeeklyCourse".equals(pathflag)){
				String rs = null;
				if(RedisUtil.exists("kkzbtx_rs")){
					rs = RedisUtil.get("kkzbtx_rs");
				}else{
					PxCsb pxCsb = courseManagementService.queryPxcsb("xkgl", "kkzbtx_rs");
					if(pxCsb != null && StringUtils.isNotBlank(pxCsb.getCsz())){
						rs = pxCsb.getCsz();
						RedisUtil.set("kkzbtx_rs",rs);
					}
				}
				if(rs != null){
					int num = Integer.parseInt(rs);//允许最小退课人数
					Number decimal = courseManagementService.querySelectTotal(zxjxjhh, xkToRabbitObject.getKch(), xkToRabbitObject.getKxh());//当前选课人数
					if(decimal.intValue() <= num){
						RedisUtil.del(xh + "_xkHash_lock");
						return "当前选课人数为"+decimal.intValue()+",课堂最低人数为"+num+",课程不允许删除！";
					}
				}
			}
			//开课周补退选  课堂人数小于XX人，就不允许学生退课 end gk

			String rwztdm = courseManagementService.queryRwztdm(xkToRabbitObject);
			if(StringUtils.isBlank(rwztdm) || !"06".equals(rwztdm)) {
				RedisUtil.del(xh + "_xkHash_lock");
				return "非选课状态的课程不允许删除！";
			}

			// 根据选课方式获得选课哈希
			@SuppressWarnings("rawtypes")
			HashMap xsxkHash=(HashMap) courseManagementService.queryXsxk(xh, zxjxjhh);

			//要删除的选课视图
			XsxkView xsxkView = (XsxkView) xsxkHash.get(xkToRabbitObject.getKch());

			if (xsxkView != null) {
				
				if(xsxkView.getXtbz() != null && xsxkView.getXtbz().contains("重修申请选课")) {
					return "重修申请选课请移步专属退课通道！";
				}
				
				String xdfsdm = xsxkView.getXdfsdm();
				PxCsb pxCsb = null;
				String tmp = "";
				if("01".equals(xdfsdm)) {
					pxCsb = courseManagementService.loadPxcs("0253", "1");
					tmp = "正常";
				} else if("04".equals(xdfsdm)) {
					pxCsb = courseManagementService.loadPxcs("0252", "0");
					tmp = "重修";
				} else if("06".equals(xdfsdm)) {
					pxCsb = courseManagementService.loadPxcs("xkgl", "fxxkkg");
					tmp = "复修";
				}
				if (pxCsb == null || pxCsb.getCsz() == null || pxCsb.getCsz().equals("0")) {
					return "不允许退"+ tmp +"课程";
				}
				
				
				XsxkView xsxkldView = null;
				if(xdfsdm.equals("01")) {
					String ldkch = courseManagementService.queryLdKch(xsxkView.getXsxkViewPk().getKch());
					if(StringUtils.isNotBlank(ldkch)) {
						xsxkldView = (XsxkView) xsxkHash.get(ldkch);
						//					if(xsxkldView == null) {
						//						RedisUtil.del(xh + "_xkHash_lock");
						//						return "已选课程中未查询到联动课程！";
						//					}
					}
				}

				XkJdView jd = courseManagementService.queryJd(zxjxjhh);
				String xxbm = commonService.queryParamValue();
				//烟台大学25893 ，学生在重补修选课阶段，只允许退“修读方式“为”重修“的课程
				//天津工业25873  ，学生在重补修选课阶段，只允许退“修读方式“为”重修“的课程
				if( "100027".equals(xxbm) && "005".equals(jd.getJdlxdm()) ){
					if( !( "04".equals(xsxkView.getXdfsdm()) || "06".equals(xsxkView.getXdfsdm()) ) ){
//						学生的课程必须符合1和2的条件才能退课
						return "当前为重补修选课阶段，只允许重修或复修退课！";
					}
				} else if(StringUtils.isNotBlank(xxbm) && xxbm.equals("100010") 
						&& jd != null && StringUtils.isNotBlank(jd.getJdlxdm())
						&& jd.getJdlxdm().equals("005")) {

					String xkjdlxdm = xsxkView.getXkjdlxdm();
					if(StringUtils.isBlank(xkjdlxdm) || !xkjdlxdm.equals("005")) {
						RedisUtil.del(xh + "_xkHash_lock");
						return "以前阶段的选课不允许删除！";
					}
				}

				if(StringUtils.isNotBlank(xxbm) && xxbm.equals("100054")) {
					String xkkzh = xsxkView.getXkkzh();
					if(StringUtils.isNotBlank(xkkzh)) {
						Object[] obj = courseManagementService.queryKzXkMs(zxjxjhh, xh, xkkzh);
						if(obj != null && obj[0] != null && Integer.parseInt(obj[1] + "") <= Integer.parseInt(obj[0] + "")) {
							RedisUtil.del(xh + "_xkHash_lock");
							return "退课后，课程所属选课课组已选课门数少于“最少选课门数”，不允许删除！";
						}
					}
					if(xsxkldView != null) {
						xkkzh = xsxkldView.getXkkzh();
						if(StringUtils.isNotBlank(xkkzh)) {
							Object[] obj = courseManagementService.queryKzXkMs(zxjxjhh, xh, xkkzh);
							if(obj != null && obj[0] != null && Integer.parseInt(obj[1] + "") <= Integer.parseInt(obj[0] + "")) {
								RedisUtil.del(xh + "_xkHash_lock");
								return "退课后，联动课程所属选课课组已选课门数少于“最少选课门数”，不允许删除！";
							}
						}
					}
				}


				// 判断课程是否可删
				mes = checkAllowDelKc(xsxkHash, xsxkView);
				if (StringUtils.isNotBlank(mes)) {
					return mes;
				}
				if(xsxkldView != null) {
					mes = checkAllowDelKc(xsxkHash, xsxkldView);
					if (StringUtils.isNotBlank(mes)) {
						RedisUtil.del(xh + "_xkHash_lock");
						return "【联动退课】" + mes;
					}
				}

				// 直选式课程删除
				PxCsb csb = courseManagementService.loadPxcs("xkgl", "ycsfkyl");
				boolean ycsf = (csb != null && StringUtils.isNotBlank(csb.getCsz()) && csb.getCsz().equals("1"));

				String xkztdm = xsxkView.getXkztdm();
				String xkztdm1 = xsxkView.getXkztdm();
				if(!xkztdm.equals(xkztdm1)) {
					RedisUtil.del(xh + "_xkHash_lock");
					return "对不起，退课课程与联动课程选课状态不一致！";
				}
				if ((xkztdm == null)
						|| ("001".equals(xkztdm))
						|| ("011".equals(xkztdm))
						|| ("012".equals(xkztdm))
						|| ("014".equals(xkztdm))
						|| ("016".equals(xkztdm))) {

					// 删除直选课程
					mes = delZXKc(xsxkView, ycsf, ip, xsxkldView, xkToRabbitObject);
				} else {
					if ((!"008".equals(xkztdm))
							&& (!"013".equals(xkztdm))) {
						String ztName = courseManagementService.queryXkzt(xkztdm);
						mes = "该课程当前状态为【"+ztName+"】，不允许删除！";
						return mes;
					} else {
						// 删除拟选数据
						mes = courseManagementService.delZYKc(xsxkView, ip, xsxkldView, ycsf, xkToRabbitObject);
					}
				}

				//课程删除最后mes
				if(StringUtils.isBlank(mes)) {
					mes = (xsxkldView == null ? "" : "【联动退课】") + "删除课程成功！";
				}
			} else {
				mes = "选课数据不存在！";
			}
			RedisUtil.del(xh + "_xkHash_lock");
			return mes;
		} catch (Exception e) {
			e.printStackTrace();
			return "删除课程异常，请联系管理员处理！";
		}
	}

	/**
	 * 获取选课结果
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/selectResult/query")
	@ResponseBody
	public Map<String, Object> getXkResult(Model model, Integer kcNum, @RequestParam("redisKey") String redisKey) {
		Map<String, Object> map = new HashMap<String, Object>();
		String schoolId = commonService.queryParamValue();
		List<Object[]> kclist = new ArrayList<Object[]>();
		map.put("schoolId", schoolId);
		if (redisKey != null) {
			try {
				if (RedisUtil.exists(redisKey)) {
					String result = RedisUtil.get(redisKey);
					String[] res = result.split(";");
					if (kcNum == res.length - 1) {
						if("100027".equals(schoolId)){
							Double zxf = 0.0;
							for (int i = 1; i < res.length; i++) {
								if("100027".equals(schoolId) && res[i].indexOf("选课成功")!= -1){
									String[] kchs = res[i].split(":");
									String[] kchxs = kchs[0].split("_");
									String kch = kchxs[0];
									CodeKcb kcb = baseDao.findById(CodeKcb.class, kch);
									double je = kcb.getXf() * 100;
									Object[] obj = {kch,kchxs[1],kcb.getKcm(),String.valueOf(kcb.getXf()),String.valueOf(je)};
									kclist.add(obj);
									Double xf = kcb.getXf();
									zxf += xf;
								}
							}
							map.put("xm", AuthUtil.getCurrentUser().getUserName());
							map.put("zxf", String.valueOf(zxf));
							map.put("zje", String.valueOf(zxf*100));
							map.put("kclist", kclist);
						}
						map.put("isFinish", true);
						RedisUtil.del(redisKey);
					} else {
						map.put("isFinish", false);
					}
					map.put("result", res);
				} else {
					map.put("isFinish", false);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			map.put("isFinish", false);
		}

		return map;
	}

	private String getIpAddr(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		if ((ip == null) || (ip.length() == 0)
				|| ("unknown".equalsIgnoreCase(ip))) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if ((ip == null) || (ip.length() == 0)
				|| ("unknown".equalsIgnoreCase(ip))) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 
				|| "unknown".equalsIgnoreCase(ip)) {  
			ip = request.getHeader("HTTP_X_FORWARDED_FOR");  
		}  
		if (ip == null || ip.length() == 0 
				|| "unknown".equalsIgnoreCase(ip)) {  
			ip = request.getHeader("X-Real-IP");  
		}
		if ((ip == null) || (ip.length() == 0)
				|| ("unknown".equalsIgnoreCase(ip))) {
			ip = request.getRemoteAddr();
		}

		// 处理多IP的情况（只取第一个IP）
		if (ip != null && ip.contains(",")) {
			String[] ipArray = ip.split(",");
			ip = ipArray[0];
		}
		return ip;
	}


	// 任务计划集合根据时间组装为HashMap
	@SuppressWarnings("unchecked")
	private HashMap<String,Object> getZdjhBySj(List<Rwjh> zdjh) {
		HashMap<String,Object> zdjhHash = new HashMap<String,Object>();
		HashMap<String,Object> zdjhTimeHash = new HashMap<String,Object>();
		ArrayList<Rwjh> zdjhNoTime = new ArrayList<Rwjh>();
		zdjhHash.put("hasTime", zdjhTimeHash);
		zdjhHash.put("noTime", zdjhNoTime);
		if ((zdjh == null) || (zdjh.size() == 0)) {
			return zdjhHash;
		}
		for (int i = 0; i < zdjh.size(); i++) {
			Rwjh tempRwjh = zdjh.get(i);
			/**
			 * @date 2017-03-28
			 * <AUTHOR>
			 * 去掉已选课程过滤限制
			 *  && (kchlist == null || kchlist.size() == 0 || checkKchIsContain(tempRwjh.getKch(),kchlist))
			 */
			if(tempRwjh!=null && ((tempRwjh.getBkskyl() != null && tempRwjh.getBkskyl()>0) 
					|| ((StringUtils.isNotBlank(tempRwjh.getXkmsdm()) && tempRwjh.getXkmsdm().equals("02"))))){
				if ((tempRwjh.getSkxq() == null)
						|| (tempRwjh.getSkxq().intValue() == 0)) {
					zdjhNoTime = (ArrayList<Rwjh>) zdjhHash.get("noTime");
					zdjhNoTime.add(zdjhNoTime.size(), tempRwjh);
				} else {
					for (int k = 0; k < tempRwjh.getCxjc().intValue(); k++) {
						String timeId = tempRwjh.getKch() + "_" + tempRwjh.getKxh();
						Rwjh rwjh = new Rwjh();
						BeanUtils.copyProperties(tempRwjh, rwjh);
						rwjh.setSkjc(tempRwjh.getSkjc() + k);
						zdjhTimeHash = (HashMap<String, Object>) zdjhHash.get("hasTime");
						ArrayList<Rwjh> tempRwList = (ArrayList<Rwjh>) zdjhTimeHash.get(timeId);
						if (tempRwList == null) {
							tempRwList = new ArrayList<Rwjh>();
						}
						tempRwList.add(rwjh);
						zdjhTimeHash.put(timeId, tempRwList);
					}
				}
			}

		}
		return zdjhHash;
	}

	@SuppressWarnings("unused")
	private boolean checkKchIsContain(String kch, List<String> kchlist) {
		boolean flag = true;
		for(int i=0;i<kchlist.size();i++){
			if(kch.equals(kchlist.get(i))){
				flag = false;
				break;
			}
		}

		return flag;
	}

	// 校任选课Hash
	public HashMap<String, RwXarxk> getXarxkHash(List<RwXarxk> inforList) {
		HashMap<String, RwXarxk> inforHash = new HashMap<String, RwXarxk>();
		if ((inforList == null) || (inforList.size() == 0)) {
			return inforHash;
		}
		for (int i = 0; i < inforList.size(); i++) {
			RwXarxk tempInfor = inforList.get(i);
			inforHash.put(tempInfor.getKch() + "_" + tempInfor.getKxh(),
					tempInfor);
		}
		return inforHash;
	}

	// 系任选课Hash
	public HashMap<String, RwXirxk> getXirxkHash(List<RwXirxk> inforList) {
		HashMap<String, RwXirxk> inforHash = new HashMap<String, RwXirxk>();
		if ((inforList == null) || (inforList.size() == 0)) {
			return inforHash;
		}
		for (int i = 0; i < inforList.size(); i++) {
			RwXirxk tempInfor = inforList.get(i);
			inforHash.put(tempInfor.getKch() + "_" + tempInfor.getKxh(),
					tempInfor);
		}
		return inforHash;
	}

	// 自由选课Hash
	public HashMap<String, RwRxk> getRxkHash(List<RwRxk> inforList) {
		HashMap<String, RwRxk> inforHash = new HashMap<String, RwRxk>();
		if ((inforList == null) || (inforList.size() == 0)) {
			return inforHash;
		}
		for (int i = 0; i < inforList.size(); i++) {
			RwRxk tempInfor = inforList.get(i);
			inforHash.put(tempInfor.getKch() + "_" + tempInfor.getKxh(),
					tempInfor);
		}
		return inforHash;
	}

	//判断课程是否已选
	public boolean checkKchIsContained(String kch, String zkch, List<String> kchlist) {
		boolean flag = true;
		for(int i=0;i<kchlist.size();i++){
			if(kch.equals(kchlist.get(i)) 
					|| (StringUtils.isNotBlank(zkch) && zkch.equals(kchlist.get(i)))){
				flag = false;
				break;
			}
		}

		return flag;
	}

	//重修选课
	@SuppressWarnings("unchecked")
	private List<RwCxxk> zlCxxkList(List<RwCxxk> rwcxkList, List<String> kchlist) {
		LinkedHashMap<String, RwCxxk> cxkcMap = new LinkedHashMap<String, RwCxxk>();
		ArrayList<RwCxxk> cxhbList = new ArrayList<RwCxxk>();
		for (int i = 0; i < rwcxkList.size(); i++) {
			RwCxxk rwcx = new RwCxxk();
			rwcx = rwcxkList.get(i);
			if(kchlist==null || kchlist.size()==0 || checkKchIsContained(rwcx.getKch(), rwcx.getZkch(), kchlist)){
				String key = "";
				ArrayList<Kcsjdd> tList = new ArrayList<Kcsjdd>();
				Kcsjdd temp = new Kcsjdd();

				temp.setSkzc(rwcx.getSkzc());
				temp.setZcsm(rwcx.getZcsm());
				temp.setSkxq(rwcx.getSkxq());
				temp.setSkjc("" + rwcx.getSkjc());
				temp.setJasm(rwcx.getJasm());
				temp.setJxlm(rwcx.getJxlm());
				temp.setXqm(rwcx.getXqm());
				temp.setCxjc(rwcx.getCxjc());
				key = rwcx.getKch() + "," + rwcx.getKxh() + "," + rwcx.getSkjs();

				tList.add(temp);
				rwcx.setSjdd(tList);
				if (i == 0) {
					cxkcMap.put(key, rwcx);
				} else {
					Iterator<String> cxit = cxkcMap.keySet().iterator();
					while (cxit.hasNext()) {
						String tkey = cxit.next();

						if (key.equals(tkey)) {
							RwCxxk yrwcx = cxkcMap.get(tkey);
							ArrayList<Kcsjdd> yList = yrwcx.getSjdd();

							tList.addAll(yList);
							rwcx.setSjdd(tList);

							yrwcx = null;
							yList = null;
						}
					}
					cxkcMap.put(key, rwcx);
				}

				rwcx = null;
				temp = null;
				tList = null;
			}
		}

		Iterator<String> cxit = cxkcMap.keySet().iterator();
		while (cxit.hasNext()) {
			String tkey = cxit.next();
			RwCxxk temp = cxkcMap.get(tkey);
			cxhbList.add(temp);
		}
		return cxhbList;
	}

	// 获取总学分
	private Double getZxf(HashMap<String, SelectCourseTableView> xsxkHash) {
		double zxf = 0;
		if (xsxkHash != null && xsxkHash.size() > 0) {
			Collection<SelectCourseTableView> list = xsxkHash.values();
			for (SelectCourseTableView view : list) {
				if (view != null) {
					Double xf = view.getUnit();
					zxf += xf;
				}
			}
		}
		return zxf;
	}

	// 判断课程是否可删
	@SuppressWarnings("rawtypes")
	private String checkAllowDelKc(HashMap xsxkHash, XsxkView xsxkView) {
		String checkFlag = "";
		checkFlag = sfSykc(xsxkHash, xsxkView);
		if (checkFlag != null && !checkFlag.equals("")) {
			return checkFlag;
		}
		checkFlag = checkDelXkkz(xsxkHash, xsxkView);
		return checkFlag;
	}


	// 检查课组删除状态控制
	@SuppressWarnings("rawtypes")
	private String checkDelXkkz(HashMap xsxkHash, XsxkView xsxkView) {
		if (xsxkHash.get(xsxkView.getXsxkViewPk().getKch()) == null) {
			return "从已选课程未查询到该课程！";
		}
		
		String xxbm = commonService.queryParamValue();
		if(("04".equals(xsxkView.getXdfsdm()) || "06".equals(xsxkView.getXdfsdm()) || "09".equals(xsxkView.getXdfsdm())) && "100020".equals(xxbm)){//湖南理工 016为已缴费状态，可申请重、复、补修选课退费

		} else {
			if (xsxkView.getXkztdm() != null && xsxkView.getXkztdm().equals("016")) {
				PxCsb pxcstrue = courseManagementService.loadPxcs("0910","33");
				if (pxcstrue != null && pxcstrue.getCsz().equals("0")) {
					return "置入课程不允许删除！";
				}
			}
		}

		if (xsxkView.getXkkzdm() == null) {
			return "";
		}
		if (!(xsxkView.getXkkzdm().equals("01") || xsxkView.getXkkzdm().equals("03"))) {

			String tip = courseManagementService.queryTkMsg(xsxkView.getXsxkViewPk());
			if(StringUtils.isBlank(tip)) {
				return "该课程不允许退课！";
			} else {
				return tip;
			}
		}
		return "";
	}

	// 判断是否实验课程
	@SuppressWarnings({ "rawtypes" })
	private String sfSykc(HashMap xsxkHash, XsxkView xsxkView) {
		if (xsxkHash.get(xsxkView.getXsxkViewPk().getKch()) != null) {
			try {
				List<SyXkbView> syxkjg = courseManagementService.querySyxkb(xsxkView
						.getXsxkViewPk().getXh(), xsxkView.getXsxkViewPk()
						.getKch());
				if (syxkjg != null && syxkjg.size() > 0) {
					return "已经选择了对应的项目，请在实验课中删除选择的项目后，再删除此课程!";
				} else {
					return "";
				}
			} catch (Exception e) {
				e.printStackTrace();
				return "删除课程数据失败请联系管理员！";
			}
		}
		return "";
	}

	// 删除直选课程
	private String delZXKc(XsxkView xsxkView, boolean ycsf,String ip, XsxkView xsxkldView, XkToRabbitObject xkToRabbitObject) {
		PxCsb cxcsb = courseManagementService.loadPxcs("0251","02");
		PxCsb fxcsb = courseManagementService.loadPxcs("xkgl","fxpdkyl");
		return courseManagementService.delXsxk(xsxkView, cxcsb, fxcsb, ycsf, ip, xsxkldView, xkToRabbitObject);
	}
	/**
	 * 农大查看选课人数
	 */
	@RequestMapping("/student/courseSelect/selectCourse/viewXkCount/{zxjxjhh}/{kch}/{kxh}")
	@ResponseBody
	public int viewXkCount(HttpServletRequest request,
			HttpServletResponse response,
			@PathVariable("zxjxjhh") String zxjxjhh,
			@PathVariable("kch") String kch,
			@PathVariable("kxh") String kxh
			) {
		return courseManagementService.viewXkCount(zxjxjhh,kch,kxh).intValue();
	}

	/**
	 * 停开课程页面
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/stopCourse/index")
	public String tkkc(Model model, HttpServletRequest request) {

		//是否只允许重修选课
		String err = courseManagementService.checkCxkc();
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		// 方案计划号
		String fajhh = request.getParameter("fajhh");
		model.addAttribute("fajhh", fajhh);
		//是否显示自由选课
		String xszyxk = "";
		PxCsb pxcs = courseManagementService.loadPxcs("web_xkjmcs","xszyxk");
		if (pxcs != null && pxcs.getCsz() != null && pxcs.getCsz().equals("0")) {
			xszyxk = "0";
		}
		model.addAttribute("xszyxk", xszyxk);
		return "student/courseSelectManagement/tkkc";
	}

	/**
	 * 农大查看选课人数
	 */
	@RequestMapping("/student/courseSelect/stopCourse/courseList")
	@ResponseBody
	public List<Object[]> stopCourseList(HttpServletRequest request,
			HttpServletResponse response, String fajhh) {
		// 获得执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		return courseManagementService.stopCourseList(zxjxjhh, fajhh, xh);
	}
	
	/**
	 * 删除课程
	 *
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/delCourse/applyForRefund",produces="application/text;charset=UTF-8")
	@ResponseBody
	public String applyForRefund(Model model, HttpServletRequest request,
						   XkToRabbitObject xkToRabbitObject) {
		if(!csrfToken.isTokenValid(request)){
			return csrfToken.gotoAjaxIndex();
		}
		String mes = "";
		try{
			String ip = getIpAddr(request);
			String xh = AuthUtil.getCurrentUser().getIdNumber();
			String zxjxjhh = CommonUtils.queryCurrentXnxq();
			xkToRabbitObject.setZxjxjhh(zxjxjhh);
			xkToRabbitObject.setXh(xh);
			xkToRabbitObject.setIp(getIpAddr(request));
			String rwztdm = courseManagementService.queryRwztdm(xkToRabbitObject);
			if(StringUtils.isBlank(rwztdm) || !"06".equals(rwztdm)) {
				return "非选课状态的课程不允许删除！";
			}

			// 根据选课方式获得选课哈希
			@SuppressWarnings("rawtypes")
			HashMap xsxkHash=(HashMap) courseManagementService.queryXsxk(xh, zxjxjhh);

			//要删除的选课视图
			XsxkView xsxkView = (XsxkView) xsxkHash.get(xkToRabbitObject.getKch());

			XkJdView jd = courseManagementService.queryJd(zxjxjhh);
			String xxbm = commonService.queryParamValue();
			if(StringUtils.isNotBlank(xxbm) && xxbm.equals("100010")
					&& jd != null && StringUtils.isNotBlank(jd.getJdlxdm())
					&& jd.getJdlxdm().equals("005")) {

				String xkjdlxdm = xsxkView.getXkjdlxdm();
				if(StringUtils.isBlank(xkjdlxdm) || !xkjdlxdm.equals("005")) {
					return "以前阶段的选课不允许删除！";
				}
			}

			if (xsxkView != null) {
				
				String xdfsdm = xsxkView.getXdfsdm();
				XsxkView xsxkldView = null;
				if(xdfsdm.equals("01")) {
					String ldkch = courseManagementService.queryLdKch(xsxkView.getXsxkViewPk().getKch());
					if(StringUtils.isNotBlank(ldkch)) {
						xsxkldView = (XsxkView) xsxkHash.get(ldkch);
						//					if(xsxkldView == null) {
						//						RedisUtil.del(xh + "_xkHash_lock");
						//						return "已选课程中未查询到联动课程！";
						//					}
					}
				}

				// 判断课程是否可删
				mes = checkAllowDelKc(xsxkHash, xsxkView);
				
				if(xsxkldView != null) {
					mes = checkAllowDelKc(xsxkHash, xsxkldView);
					if (StringUtils.isNotBlank(mes)) {
						RedisUtil.del(xh + "_xkHash_lock");
						return "【联动退课】" + mes;
					}
				}

				if (mes == null || mes.equals("")) {
					// 直选式课程删除
					PxCsb csb = courseManagementService.loadPxcs("xkgl", "ycsfkyl");
					boolean ycsf = (csb != null && StringUtils.isNotBlank(csb.getCsz()) && csb.getCsz().equals("1"));
					String xkztdm = xsxkView.getXkztdm();
					String xkztdm1 = xsxkView.getXkztdm();
					if(!xkztdm.equals(xkztdm1)) {
						RedisUtil.del(xh + "_xkHash_lock");
						return "对不起，退课课程与联动课程选课状态不一致！";
					}
					if ((xkztdm == null)
							|| ("001".equals(xkztdm))
							|| ("002".equals(xkztdm))
							|| ("011".equals(xkztdm))
							|| ("012".equals(xkztdm))
							|| ("014".equals(xkztdm))
							|| ("016".equals(xkztdm))) {

						Number sfjf = courseManagementService.querySfjf(xkToRabbitObject.getXh(), xkToRabbitObject.getZxjxjhh().replace("-",""), xkToRabbitObject.getKch());
						KwBmjfKcbPk id = new KwBmjfKcbPk();
						id.setZxjxjhh(xkToRabbitObject.getZxjxjhh());
						id.setKch(xkToRabbitObject.getKch());
						id.setKxh(xkToRabbitObject.getKxh());
						id.setXh(xkToRabbitObject.getXh());
						KwBmjfKcb bmjfKcb = commonService.queryEntityById(KwBmjfKcb.class, id);
						if(sfjf != null && sfjf.intValue() == 1){
							// 删除直选课程
							//mes = delZXKc(xsxkView, ycsf);
							if("04".equals( xsxkView.getXdfsdm() ) ){
								bmjfKcb.setBmlx("cx");
							}else if("06".equals( xsxkView.getXdfsdm() ) ){
								bmjfKcb.setBmlx("fx");
							}
							bmjfKcb.setJfzt("W");
							commonService.doUpdate(bmjfKcb);
						}else{
							// 删除直选课程
							mes = delZXKc(xsxkView, ycsf, ip, xsxkldView, xkToRabbitObject);
							//课程删除最后mes
							if(mes == null || "".equals(mes)){
								commonService.doDelete(bmjfKcb);
								mes = "申请退课成功！";
							}
						}
					} else {
						if ((!"008".equals(xkztdm))
								&& (!"013".equals(xkztdm))) {
							String ztName = courseManagementService.queryXkzt(xkztdm);
							mes = "该课程当前状态为【"+ztName+"】，不允许删除！";
							return mes;
						} else {
							// 删除拟选数据
							mes = courseManagementService.delZYKc(xsxkView, ip, xsxkldView, ycsf, xkToRabbitObject);
							KwBmjfKcbPk id = new KwBmjfKcbPk();
							id.setZxjxjhh(xkToRabbitObject.getZxjxjhh());
							id.setKch(xkToRabbitObject.getKch());
							id.setKxh(xkToRabbitObject.getKxh());
							id.setXh(xkToRabbitObject.getXh());
							KwBmjfKcb bmjfKcb = commonService.queryEntityById(KwBmjfKcb.class, id);
							if("04".equals( xsxkView.getXdfsdm() ) ){
								bmjfKcb.setBmlx("cx");
							}else if("06".equals( xsxkView.getXdfsdm() ) ){
								bmjfKcb.setBmlx("fx");
							}
							bmjfKcb.setJfzt("W");
							commonService.doUpdate(bmjfKcb);
						}
					}

					// 写入未成功表中
//					if(mes == null || "".equals(mes)) {
//						courseManagementService.delCourseFromRedis(xsxkView, flag, ycsf);
//						mes = courseManagementService.txsb(xsxkView,xkToRabbitObject);
//					}
					//课程删除最后mes
					if(mes == null || "".equals(mes)){
						mes = "申请退费成功，请等待退费结果！";
					}
				}

			}
			return mes;
		} catch (Exception e) {
			e.printStackTrace();
			return "删除课程异常，请联系管理员处理！";
		}
	}

	/**
	 * 选课优先级调整
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/courseSelectPriority/index")
	public String courseSelectPriority(Model model, HttpSession session) {

		// 获得用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();

		// 获得执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();

		// 选课进度
		XkJdView jdb = courseManagementService.queryJd(zxjxjhh);

		// 是否有选课阶段
		String err;
		err = courseManagementService.checkXkjd(jdb);
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		// 学生选课
		List<Object[]> list = courseManagementService.queryVolunteerXsxkList(id, zxjxjhh);
		model.addAttribute("zynum", courseManagementService.queryZynum(id, zxjxjhh));
		model.addAttribute("list", list);

		return "student/courseSelectManagement/courseSelectPriority";
	}


	/**
	 * 抽签首页
	 * @param model
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/draw/index")
	public String draw(Model model, HttpSession session) {
		// 获得用户id
		String id = AuthUtil.getCurrentUser().getIdNumber();
		// 获得执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();
		// 选课进度
		XkJdView jdb = courseManagementService.queryJd(zxjxjhh);

		// 是否可以抽签
		String err = null;
		String xxbm = LicenseManger.getSchoolId();
		model.addAttribute("xxbm", xxbm);
		err = courseManagementService.checkCq(jdb);
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}

		// 学生选课
		HashMap<String, SelectCourseTableView> xsxkHash = courseManagementService.queryXsxkHashByDraw(id, zxjxjhh);
		if("100010".equals(xxbm)) {
			model.addAttribute("zynum", courseManagementService.queryZynum(id, zxjxjhh));
		}
		// 总学分
		Double zxf = getZxf(xsxkHash);

		model.addAttribute("xsxkHash", xsxkHash);
		model.addAttribute("zxf", zxf);

		return "student/courseSelectManagement/draw";
	}


	/**
	 * 抽签
	 * @param session
	 * @param model
	 * @param fajhh
	 * @param kch
	 * @param kxh
	 * @param response
	 * @param request
	 * @return
	 */
	@RequestMapping("/student/courseSelect/draw/index/drawCourse")
	@ResponseBody
	public UrpResult drawCourse(
			HttpSession session,
			Model model,
			String fajhh, String kch, String kxh,
			HttpServletResponse response,
			HttpServletRequest request) {
		Map<String, String> map = new HashMap<String, String>();
		if (!csrfToken.isTokenValid(request)) {
			map.put("result", csrfToken.gotoAjaxIndex());
			return UrpResult.ok(map);
		}
		map.put("token", session.getAttribute("token_in_session").toString());
		String result = courseManagementService.drawCourse(fajhh, kch, kxh);
		map.put("result", result);
		return UrpResult.ok(map);
	}
	
	/**
	 * 方案推荐选课页面
	 * 
	 * @param model
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/student/courseSelect/planRecommendCourse/index")
	public String planRecommendCourse(Model model, HttpServletRequest request, HttpSession session, String fajhh) {
		
		// 是否只允许重修选课
		String err = courseManagementService.checkCxkc();
		if (err != null) {
			model.addAttribute("err", err);
			return "student/courseSelectManagement/error";
		}
		
		// 执行教学计划号
		String zxjxjhh = courseManagementService.queryExecutiveEducationPlanNumber();
		
		String key = "";
		String jsonSection = null;
		try {
			key = "xk_pad_currentSectionTime";
			if (RedisUtil.exists(key)) {
				jsonSection = RedisUtil.get(key);
			} else {
				// 获取星期节次时间信息
				CodeTermSection section = courseManagementService.getSection(null);
				jsonSection = JSONObject.fromObject(section).toString();
				RedisUtil.setex(key.getBytes(), 6*60*60, jsonSection.getBytes());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		model.addAttribute("zxjxjhh", zxjxjhh);
		model.addAttribute("fajhh", fajhh);
		model.addAttribute("section", jsonSection);
		
		return "student/courseSelectManagement/fatjxk";
	}
	
	@RequestMapping("/student/courseSelect/selectCourse/queryPlanStatus")
	@ResponseBody
	public List<TreeNode> queryPlanStatus(String fajhh) {
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		List<Object[]> list = courseManagementService.queryPlanStatus(fajhh, xh);
		
		List<TreeNode> ts = new ArrayList<TreeNode>();
		if(CollectionUtils.isNotEmpty(list)) {
			Map<String, String> map = new HashMap<String, String>();
			for (int i = 0; i < list.size(); i++) {
				Object[] kc = list.get(i);
				
				String kzh = kc[1] == null ? "" : (kc[1] + "");
				if(!map.containsKey(kzh)) {
					TreeNode tn = new TreeNode();
					tn.setId(kzh);
					String kzm = kc[2] == null ? "" : (kc[2] + "");
					String xf = kc[3] == null ? "0" : (kc[3] + "");
					String wcxf = kc[4] == null ? "0" : (kc[4] + "");
					tn.setName("<i class='fa fa-kz blue' style='font-size: 17px;'></i> " + kzm + " <i title='完成学分' class='fa fa-check isuccess' style='font-size: 12px;'></i> " + wcxf + "/ <i title='最少学分' class='fa fa-star' style='font-size: 12px;'></i> " + xf);
					tn.setTitle(kzm);
					tn.setType("kz");
					String pid = kc[0] == null ? "" : (kc[0] + "");
					tn.setPId(pid);
					tn.setOpen(Float.parseFloat(xf) > Float.parseFloat(wcxf) ? true : false);
					ts.add(tn);
					map.put(kzh, "1");
				}
				
				String zxjxjhh = kc[5] == null ? "" : (kc[5] + "");
				if(StringUtils.isNotBlank(zxjxjhh)) {
					String xnxqId = kzh + "@" + zxjxjhh;
					if(!map.containsKey(xnxqId)) {
						TreeNode tn = new TreeNode();
						tn.setId(xnxqId);
						String xnxqm = kc[6] == null ? "" : (kc[6] + "");
						tn.setName("<i class='fa fa-calendar red' style='font-size: 17px;'></i> " + xnxqm);
						tn.setTitle(xnxqm);
						tn.setType("xq");
						tn.setPId(kzh);
						tn.setOpen(true);
						ts.add(tn);
						map.put(xnxqId, "1");
					}
					
					String kch = kc[7] == null ? "" : (kc[7] + "");
					String kcId = xnxqId + "@" + kch;
					if(!map.containsKey(kcId)) {
						TreeNode tn = new TreeNode();
						tn.setId(kcId);
						String kcm = kc[8] == null ? "" : (kc[8] + "");
						String xf = kc[9] == null ? "0" : (kc[9] + "");
						String zcj = kc[10] == null ? "0" : (kc[10] + "");
						tn.setName("<i class='fa fa-book orange2' style='font-size: 17px;'></i> <i style='"+ ("未修".equals(zcj) ? "color: red;" : "") +"'>" + kcm + "</i> <i title='学分' class='fa fa-star' style='font-size: 12px;'></i> " + xf + "| <i title='成绩' class='fa fa-pencil-square-o' style='font-size: 12px;'></i> " + zcj);
						tn.setTitle(kcm);
						tn.setType("kc");
						tn.setPId(xnxqId);
						ts.add(tn);
						map.put(kcId, "1");
					}
				}
			}
			
		}
		return ts;
	}
}
