<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>打印成绩单</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 打印成绩单页面样式 */
        .scorecard-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .scorecard-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .scorecard-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .scorecard-options {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .options-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .options-title i {
            color: var(--primary-color);
        }
        
        .options-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }
        
        .option-card {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: var(--padding-md);
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
            text-align: center;
        }
        
        .option-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .option-card.chinese-all {
            border-color: var(--success-color);
        }
        
        .option-card.chinese-pass {
            border-color: var(--info-color);
        }
        
        .option-card.english-all {
            border-color: var(--warning-color);
        }
        
        .option-card.english-pass {
            border-color: var(--error-color);
        }
        
        .option-icon {
            font-size: 32px;
            margin-bottom: var(--margin-sm);
        }
        
        .option-card.chinese-all .option-icon {
            color: var(--success-color);
        }
        
        .option-card.chinese-pass .option-icon {
            color: var(--info-color);
        }
        
        .option-card.english-all .option-icon {
            color: var(--warning-color);
        }
        
        .option-card.english-pass .option-icon {
            color: var(--error-color);
        }
        
        .option-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .option-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.3;
        }
        
        .tips-section {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
        }
        
        .tips-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--info-dark);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tips-content {
            font-size: var(--font-size-small);
            color: var(--info-dark);
            line-height: 1.4;
        }
        
        .tips-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .tips-list li {
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }
        
        .tips-list li:before {
            content: "•";
            color: var(--info-color);
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .preview-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            overflow-y: auto;
        }
        
        .preview-modal-content {
            background: var(--bg-primary);
            margin: 20px;
            border-radius: 8px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        
        .preview-modal-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1001;
        }
        
        .preview-modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .btn-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
        }
        
        .preview-modal-body {
            padding: 0;
        }
        
        .preview-iframe {
            width: 100%;
            height: 70vh;
            border: none;
            border-radius: 0 0 8px 8px;
        }
        
        @media (max-width: 480px) {
            .scorecard-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .scorecard-options,
            .tips-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .options-grid {
                grid-template-columns: 1fr;
            }
            
            .preview-modal-content {
                margin: 10px;
            }
            
            .preview-iframe {
                height: 60vh;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">打印成绩单</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 打印成绩单头部 -->
        <div class="scorecard-header">
            <div class="scorecard-title">打印成绩单</div>
            <div class="scorecard-desc">选择成绩单类型进行查看和打印</div>
        </div>
        
        <!-- 成绩单选项 -->
        <div class="scorecard-options">
            <div class="options-title">
                <i class="ace-icon fa fa-file-text"></i>
                成绩单类型
            </div>
            
            <div class="options-grid">
                <div class="option-card chinese-all" onclick="selectScoreCard('zwcjd_all');">
                    <div class="option-icon">
                        <i class="ace-icon fa fa-file-text-o"></i>
                    </div>
                    <div class="option-title">中文全部成绩</div>
                    <div class="option-desc">包含所有课程成绩的中文成绩单</div>
                </div>
                
                <div class="option-card chinese-pass" onclick="selectScoreCard('zwcjd_jg');">
                    <div class="option-icon">
                        <i class="ace-icon fa fa-check-circle-o"></i>
                    </div>
                    <div class="option-title">中文及格成绩</div>
                    <div class="option-desc">仅包含及格课程成绩的中文成绩单</div>
                </div>
                
                <div class="option-card english-all" onclick="selectScoreCard('ywcjd_all');">
                    <div class="option-icon">
                        <i class="ace-icon fa fa-globe"></i>
                    </div>
                    <div class="option-title">英文全部成绩</div>
                    <div class="option-desc">包含所有课程成绩的英文成绩单</div>
                </div>
                
                <div class="option-card english-pass" onclick="selectScoreCard('ywcjd_jg');">
                    <div class="option-icon">
                        <i class="ace-icon fa fa-check-square-o"></i>
                    </div>
                    <div class="option-title">英文及格成绩</div>
                    <div class="option-desc">仅包含及格课程成绩的英文成绩单</div>
                </div>
            </div>
        </div>
        
        <!-- 使用提示 -->
        <div class="tips-section">
            <div class="tips-title">
                <i class="ace-icon fa fa-info-circle"></i>
                使用提示
            </div>
            <div class="tips-content">
                <ul class="tips-list">
                    <li>点击上方选项卡选择需要的成绩单类型</li>
                    <li>系统将自动生成PDF格式的成绩单</li>
                    <li>可以在预览窗口中查看成绩单内容</li>
                    <li>支持下载和打印功能</li>
                    <li>如遇问题请联系教务处</li>
                </ul>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>生成中...</span>
        </div>
    </div>
    
    <!-- 预览模态框 -->
    <div class="preview-modal" id="previewModal">
        <div class="preview-modal-content">
            <div class="preview-modal-header">
                <div class="preview-modal-title" id="previewTitle">成绩单预览</div>
                <button class="btn-close" onclick="closePreview();">
                    <i class="ace-icon fa fa-times"></i>
                </button>
            </div>
            <div class="preview-modal-body">
                <iframe class="preview-iframe" id="previewIframe" src=""></iframe>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentScoreCardType = '';

        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 选择成绩单类型
        function selectScoreCard(cjdlx) {
            currentScoreCardType = cjdlx;
            showLoading(true);

            $.ajax({
                url: "/student/integratedQuery/scoreQuery/scoreCard/query",
                type: "post",
                data: "cjdlx=" + cjdlx,
                dataType: "json",
                success: function(response) {
                    if (response.result > 0) {
                        showScoreCard(cjdlx);
                    } else {
                        showError("对不起，未找到相应的成绩单！");
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示成绩单
        function showScoreCard(cjdlx) {
            const url = "/pdf/web/viewer.html?file=" + encodeURIComponent("/student/integratedQuery/scoreQuery/scoreCard/courseInfo/showCard?cjdlx=" + cjdlx);
            
            // 设置预览标题
            const titles = {
                'zwcjd_all': '中文全部成绩单',
                'zwcjd_jg': '中文及格成绩单',
                'ywcjd_all': '英文全部成绩单',
                'ywcjd_jg': '英文及格成绩单'
            };
            
            $('#previewTitle').text(titles[cjdlx] || '成绩单预览');
            $('#previewIframe').attr('src', url);
            $('#previewModal').show();
        }

        // 关闭预览
        function closePreview() {
            $('#previewModal').hide();
            $('#previewIframe').attr('src', '');
        }

        // 刷新数据
        function refreshData() {
            // 刷新页面
            location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#previewModal').click(function(e) {
            if (e.target === this) {
                closePreview();
            }
        });
    </script>
</body>
</html>
