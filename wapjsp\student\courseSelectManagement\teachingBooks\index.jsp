<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>教材管理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 教材管理页面样式 */
        .books-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .books-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .books-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            background: var(--warning-light);
            color: var(--warning-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--warning-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-section i {
            color: var(--warning-color);
            margin-right: 8px;
        }
        
        .tabs-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            background: var(--bg-tertiary);
            padding: 0;
            border-bottom: 1px solid var(--divider-color);
            overflow-x: auto;
            white-space: nowrap;
        }
        
        .tabs-nav {
            display: flex;
            min-width: max-content;
        }
        
        .tab-item {
            padding: var(--padding-md);
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-base);
            white-space: nowrap;
            text-align: center;
            line-height: 1.3;
        }
        
        .tab-item.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: var(--bg-primary);
        }
        
        .tab-content {
            padding: var(--padding-md);
            min-height: 200px;
        }
        
        .books-section {
            margin-bottom: var(--margin-md);
        }
        
        .books-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .books-section-title i {
            color: var(--success-color);
        }
        
        .book-item {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--primary-color);
        }
        
        .book-required {
            border-left-color: var(--error-color);
        }
        
        .book-optional {
            border-left-color: var(--success-color);
        }
        
        .book-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .book-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .book-content {
            flex: 1;
        }
        
        .book-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .book-course {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .book-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .book-price {
            background: var(--warning-light);
            color: var(--warning-dark);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-small);
            font-weight: 500;
            text-align: center;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-required {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-optional {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-selected {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .book-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-book-action {
            flex: 1;
            min-width: 60px;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-select {
            background: var(--success-color);
            color: white;
        }
        
        .btn-unselect {
            background: var(--error-color);
            color: white;
        }
        
        .btn-restore {
            background: var(--info-color);
            color: white;
        }
        
        .btn-remove {
            background: var(--error-color);
            color: white;
        }
        
        .book-switch {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: var(--margin-sm);
        }
        
        .switch-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .switch-container {
            position: relative;
            width: 50px;
            height: 24px;
        }
        
        .switch-input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--text-disabled);
            transition: .4s;
            border-radius: 24px;
        }
        
        .switch-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        .switch-input:checked + .switch-slider {
            background-color: var(--success-color);
        }
        
        .switch-input:checked + .switch-slider:before {
            transform: translateX(26px);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .batch-actions {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: none;
        }
        
        .batch-actions-title {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .batch-buttons {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-batch {
            flex: 1;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
        }
        
        .btn-batch.danger {
            background: var(--error-color);
        }
        
        @media (max-width: 480px) {
            .books-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .tabs-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .book-details {
                grid-template-columns: 1fr;
            }
            
            .book-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">教材管理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 教材管理头部 -->
        <div class="books-header">
            <div class="books-title">教材管理</div>
            <div class="books-desc">选择和管理课程教材</div>
        </div>
        
        <!-- 提示信息 -->
        <c:if test="${isAccess == 0}">
            <div class="notice-section">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                当前非正选阶段或学生选教材开关未打开，无法选定教材！
            </div>
        </c:if>
        
        <!-- 选项卡内容 -->
        <div class="tabs-section">
            <div class="tabs-header">
                <div class="tabs-nav" id="tabsNav">
                    <c:if test="${isAccess == 1 && '100013' != schoolId}">
                        <button class="tab-item active" data-tab="available" onclick="switchTab('available', this);">
                            教材列表
                        </button>
                    </c:if>
                    <button class="tab-item <c:if test="${isAccess == 0 || '100013' == schoolId}">active</c:if>" data-tab="selected" onclick="switchTab('selected', this);">
                        已选教材
                    </button>
                    <c:if test="${'100013' == schoolId}">
                        <button class="tab-item" data-tab="deleted" onclick="switchTab('deleted', this);">
                            已删除教材
                        </button>
                    </c:if>
                </div>
            </div>
            
            <div class="tab-content">
                <div class="books-section">
                    <div class="books-section-title">
                        <i class="ace-icon fa fa-book"></i>
                        <span id="sectionTitle">教材列表</span>
                    </div>
                    
                    <div id="booksList">
                        <!-- 动态加载教材列表 -->
                    </div>
                </div>
                
                <!-- 批量操作 -->
                <div class="batch-actions" id="batchActions">
                    <div class="batch-actions-title">批量操作</div>
                    <div class="batch-buttons">
                        <button class="btn-batch" onclick="batchSelect();">
                            <i class="ace-icon fa fa-check"></i>
                            批量选定
                        </button>
                        <button class="btn-batch danger" onclick="batchRemove();">
                            <i class="ace-icon fa fa-trash"></i>
                            批量删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-book"></i>
            <div>暂无教材数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let booksData = [];
        let currentTab = 'available';
        let selectedBooks = new Set();
        let schoolId = '${schoolId}';
        let isAccess = '${isAccess}';

        $(function() {
            initPage();
            initTabs();
            loadBooks();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();

            // 根据权限设置默认选项卡
            if (isAccess == '0' || schoolId == '100013') {
                currentTab = 'selected';
                switchTab('selected', $('.tab-item[data-tab="selected"]')[0]);
            }
        }

        // 初始化选项卡
        function initTabs() {
            // 选项卡切换事件已在HTML中绑定
        }

        // 切换选项卡
        function switchTab(tabType, element) {
            if (currentTab === tabType) return;

            // 更新选项卡状态
            $('.tab-item').removeClass('active');
            $(element).addClass('active');

            // 设置当前选项卡
            currentTab = tabType;

            // 更新标题
            updateSectionTitle();

            // 清空选择状态
            selectedBooks.clear();
            updateBatchActions();

            // 重新加载数据
            loadBooks();
        }

        // 更新区域标题
        function updateSectionTitle() {
            let title = '';
            switch (currentTab) {
                case 'available':
                    title = '教材列表';
                    break;
                case 'selected':
                    title = '已选教材';
                    break;
                case 'deleted':
                    title = '已删除教材';
                    break;
                default:
                    title = '教材列表';
            }
            $('#sectionTitle').text(title);
        }

        // 加载教材数据
        function loadBooks() {
            showLoading(true);

            let type = 0; // 默认教材列表
            switch (currentTab) {
                case 'available':
                    type = 0;
                    break;
                case 'selected':
                    type = 1;
                    break;
                case 'deleted':
                    type = 2;
                    break;
            }

            $.ajax({
                url: "/student/courseSelect/books/dealBooks/queryList/" + type,
                type: "post",
                dataType: "json",
                success: function(data) {
                    if (data && data.length > 0) {
                        booksData = data;
                        renderBooksList();
                        showEmptyState(false);
                    } else {
                        booksData = [];
                        renderBooksList();
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    showEmptyState(true);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染教材列表
        function renderBooksList() {
            const container = $('#booksList');
            container.empty();

            if (booksData.length === 0) {
                return;
            }

            booksData.forEach(function(book, index) {
                const itemHtml = createBookItem(book, index);
                container.append(itemHtml);
            });
        }

        // 创建教材项目HTML
        function createBookItem(book, index) {
            // 数据结构：[课程名, 课程属性, 教材名称, 出版社, 主编, 单价, 版次, 出版日期, ISBN, 是否选定, 课程号, 课序号, 教材编号, 是否必购]
            const courseName = book[0] || '';
            const courseAttr = book[1] || '';
            const bookName = book[2] || '';
            const publisher = book[3] || '';
            const author = book[4] || '';
            const price = book[5] || '';
            const edition = book[6] || '';
            const publishDate = book[7] || '';
            const isbn = book[8] || '';
            const isSelected = book[9] || '0';
            const courseCode = book[10] || '';
            const courseSeq = book[11] || '';
            const bookCode = book[12] || '';
            const isRequired = book[13] || '0';

            // 获取状态信息
            const statusInfo = getBookStatusInfo(isRequired, isSelected);

            // 构建操作按钮
            let actionButtons = '';
            const bookId = `${courseCode},${courseSeq},${bookCode}`;

            if (currentTab === 'available') {
                // 教材列表页面
                if (schoolId !== '100006') {
                    actionButtons += `
                        <div class="book-actions">
                            <label class="book-switch">
                                <span class="switch-label">选定教材</span>
                                <div class="switch-container">
                                    <input type="checkbox" class="switch-input" ${isSelected === '1' ? 'checked' : ''}
                                           onchange="toggleBookSelection('${bookId}', this.checked)">
                                    <span class="switch-slider"></span>
                                </div>
                            </label>
                        </div>
                    `;
                }
            } else if (currentTab === 'selected') {
                // 已选教材页面
                if (schoolId === '100013' && isAccess === '1') {
                    actionButtons += `
                        <div class="book-actions">
                            <button class="btn-book-action btn-remove" onclick="removeBook('${bookId}');">
                                <i class="ace-icon fa fa-trash"></i>
                                <span>删除</span>
                            </button>
                        </div>
                    `;
                }
            } else if (currentTab === 'deleted') {
                // 已删除教材页面
                if (schoolId === '100013' && isAccess === '1') {
                    actionButtons += `
                        <div class="book-actions">
                            <button class="btn-book-action btn-restore" onclick="restoreBook('${bookId}');">
                                <i class="ace-icon fa fa-undo"></i>
                                <span>恢复</span>
                            </button>
                        </div>
                    `;
                }
            }

            return `
                <div class="book-item ${statusInfo.class}" data-book-id="${bookId}">
                    <div class="book-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            ${currentTab === 'available' && schoolId !== '100006' ? `
                                <input type="checkbox" class="book-checkbox" value="${bookId}"
                                       onchange="updateBookSelection(this)" style="margin-right: 12px; margin-top: 4px;">
                            ` : ''}
                            <div class="book-index">${index + 1}</div>
                            <div class="book-content">
                                <div class="book-name">${bookName}</div>
                                <div class="book-course">${courseName} (${courseAttr})</div>
                            </div>
                        </div>
                        <div>
                            <span class="status-badge ${statusInfo.statusClass}">${statusInfo.text}</span>
                        </div>
                    </div>

                    <div class="book-details">
                        <div class="detail-item">
                            <span class="detail-label">出版社</span>
                            <span>${publisher}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">主编</span>
                            <span>${author}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">版次</span>
                            <span>${edition}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">出版日期</span>
                            <span>${publishDate}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">ISBN</span>
                            <span>${isbn}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">单价</span>
                            <span class="book-price">¥${price}</span>
                        </div>
                    </div>

                    ${actionButtons}
                </div>
            `;
        }

        // 获取教材状态信息
        function getBookStatusInfo(isRequired, isSelected) {
            let text = '';
            let statusClass = '';
            let itemClass = '';

            if (isRequired === '1') {
                text = '必购教材';
                statusClass = 'status-required';
                itemClass = 'book-required';
            } else {
                if (isSelected === '1') {
                    text = '已选定';
                    statusClass = 'status-selected';
                    itemClass = 'book-optional';
                } else {
                    text = '可选教材';
                    statusClass = 'status-optional';
                    itemClass = 'book-optional';
                }
            }

            return { text, statusClass, class: itemClass };
        }

        // 切换教材选择状态
        function toggleBookSelection(bookId, isSelected) {
            const params = bookId + "," + (isSelected ? "1" : "0");
            saveBookSelection(params, 'single');
        }

        // 更新教材选择状态
        function updateBookSelection(checkbox) {
            if (checkbox.checked) {
                selectedBooks.add(checkbox.value);
            } else {
                selectedBooks.delete(checkbox.value);
            }
            updateBatchActions();
        }

        // 更新批量操作显示
        function updateBatchActions() {
            const batchContainer = $('#batchActions');
            if (currentTab === 'available' && selectedBooks.size > 0 && schoolId !== '100006') {
                batchContainer.show();
            } else {
                batchContainer.hide();
            }
        }

        // 批量选定
        function batchSelect() {
            if (selectedBooks.size === 0) {
                showError('请先选择教材');
                return;
            }

            let params = '';
            selectedBooks.forEach(function(bookId) {
                if (params) params += '|';
                params += bookId + ',1';
            });

            saveBookSelection(params, 'batch');
        }

        // 批量删除
        function batchRemove() {
            if (selectedBooks.size === 0) {
                showError('请先选择教材');
                return;
            }

            let params = '';
            selectedBooks.forEach(function(bookId) {
                if (params) params += '|';
                params += bookId + ',0';
            });

            saveBookSelection(params, 'batch');
        }

        // 保存教材选择
        function saveBookSelection(params, type) {
            if (!params) {
                showError('无数据！');
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/courseSelect/books/dealBooks/saveJc",
                type: "post",
                data: "tokenValue=" + $("#tokenValue").val() + "&param=" + params,
                dataType: "json",
                success: function(data) {
                    if (data.result && data.result.indexOf("/") !== -1) {
                        window.location.href = data.result;
                    } else {
                        $("#tokenValue").val(data.token);
                        if (data.result === "ok") {
                            showSuccess("操作成功！");
                            if (type === 'batch') {
                                selectedBooks.clear();
                                updateBatchActions();
                            }
                            loadBooks();
                        } else {
                            showError(data.result);
                        }
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 删除教材
        function removeBook(bookId) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm("确认删除该教材？", function(confirmed) {
                    if (confirmed) {
                        const params = bookId + ",0";
                        saveBookAction(params, 'remove');
                    }
                });
            } else {
                if (confirm("确认删除该教材？")) {
                    const params = bookId + ",0";
                    saveBookAction(params, 'remove');
                }
            }
        }

        // 恢复教材
        function restoreBook(bookId) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm("确认恢复该教材？", function(confirmed) {
                    if (confirmed) {
                        const params = bookId + ",1";
                        saveBookAction(params, 'restore');
                    }
                });
            } else {
                if (confirm("确认恢复该教材？")) {
                    const params = bookId + ",1";
                    saveBookAction(params, 'restore');
                }
            }
        }

        // 保存教材操作
        function saveBookAction(params, action) {
            showLoading(true);

            $.ajax({
                url: "/student/courseSelect/books/dealBooks/saveJc",
                type: "post",
                data: "tokenValue=" + $("#tokenValue").val() + "&param=" + params,
                dataType: "json",
                success: function(data) {
                    if (data.result && data.result.indexOf("/") !== -1) {
                        window.location.href = data.result;
                    } else {
                        $("#tokenValue").val(data.token);
                        if (data.result === "ok") {
                            showSuccess("操作成功！");
                            loadBooks();
                        } else {
                            showError(data.result);
                        }
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.tabs-section').hide();
            } else {
                $('#emptyState').hide();
                $('.tabs-section').show();
            }
        }

        // 刷新数据
        function refreshData() {
            loadBooks();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
