<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ page contentType="text/html;charset=UTF-8" language="java"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实习报告管理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实习报告管理页面样式 */
        .reports-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .reports-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .reports-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--primary-color);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input, .form-select {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-md);
        }
        
        .btn-search {
            flex: 1;
            background: var(--info-color);
            color: white;
        }
        
        .reports-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .reports-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .reports-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .reports-section-title i {
            color: var(--success-color);
        }
        
        .report-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .report-item:last-child {
            border-bottom: none;
        }
        
        .report-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .report-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .report-content {
            flex: 1;
        }
        
        .report-task-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .report-course-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .report-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-uploaded {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-not-uploaded {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-pending {
            background: var(--text-disabled);
            color: white;
        }
        
        .report-arrangement {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .report-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-report-action {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-upload {
            background: var(--success-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .reports-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .search-section,
            .reports-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .report-details {
                grid-template-columns: 1fr;
            }
            
            .search-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}" />
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实习报告管理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 实习报告头部 -->
        <div class="reports-header">
            <div class="reports-title">实习报告管理</div>
            <div class="reports-desc">上传和管理实习报告</div>
        </div>
        
        <!-- 查询条件 -->
        <div class="search-section">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            
            <form id="queryInfo" name="queryInfo" class="search-form">
                <div class="form-group">
                    <label class="form-label">学年学期</label>
                    <select name="xnxq" id="executiveEducationPlanNumber" class="form-select">
                        <option value="">全部</option>
                        <cache:query var="view" region="jh_zxjxjhb_view" orderby="zxjxjhh desc" />
                        <c:forEach items="${view}" var="view">
                            <option value="${view.zxjxjhh}" <c:if test="${view.zxjxjhh==xnxq}">selected</c:if>>${view.zxjxjhm}</option>
                        </c:forEach>
                    </select>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">实习课程号</label>
                        <input type="text" name="sxkch" id="sxkch" class="form-input" placeholder="请输入课程号">
                    </div>
                    <div class="form-group">
                        <label class="form-label">任务名称</label>
                        <input type="text" name="sxrwmc" id="sxrwmc" class="form-input" placeholder="请输入任务名称">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">实习类别</label>
                        <select id="sxlb" name="sxlb" class="form-select">
                            <option value="">全部</option>
                            <cache:query var="sxlbs" region="SX_CODE_SXLBB" />
                            <c:forEach items="${sxlbs}" var="sxlb">
                                <option value="${sxlb.sxlbm}">${sxlb.sxlb}</option>
                            </c:forEach>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">实习性质</label>
                        <select id="sxxz" name="sxxz" class="form-select">
                            <option value="">全部</option>
                            <cache:query var="sxxzs" region="SX_CODE_SXXZB"/>
                            <c:forEach items="${sxxzs}" var="sxxz">
                                <option value="${sxxz.sxxzm}">${sxxz.sxxz}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
            </form>
            
            <div class="search-actions">
                <button class="btn-mobile btn-search" onclick="searchReports();">
                    <i class="ace-icon fa fa-search"></i>
                    <span>查询</span>
                </button>
            </div>
        </div>
        
        <!-- 实习报告列表 -->
        <div class="reports-section">
            <div class="reports-section-header">
                <div class="reports-section-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    我的实习信息
                </div>
            </div>
            
            <div id="reportsList">
                <!-- 动态加载报告列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreReports();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无实习报告数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>查询中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let reportsData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let xnxq = "${xnxq}";

        $(function() {
            initPage();
            loadReports(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 搜索报告
        function searchReports() {
            loadReports(1, true);
        }

        // 加载更多报告
        function loadMoreReports() {
            if (hasMore) {
                loadReports(currentPage + 1, false);
            }
        }

        // 加载报告数据
        function loadReports(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }

            showLoading(true);

            const params = $(document.queryInfo).serialize() + "&pageNum=" + page + "&pageSize=" + pageSize;

            $.ajax({
                url: "/student/internship/sxbggl/findSxrw",
                type: "get",
                data: params,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && response.records && response.records.length > 0) {
                        if (reset) {
                            reportsData = response.records;
                        } else {
                            reportsData = reportsData.concat(response.records);
                        }

                        totalCount = response.pageContext.totalCount;
                        currentPage = page;
                        hasMore = reportsData.length < totalCount;

                        renderReportsList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            reportsData = [];
                            renderReportsList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    showEmptyState(true);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染报告列表
        function renderReportsList(reset = false) {
            const container = $('#reportsList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : reportsData.length - pageSize;
            const endIndex = reportsData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (reportsData[i]) {
                    const itemHtml = createReportItem(reportsData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建报告项目HTML
        function createReportItem(report, index) {
            // 获取状态信息
            const uploadStatusInfo = getUploadStatusInfo(report.SXBGSCF);
            const approvalStatusInfo = getApprovalStatusInfo(report.SFJLM);
            const needUploadInfo = getNeedUploadInfo(report.SFSCSXBG);

            // 构建操作按钮
            let actionButtons = '';
            const queryXnxq = $('#executiveEducationPlanNumber').val();

            if (queryXnxq !== xnxq) {
                // 非当前学期，只能查看
                actionButtons = `
                    <div class="report-actions">
                        <button class="btn-report-action btn-view" onclick="showDetails('${report.ID}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                    </div>
                `;
            } else {
                // 当前学期
                if (report.SFJLM === 'approve') {
                    // 已审批通过，只能查看
                    actionButtons = `
                        <div class="report-actions">
                            <button class="btn-report-action btn-view" onclick="showDetails('${report.ID}');">
                                <i class="ace-icon fa fa-eye"></i>
                                <span>查看</span>
                            </button>
                        </div>
                    `;
                } else {
                    // 未审批或审批不通过
                    actionButtons = `
                        <div class="report-actions">
                            <button class="btn-report-action btn-view" onclick="showDetails('${report.ID}');">
                                <i class="ace-icon fa fa-eye"></i>
                                <span>查看</span>
                            </button>
                    `;

                    if (report.SFSCSXBG === '1') {
                        // 需要上传报告
                        actionButtons += `
                            <button class="btn-report-action btn-upload" onclick="uploadSxbg('${report.ID}');">
                                <i class="ace-icon fa fa-cloud-upload"></i>
                                <span>上传报告</span>
                            </button>
                        `;
                    }

                    actionButtons += `</div>`;
                }
            }

            return `
                <div class="report-item">
                    <div class="report-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="report-index">${index + 1}</div>
                            <div class="report-content">
                                <div class="report-task-name">${report.KCM || ''}</div>
                                <div class="report-course-info">课程号：${report.KCH || ''} | 分组号：${report.KXH || ''}</div>
                            </div>
                        </div>
                        <div>
                            <span class="status-badge ${uploadStatusInfo.class}">${uploadStatusInfo.text}</span>
                        </div>
                    </div>

                    <div class="report-details">
                        <div class="detail-item">
                            <span class="detail-label">是否需要上传</span>
                            <span>${needUploadInfo.text}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">审批结论</span>
                            <span class="status-badge ${approvalStatusInfo.class}">${approvalStatusInfo.text}</span>
                        </div>
                    </div>

                    ${report.WWW ? `
                    <div class="report-arrangement">
                        <strong>实习安排：</strong>${report.WWW}
                    </div>
                    ` : ''}

                    ${actionButtons}
                </div>
            `;
        }

        // 获取上传状态信息
        function getUploadStatusInfo(sxbgscf) {
            if (sxbgscf === "1") {
                return { text: '已上传', class: 'status-uploaded' };
            } else {
                return { text: '未上传', class: 'status-not-uploaded' };
            }
        }

        // 获取审批状态信息
        function getApprovalStatusInfo(sfjlm) {
            if (sfjlm === "") {
                return { text: '未审批', class: 'status-pending' };
            } else if (sfjlm === "approve") {
                return { text: '审批通过', class: 'status-approved' };
            } else {
                return { text: '审批不通过', class: 'status-rejected' };
            }
        }

        // 获取是否需要上传信息
        function getNeedUploadInfo(sfscsxbg) {
            if (sfscsxbg === "1") {
                return { text: '需要上传' };
            } else {
                return { text: '无需上传' };
            }
        }

        // 上传实习报告
        function uploadSxbg(rwId) {
            const url = "/student/internship/sxbggl/uploadSxbg/" + rwId;

            if (parent && parent.addTab) {
                parent.addTab('上传实习报告', url);
            } else {
                location.href = url;
            }
        }

        // 查看详情
        function showDetails(rwId) {
            const url = "/student/internship/sxbggl/showSxbgDetails/" + rwId;

            if (parent && parent.addTab) {
                parent.addTab('实习报告详情', url);
            } else {
                location.href = url;
            }
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && reportsData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (reportsData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.reports-section').hide();
            } else {
                $('#emptyState').hide();
                $('.reports-section').show();
            }
        }

        // 刷新数据
        function refreshData() {
            loadReports(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
