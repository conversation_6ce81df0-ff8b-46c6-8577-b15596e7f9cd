package educationalAdministration.student.personalManagement.controller;

import com.urpSoft.business.utils.UrpResult;
import com.urpSoft.core.data.query.component.QueryInfo;
import com.urpSoft.core.document.common.service.ICommonExportService;
import com.urpSoft.core.pagination.page.Page;
import com.urpSoft.core.pagination.service.IPageService;
import com.urpSoft.core.service.IBaseService;
import com.urpSoft.core.util.AuthUtil;
import com.urpSoft.core.util.CSRFToken;
import educationalAdministration.dictionary.entity.LwKtckwxb;
import educationalAdministration.dictionary.entity.PrintTemplateTable;
import educationalAdministration.dictionary.entity.PrintTemplateTablePK;
import educationalAdministration.dictionary.entity.SysColConfig;
import educationalAdministration.dictionary.entity.SysYwhdkzb;
import educationalAdministration.student.common.utils.CommonUtils;
import educationalAdministration.student.individualApplication.applyCommon.entity.EaApplysQu;
import educationalAdministration.student.individualApplication.applyCommon.service.ApplyCommonService;
import educationalAdministration.student.personalManagement.entity.CkwxList;
import educationalAdministration.student.personalManagement.entity.LwGcnrFjlxzb;
import educationalAdministration.student.personalManagement.entity.LwGcnrLmjzb;
import educationalAdministration.student.personalManagement.entity.LwGcnrSzlqzfwb;
import educationalAdministration.student.personalManagement.entity.LwGcnrmbb;
import educationalAdministration.student.personalManagement.entity.LwGcnrzdb;
import educationalAdministration.student.personalManagement.entity.LwJtbgb;
import educationalAdministration.student.personalManagement.entity.LwJtbgnrb;
import educationalAdministration.student.personalManagement.entity.LwKtbgb;
import educationalAdministration.student.personalManagement.entity.LwKtbgnrb;
import educationalAdministration.student.personalManagement.entity.LwLwxzjhb;
import educationalAdministration.student.personalManagement.entity.LwPcb;
import educationalAdministration.student.personalManagement.entity.LwXtb;
import educationalAdministration.student.personalManagement.entity.LwZqjcb;
import educationalAdministration.student.personalManagement.entity.LwZqjcnrb;
import educationalAdministration.student.personalManagement.entity.XsXjb;
import educationalAdministration.student.personalManagement.service.ProcessManagementService;
import educationalAdministration.system.minio.utils.MinioUtils;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.sql.Blob;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Controller
public class ProcessManagementController {

	@Resource
	private IPageService pageService;

	@Resource
	private IBaseService baseService;

	@Resource
	private ICommonExportService commonExportService;

	private CSRFToken csrfToken = CSRFToken.getInstance();

	@Resource
	private ProcessManagementService processManagementService;

	@Resource
	private ApplyCommonService applyCommonService;

	@SuppressWarnings("unused")
	@ModelAttribute
	private void doGetCurrentUser() {
		AuthUtil.getCurrentUser();
	}

	/**
	 * 进入开题报告页面
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/student/personalManagement/processManagement/ktbgIndex")
	public String ktbgIndex(Model model) {

		String xh = AuthUtil.getCurrentUser().getIdNumber();
		String sql = "select count(1) from lw_kxtbmd where xh = '" + xh + "'";
		long count = processManagementService.queryCounBySql(sql);
		if (count < 1) {
			model.addAttribute("errorMessage", "您不在当前论文批次的学生名单中！");
			return "/student/personalManagement/processManagement/index";
		}

		sql = "  SELECT count(1)  FROM lw_pcb b  WHERE b.pczt = '1' ORDER BY b.pczt";
		long pcnum = processManagementService.queryCounBySql(sql);
		if (pcnum < 1) {
			model.addAttribute("errorMessage", "没有开启的论文批次！");
			return "/student/personalManagement/processManagement/index";
		}

		sql = " SELECT  count(1) FROM lw_kxtbmd a,lw_pcb b  WHERE a.pch = b.pch AND a.xh = '" + xh + "' AND b.pczt = '1'  ";
		pcnum = processManagementService.queryCounBySql(sql);
		if (pcnum < 1) {
			model.addAttribute("errorMessage", "您不在当前论文批次的学生名单中！");
			return "/student/personalManagement/processManagement/index";
		}


		String schoolCode = CommonUtils.queryParamValue();
		if ("100030".equals(schoolCode)) {
			List<Object[]> lwpcxxbList = this.processManagementService.loadLwpcxx(xh);
			if (lwpcxxbList != null && lwpcxxbList.size() > 0) {
				for (Object[] obj : lwpcxxbList) {
					if (!(obj[8] != null && "1".equals(obj[8].toString()))) {
						model.addAttribute("errorMessage", "没有确认【承诺与使用授权书】,点击<a style='cursor: pointer;text-decoration: underline;' onclick='returnOther();'>这里</a>前往确认！");
						return "/student/personalManagement/processManagement/index";
					}
				}

			} else {
				model.addAttribute("errorMessage", "您不在当前论文批次的学生名单中！");
				return "/student/personalManagement/processManagement/index";
			}
		}


		sql = "select count(1) from ea_process where apply_type ='10016' and in_use = '1'";
		count = processManagementService.queryCounBySql(sql);
		String errorMessage = "";
		boolean flag = true;
		if (count == 0) {
			errorMessage = "开题报告无审批流程，请联系管理员！";
			flag = false;
		}
		if (flag) {
			sql = "select count(1) from EA_PROCESS_LINK where EAP_CODE in(select EAP_CODE from ea_process where apply_type ='10016' and in_use = '1') and in_use = '1'";
			count = processManagementService.queryCounBySql(sql);
			if (count == 0) {
				errorMessage = "开题报告未定义审批角色，请联系管理员！";
				flag = false;
			}
		}
		SysYwhdkzb ywhdkzb = processManagementService.queryEntityById(SysYwhdkzb.class, "10016");

		if (flag) {
			String ckFlag = applyCommonService.queryProcessApplyWhiteList("lw_ktbgb", xh);
			if ("0".equals(ckFlag)) {
				if (ywhdkzb != null) {
					if ("1".equals(ywhdkzb.getQyf())) {
						if ("0".equals(ywhdkzb.getKg())) {
							errorMessage = "当前开关已关闭！";
							flag = false;
						} else {
							sql = "select count(1) from sys_ywhdkzb where id = '10016' and (to_char(sysdate,'yyyymmddHH24mi') between kssj and jssj) and kg = '1'";
							count = processManagementService.queryCounBySql(sql);
							if (count == 0) {
								errorMessage = "当前操作不在允许时间范围！";
								flag = false;
							}
						}
					} else {
						errorMessage = "当前功能尚未启用！";
						flag = false;
					}
				}
			}
		}
		Object[] xtb = processManagementService.queryLwXtb(xh);
		boolean flage = true;
		if (xtb != null) {
			model.addAttribute("xnxq", xtb[0].toString());
			model.addAttribute("tmbh", xtb[1].toString());
			model.addAttribute("ywhdkzb", ywhdkzb);
		} else {
			errorMessage = "尚未确认选题！";
			flag = false;
		}

		if ("100015".equals(schoolCode)) {
			//增加前置环节检查：只有指导教师已完成任务书环节（按选题题目确认lw_rwsb存在lw_rwsb.shzt = '3'的记录），选题学生才能填报开题报告。
			sql = " SELECT count(1) FROM lw_rwsb a WHERE a.xnxq = '" + xtb[0].toString() + "' AND a.tmbh = '" + xtb[1].toString() + "' AND a.shzt = '3' ";
			long rwsnum = processManagementService.queryCounBySql(sql);
			if (rwsnum < 1) {
				model.addAttribute("flage", false);
				model.addAttribute("errorMessage", "指导教师尚未完成任务书环节！");
				return "/student/personalManagement/processManagement/index";
			}
		}


		model.addAttribute("gclx", "01");
		if (flag) {
			XsXjb xjb = baseService.queryEntityById(XsXjb.class, xh);
			String njdm = xjb.getNjdm();
			String xsh = xjb.getXsh();
			sql = "select count(1) from lw_yxjdkzb where lwjdm = '04' and xsh ='" + xsh + "' and kg='1' and (to_char(sysdate,'yyyymmddHH24mi') between kssj and jssj) ";
			long num = processManagementService.queryCounBySql(sql);
			if (num == 0) {
				errorMessage = "当前非院系开题报告阶段！";
				flag = false;
			} else {
				String zyh = xjb.getZyh();
				sql = "select a.gcmbid, a.njdm, a.xsh, a.zyh, b.wjm FROM lw_gcmb_syfwb a, lw_gcnrmbb b where a.gcmbid=b.gcmbid and b.gclx='01' and b.mbzt='1' and a.inuse is null " +
						"and ((a.njdm='" + njdm + "' and a.xsh='" + xsh + "' and a.zyh='" + zyh + "') Or (a.njdm='" + njdm + "' and a.xsh='" + xsh + "' and a.zyh='N') Or (a.njdm='" + njdm + "' and a.xsh='N') Or (a.njdm='N')) ";
				List<Object[]> mbbs = processManagementService.queryListBySql(sql);
				if (CollectionUtils.isNotEmpty(mbbs)) {


					List<com.alibaba.fastjson.JSONObject> pcbs = CommonUtils.queryJsonDataBySql(" SELECT a.pch,b.pcmc,a.fajhh,a.xh,b.zxjxjhh FROM lw_kxtbmd a,lw_pcb b  WHERE a.pch = b.pch AND a.xh = '" + xh + "' AND b.pczt = '1' ORDER BY b.pczt ");
					com.alibaba.fastjson.JSONObject xsxtpcb = pcbs.get(0);
					String pch = (String) xsxtpcb.get("pch");
					String pcmc = (String) xsxtpcb.get("pcmc");


					sql = "select count(1) from LW_KTBGB where xh='" + xh + "' and shzt<>'4' and xnxq = '" + pch + "'";
					count = processManagementService.queryCounBySql(sql);
					if (count > 0) {
						flage = false;
					}
				} else {
					errorMessage = "报告模板内容尚未维护！";
					flag = false;
				}
			}
			model.addAttribute("flage", flage);
		}
		model.addAttribute("schoolCode", schoolCode);
		model.addAttribute("errorMessage", errorMessage);
		model.addAttribute("pagetitle", "开题报告");
		model.addAttribute("apply_type", "10016");
		return "/student/personalManagement/processManagement/index";
	}

	/**
	 * 进入学生中期检查页面
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/student/personalManagement/processManagement/xszqjcIndex")
	public String xszqjcIndex(Model model) {


		String xh = AuthUtil.getCurrentUser().getIdNumber();
		String sql = "select count(1) from lw_kxtbmd where xh = '" + xh + "'";
		long count = processManagementService.queryCounBySql(sql);
		if (count < 1) {
			model.addAttribute("errorMessage", "您不在当前论文批次的学生名单中！");
			return "/student/personalManagement/processManagement/index";
		}

		sql = "  SELECT count(1)  FROM lw_pcb b  WHERE b.pczt = '1' ORDER BY b.pczt";
		long pcnum = processManagementService.queryCounBySql(sql);
		if (pcnum < 1) {
			model.addAttribute("errorMessage", "没有开启的论文批次！");
			return "/student/personalManagement/processManagement/index";
		}

		sql = " SELECT  count(1) FROM lw_kxtbmd a,lw_pcb b  WHERE a.pch = b.pch AND a.xh = '" + xh + "' AND b.pczt = '1'  ";
		pcnum = processManagementService.queryCounBySql(sql);
		if (pcnum < 1) {
			model.addAttribute("errorMessage", "您不在当前论文批次的学生名单中！");
			return "/student/personalManagement/processManagement/index";
		}

		sql = "select count(1) from ea_process where apply_type ='10017' and in_use = '1'";
		count = processManagementService.queryCounBySql(sql);
		String errorMessage = "";
		boolean flag = true;
		if (count == 0) {
			errorMessage = "学生中期检查无审批流程，请联系管理员！";
			flag = false;
		}
		if (flag) {
			sql = "select count(1) from EA_PROCESS_LINK where EAP_CODE in(select EAP_CODE from ea_process where apply_type ='10017' and in_use = '1') and in_use = '1'";
			count = processManagementService.queryCounBySql(sql);
			if (count == 0) {
				errorMessage = "学生中期检查未定义审批角色，请联系管理员！";
				flag = false;
			}
		}
		SysYwhdkzb ywhdkzb = processManagementService.queryEntityById(SysYwhdkzb.class, "10017");
		if (flag) {
			String ckFlag = applyCommonService.queryProcessApplyWhiteList("lw_zqjcb", xh);
			if ("0".equals(ckFlag)) {
				if (ywhdkzb != null) {
					if ("1".equals(ywhdkzb.getQyf())) {
						if ("0".equals(ywhdkzb.getKg())) {
							errorMessage = "当前开关已关闭！";
							flag = false;
						} else {
							sql = "select count(1) from sys_ywhdkzb where id = '10017' and (to_char(sysdate,'yyyymmddHH24mi') between kssj and jssj) and kg = '1'";
							count = processManagementService.queryCounBySql(sql);
							if (count == 0) {
								errorMessage = "当前操作不在允许时间范围！";
								flag = false;
							}
						}
					} else {
						errorMessage = "当前功能尚未启用！";
						flag = false;
					}
				}
			}
		}
		Object[] xtb = processManagementService.queryLwXtb(xh);
		boolean flage = true;
		if (xtb != null) {
			model.addAttribute("xnxq", xtb[0].toString());
			model.addAttribute("tmbh", xtb[1].toString());
			model.addAttribute("ywhdkzb", ywhdkzb);
		} else {
			errorMessage = "尚未确认选题！";
			flag = false;
		}
		model.addAttribute("gclx", "03");
		if (flag) {
			XsXjb xjb = baseService.queryEntityById(XsXjb.class, xh);
			String xsh = xjb.getXsh();
			sql = "select count(1) from lw_yxjdkzb where lwjdm = '05' and xsh ='" + xsh + "' and kg='1' and (to_char(sysdate,'yyyymmddHH24mi') between kssj and jssj) ";
			long num = processManagementService.queryCounBySql(sql);
			if (num == 0) {
				errorMessage = "当前非院系中期检查阶段！";
				flag = false;
			} else {
				sql = "select count(1) from lw_gcnrzdb where gclx='03' and inuse='1'";
				count = processManagementService.queryCounBySql(sql);
				if (count > 0) {

					List<com.alibaba.fastjson.JSONObject> pcbs = CommonUtils.queryJsonDataBySql(" SELECT a.pch,b.pcmc,a.fajhh,a.xh,b.zxjxjhh FROM lw_kxtbmd a,lw_pcb b  WHERE a.pch = b.pch AND a.xh = '" + xh + "' AND b.pczt = '1' ORDER BY b.pczt ");
					com.alibaba.fastjson.JSONObject xsxtpcb = pcbs.get(0);
					String pch = (String) xsxtpcb.get("pch");
					String pcmc = (String) xsxtpcb.get("pcmc");

					sql = "select count(1) from LW_ZQJCB where xh='" + xh + "' and jclb='1' and shzt<>'4' and xnxq = '" + pch + "'";
					count = processManagementService.queryCounBySql(sql);
					if (count > 0) {
						flage = false;
					}
					model.addAttribute("gclx", "03");
					model.addAttribute("flage", flage);
					model.addAttribute("xnxq", xtb[0].toString());
					model.addAttribute("tmbh", xtb[1].toString());
				} else {
					errorMessage = "学生中期检查模板内容尚未维护！";
					flag = false;
				}
			}
			model.addAttribute("flage", flage);
		}
		String schoolCode = CommonUtils.queryParamValue();
		model.addAttribute("schoolCode", schoolCode);
		model.addAttribute("errorMessage", errorMessage);
		model.addAttribute("pagetitle", "中期检查");
		model.addAttribute("apply_type", "10017");
		return "/student/personalManagement/processManagement/index";
	}

	/**
	 * 进入结题报告页面
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/student/personalManagement/processManagement/jtbgIndex")
	public String jtbgIndex(Model model) {

		String xh = AuthUtil.getCurrentUser().getIdNumber();
		String sql = "select count(1) from lw_kxtbmd where xh = '" + xh + "'";
		long count = processManagementService.queryCounBySql(sql);
		if (count < 1) {
			model.addAttribute("errorMessage", "您不在当前论文批次的学生名单中！");
			return "/student/personalManagement/processManagement/index";
		}

		sql = "  SELECT count(1)  FROM lw_pcb b  WHERE b.pczt = '1' ORDER BY b.pczt";
		long pcnum = processManagementService.queryCounBySql(sql);
		if (pcnum < 1) {
			model.addAttribute("errorMessage", "没有开启的论文批次！");
			return "/student/personalManagement/processManagement/index";
		}

		sql = " SELECT  count(1) FROM lw_kxtbmd a,lw_pcb b  WHERE a.pch = b.pch AND a.xh = '" + xh + "' AND b.pczt = '1'  ";
		pcnum = processManagementService.queryCounBySql(sql);
		if (pcnum < 1) {
			model.addAttribute("errorMessage", "您不在当前论文批次的学生名单中！");
			return "/student/personalManagement/processManagement/index";
		}

		sql = "select count(1) from ea_process where apply_type ='10018' and in_use = '1'";
		count = processManagementService.queryCounBySql(sql);
		String errorMessage = "";
		boolean flag = true;
		if (count == 0) {
			errorMessage = "结题报告无审批流程，请联系管理员！";
			flag = false;
		}
		sql = "select count(1) from EA_PROCESS_LINK where EAP_CODE in(select EAP_CODE from ea_process where apply_type ='10018' and in_use = '1') and in_use = '1'";
		count = processManagementService.queryCounBySql(sql);
		if (count == 0) {
			errorMessage = "结题报告未定义审批角色，请联系管理员！";
			flag = false;
		}

		String ckFlag = applyCommonService.queryProcessApplyWhiteList("lw_jtbgb", xh);
		if ("0".equals(ckFlag)) {
			SysYwhdkzb ywhdkzb = processManagementService.queryEntityById(SysYwhdkzb.class, "10018");
			if (ywhdkzb != null) {
				if ("1".equals(ywhdkzb.getQyf())) {
					if ("0".equals(ywhdkzb.getKg())) {
						errorMessage = "当前开关已关闭！";
						flag = false;
					} else {
						sql = "select count(1) from sys_ywhdkzb where id = '10018' and (to_char(sysdate,'yyyymmddHH24mi') between kssj and jssj) and kg = '1'";
						count = processManagementService.queryCounBySql(sql);
						if (count == 0) {
							errorMessage = "当前操作不在允许时间范围！";
							flag = false;
						}
					}
				} else {
					errorMessage = "当前功能尚未启用！";
					flag = false;
				}
			}
		}

		Object[] xtb = processManagementService.queryLwXtb(xh);
		if (xtb != null) {
			model.addAttribute("xnxq", xtb[0].toString());
			model.addAttribute("tmbh", xtb[1].toString());
		} else {
			errorMessage = "尚未确认选题！";
			flag = false;
		}
		model.addAttribute("gclx", "05");
		boolean flage = true;
		if (flag) {
			XsXjb xjb = baseService.queryEntityById(XsXjb.class, xh);
			String xsh = xjb.getXsh();
			sql = "select count(1) from lw_yxjdkzb where lwjdm = '051' and xsh ='" + xsh + "' and kg='1' and (to_char(sysdate,'yyyymmddHH24mi') between kssj and jssj) ";
			long num = processManagementService.queryCounBySql(sql);
			if (num == 0) {
				errorMessage = "当前非院系结题报告阶段！";
				flag = false;
			} else {
				sql = "select count(1) from LW_GCNRZDB where gclx='05' and inuse='1'";
				count = processManagementService.queryCounBySql(sql);
				if (count > 0) {
					List<com.alibaba.fastjson.JSONObject> pcbs = CommonUtils.queryJsonDataBySql(" SELECT a.pch,b.pcmc,a.fajhh,a.xh,b.zxjxjhh FROM lw_kxtbmd a,lw_pcb b  WHERE a.pch = b.pch AND a.xh = '" + xh + "' AND b.pczt = '1' ORDER BY b.pczt ");
					com.alibaba.fastjson.JSONObject xsxtpcb = pcbs.get(0);
					String pch = (String) xsxtpcb.get("pch");
					String pcmc = (String) xsxtpcb.get("pcmc");
					sql = "select count(1) from LW_JTBGB where xh='" + xh + "' and shzt<>'4' and xnxq = '" + pch + "' ";
					count = processManagementService.queryCounBySql(sql);
					if (count > 0) {
						flage = false;
					}
				} else {
					errorMessage = "结题报告模板内容尚未维护！";
					flag = false;
				}
			}
			model.addAttribute("flage", flage);
		}
		model.addAttribute("errorMessage", errorMessage);
		model.addAttribute("pagetitle", "结题报告");
		model.addAttribute("apply_type", "10018");
		return "/student/personalManagement/processManagement/index";
	}

	/**
	 * 分页查询
	 *
	 * @param model
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/processManagement/search")
	@ResponseBody
	public Page<Object> search(Model model, @RequestParam(defaultValue = "1") int pageNum, @RequestParam(defaultValue = "10") int pageSize, @RequestParam("gclx") String gclx,
							   @RequestParam("xnxq") String xnxq, @RequestParam("tmbh") String tmbh) {
		String xh = AuthUtil.getCurrentUser().getIdNumber();//获取当前学生号
		String sql = "";
		if ("01".equals(gclx)) {
			sql = "select a.rid,a.xnxq,pn.XNXQMC(c.zxjxjhh) xnxqm,c.pcmc,a.tmbh,b.tmmc_xs as tmmc,a.xh,a.shzt,to_char(to_date(a.tjsj,'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') tjsj,a.bz,rownum rn from LW_KTBGB a,lw_tmxxb b,lw_pcb c  where a.xnxq=b.zxjxjhh and a.tmbh=b.tmbh and b.zxjxjhh = c.pch  and a.xnxq='" + xnxq + "' and a.tmbh='" + tmbh + "' and a.xh='" + xh + "' order by a.tjsj desc";
		} else if ("03".equals(gclx)) {
			sql = "select a.rid,a.xnxq,pn.XNXQMC(c.zxjxjhh) xnxqm,c.pcmc,a.tmbh,b.tmmc_xs as tmmc,a.xh,a.shzt,to_char(to_date(a.tjsj,'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') tjsj,a.bz,rownum rn from LW_ZQJCB a,lw_tmxxb b,lw_pcb c where a.xnxq=b.zxjxjhh and a.tmbh=b.tmbh and b.zxjxjhh = c.pch  and a.jclb='1' and a.xnxq='" + xnxq + "' and a.tmbh='" + tmbh + "' and a.xh='" + xh + "' order by a.tjsj desc";
		} else if ("05".equals(gclx)) {
			sql = "select a.rid,a.xnxq,pn.XNXQMC(c.zxjxjhh) xnxqm,c.pcmc,a.tmbh,b.tmmc_xs as tmmc,a.xh,a.shzt,to_char(to_date(a.tjsj,'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss') tjsj,a.bz,rownum rn from LW_JTBGB a,lw_tmxxb b,lw_pcb c where a.xnxq=b.zxjxjhh and a.tmbh=b.tmbh and b.zxjxjhh = c.pch  and a.xnxq='" + xnxq + "' and a.tmbh='" + tmbh + "' and a.xh='" + xh + "' order by a.tjsj desc";
		}
		QueryInfo info = new QueryInfo();
		info.setPageNum(pageNum);
		info.setMaxResult(pageSize);
		info.setSql(sql);
		Page<Object> page = pageService.queryPageBySql(info);
		return page;
	}

	/**
	 * 校验是否可以申请
	 *
	 * @return
	 */
	@RequestMapping("/student/personalManagement/processManagement/checkaddApply")
	@ResponseBody
	public Map<String, Object> checkApplyNote(String type) {
		String sqlx = "";
		String tabName = "";
		if ("01".equals(type)) {
			sqlx = "10016";
			tabName = "lw_ktbgb";
		} else if ("03".equals(type)) {
			sqlx = "10017";
			tabName = "lw_zqjcb";
		} else if ("05".equals(type)) {
			sqlx = "10018";
			tabName = "lw_jtbgb";
		}
		Map<String, Object> map = new HashMap<String, Object>();
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		XsXjb xsXjb = processManagementService.queryEntityById(XsXjb.class, xh);
		String ckFlag = applyCommonService.queryProcessApplyWhiteList(tabName, xh);
		SysYwhdkzb sysYwhdkzb = baseService.queryEntityById(SysYwhdkzb.class, sqlx);
		if ("0".equals(ckFlag)) {
			String sql = "select count(1) from LW_YXJDKZB where lwjdm='" + ("01".equals(type) ? "04" : ("03".equals(type) ? "05" : "051")) + "' and xsh ='" + xsXjb.getXsh() + "' and kg='1' and (to_char(sysdate,'yyyymmddHH24mi') between kssj and jssj) ";
			long num = processManagementService.queryCounBySql(sql);
			if (num == 0) {
				String errorMessage = "当前非院系报告阶段！";
				map.put("msg", errorMessage);
			} else {
				if (sysYwhdkzb != null) {
					if (sysYwhdkzb.getQyf().equals("0")) {
						map.put("msg", "申请开关已关闭，不允许申请！");
					} else {
						sql = "SELECT count(1) from sys_ywhdkzb where id = '" + sqlx + "' and (to_char(sysdate,'yyyymmddHH24mi') between kssj and jssj) and kg = '1'";
						long timeCount = processManagementService.queryCounBySql(sql);
						if (timeCount == 0) {
							map.put("msg", "当前时间不允许进行申请！");
						} else {
							map.put("sfxyd", sysYwhdkzb.getSfxyd());
							map.put("ydnr", sysYwhdkzb.getYdnrstr());
							map.put("qzydms", sysYwhdkzb.getQzydms());
						}
					}
				} else {
					map.put("msg", "未维护申请控制开关，请联系管理员处理！");
				}
			}
		} else {
			if (sysYwhdkzb != null) {
				map.put("sfxyd", sysYwhdkzb.getSfxyd());
				map.put("ydnr", sysYwhdkzb.getYdnrstr());
				map.put("qzydms", sysYwhdkzb.getQzydms());
			}
		}

		return map;
	}

	/**
	 * 进入维护页面
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/student/personalManagement/processManagement/update")
	public String update(Model model, @RequestParam("gclx") String gclx, @RequestParam("type") String type, String rid) {


		String xh = AuthUtil.getCurrentUser().getIdNumber();
		List<com.alibaba.fastjson.JSONObject> pcbs = CommonUtils.queryJsonDataBySql(" SELECT a.pch,b.pcmc,a.fajhh,a.xh,b.zxjxjhh FROM lw_kxtbmd a,lw_pcb b  WHERE a.pch = b.pch AND a.xh = '" + xh + "' AND b.pczt = '1' ORDER BY b.pczt ");
		com.alibaba.fastjson.JSONObject xsxtpcb = pcbs.get(0);
		String pch = (String) xsxtpcb.get("pch");
		String pcmc = (String) xsxtpcb.get("pcmc");

		XsXjb xjb = baseService.queryEntityById(XsXjb.class, xh);
		String njdm = xjb.getNjdm();
		String xsh = xjb.getXsh();
		String zyh = xjb.getZyh();
		String sql = "SELECT a.gcmbid, a.njdm, a.xsh, a.zyh, b.wjm FROM lw_gcmb_syfwb a, lw_gcnrmbb b where a.gcmbid=b.gcmbid and b.gclx='" + gclx + "' and b.mbzt='1' and a.inuse is null " +
				"and ((a.njdm='" + njdm + "' and a.xsh='" + xsh + "' and a.zyh='" + zyh + "') Or (a.njdm='" + njdm + "' and a.xsh='" + xsh + "' and a.zyh='N') Or " +
				"(a.njdm='" + njdm + "' and a.xsh='N') Or (a.njdm='N')) ORDER BY decode(njdm, 'N', '', njdm), decode(xsh, 'N', '', xsh), decode(zyh, 'N', '', zyh)";
		List<Object[]> lwmbs = processManagementService.queryListBySql(sql);
		if (CollectionUtils.isNotEmpty(lwmbs) && lwmbs.size() > 0) {
			boolean flage = false;
			if ("add".equals(type)) {
				if ("01".equals(gclx)) {
					sql = "select count(1) from LW_KTBGB where xh='" + xh + "' and shzt<>'4' and xnxq = '" + pch + "'";
				} else if ("03".equals(gclx)) {
					sql = "select count(1) from LW_ZQJCB where xh='" + xh + "' and jclb='1' and shzt<>'4' and xnxq = '" + pch + "'";
				} else if ("05".equals(gclx)) {
					sql = "select count(1) from LW_JTBGB where xh='" + xh + "' and shzt<>'4' and xnxq = '" + pch + "'";
				}
				long i = processManagementService.queryCounBySql(sql);
				if (i > 0) {
					flage = false;
				} else {
					flage = true;
				}
			} else if ("edit".equals(type)) {
				if ("01".equals(gclx)) {
					sql = "select count(1) from LW_KTBGB where xh='" + xh + "' and shzt='0' and xnxq = '" + pch + "'";
				} else if ("03".equals(gclx)) {
					sql = "select count(1) from LW_ZQJCB where xh='" + xh + "' and jclb='1' and shzt='0' and xnxq = '" + pch + "'";
				} else if ("05".equals(gclx)) {
					sql = "select count(1) from LW_JTBGB where xh='" + xh + "' and shzt='0' and xnxq = '" + pch + "'";
				}
				long i = processManagementService.queryCounBySql(sql);
				if (i > 0) {
					flage = true;
				}
			} else if ("look".equals(type)) {
				flage = true;
			}
			if (flage) {
				Object[] gcmb = lwmbs.get(0);
				List<LwGcnrzdb> gcnrzdbList = refactoringLwGcnrzdbList(gclx, rid, gcmb[0] + "", "0");
				LwXtb xtb = processManagementService.loadLwXtb(xh);
				String sqlx = "";
				if ("01".equals(gclx)) {
					sqlx = "10016";
				} else if ("03".equals(gclx)) {
					sqlx = "10017";
				} else if ("05".equals(gclx)) {
					sqlx = "10018";
				}
				String schoolCode = CommonUtils.queryParamValue();
				SysYwhdkzb ywhdkzb = processManagementService.queryEntityById(SysYwhdkzb.class, sqlx);
				if (!"add".equals(type) && "01".equals(gclx)) {
					LwKtbgb ktbgb = processManagementService.queryEntityById(LwKtbgb.class, rid);
					model.addAttribute("ktbgb", ktbgb);
					if ("look".equals(type) && "100014".equals(schoolCode)) {
						List<SysColConfig> sysColConfigs = applyCommonService.querySysColConfigByTabName("lw_ktbgb");
						model.addAttribute("sysColConfigs", sysColConfigs);
						JSONObject ktbgJson = JSONObject.fromObject(ktbgb);
						for (SysColConfig sysColConfig : sysColConfigs) {
							sysColConfig.setColvalue(ktbgJson.get(sysColConfig.getColid().toLowerCase()) + "");
						}
						model.addAttribute("sysColConfigs", sysColConfigs);
					}
					List<LwKtckwxb> ktckwxList = processManagementService.queryKtckwxList(rid);
					model.addAttribute("ktckwxList", ktckwxList);
				}
				if (!"add".equals(type) && "03".equals(gclx)) {
					LwZqjcb zqjcb = processManagementService.queryEntityById(LwZqjcb.class, rid);
					model.addAttribute("zqjcb", zqjcb);
					if ("look".equals(type) && "100014".equals(schoolCode)) {
						List<SysColConfig> sysColConfigs = applyCommonService.querySysColConfigByTabName("lw_zqjcb");
						model.addAttribute("sysColConfigs", sysColConfigs);
						JSONObject zqjcJson = JSONObject.fromObject(zqjcb);
						for (SysColConfig sysColConfig : sysColConfigs) {
							sysColConfig.setColvalue(zqjcJson.get(sysColConfig.getColid().toLowerCase()) + "");
						}
						model.addAttribute("sysColConfigs", sysColConfigs);
					}
				}
				if (!"add".equals(type) && "05".equals(gclx)) {
					LwJtbgb jtbgb = processManagementService.queryEntityById(LwJtbgb.class, rid);
					model.addAttribute("jtbgb", jtbgb);
					if ("look".equals(type) && "100014".equals(schoolCode)) {
						List<SysColConfig> sysColConfigs = applyCommonService.querySysColConfigByTabName("lw_jtbgb");
						model.addAttribute("sysColConfigs", sysColConfigs);
						JSONObject jtbgJson = JSONObject.fromObject(jtbgb);
						for (SysColConfig sysColConfig : sysColConfigs) {
							sysColConfig.setColvalue(jtbgJson.get(sysColConfig.getColid().toLowerCase()) + "");
						}
						model.addAttribute("sysColConfigs", sysColConfigs);
					}
				}
				model.addAttribute("schoolCode", schoolCode);
				model.addAttribute("rid", rid);
				model.addAttribute("ywhdkzb", ywhdkzb);
				model.addAttribute("gcmbid", gcmb[0] + "");
				model.addAttribute("type", type);
				model.addAttribute("gclx", gclx);
				model.addAttribute("xtb", xtb);
				model.addAttribute("xjb", xjb);
				model.addAttribute("gcnrzdbList", gcnrzdbList);
				model.addAttribute("gcmbid", lwmbs.get(0)[0]);
				model.addAttribute("wjm", lwmbs.get(0)[4]);
				if ("look".equals(type)) {
					EaApplysQu eaApplys = applyCommonService.queryEntityById(EaApplysQu.class, rid);
					model.addAttribute("eaApplys", eaApplys);
					List<Object[]> eaResults = applyCommonService.queryEaResultsByApplyId(rid);
					model.addAttribute("eaResults", eaResults);
				}
				return "/student/personalManagement/processManagement/update";

			} else {
				String errorMessage = "报告内容已维护！";
				model.addAttribute("errorInfo", errorMessage);
				return "system/error/printErrorMessageByModal";
			}
		} else {
			String errorMessage = "报告模板内容尚未维护！";
			model.addAttribute("errorInfo", errorMessage);
			return "system/error/printErrorMessageByModal";
		}
	}

	@RequestMapping("/student/personalManagement/processManagement/update/queryApprovalDatas")
	@ResponseBody
	public UrpResult queryApprovalDatas(Model model, @RequestParam("gclx") String gclx, String rid, String gcmbid) {

		Map<String, Object> map = new HashMap<>();
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		EaApplysQu eaApplys = applyCommonService.queryEntityById(EaApplysQu.class, rid);
		model.addAttribute("eaApplys", eaApplys);
		LwGcnrmbb lwGcnrmbb = processManagementService.queryEntityById(LwGcnrmbb.class, gcmbid);
		List<com.alibaba.fastjson.JSONObject> fxpfList = null;
		List<com.alibaba.fastjson.JSONObject> pfList = null;
		String cjfxpy = "", fxcj = "";

		if ("1".equals(lwGcnrmbb.getGcpjcj()) && StringUtils.isNotBlank(lwGcnrmbb.getCjfxdm())) {

			LwXtb xtb = processManagementService.loadLwXtb(xh);

			String sql = "select c.cjzcid,c.cjfxdm,c.mfcj,c.pfbzid from lw_fxcj_zcb z,lw_fxcj_zcqdb c,xs_xjb x where z.cjzcid = c.cjzcid " +
					"and z.pch = '" + xtb.getId().getZxjxjhh() + "' and z.xsh = x.xsh and z.zyh = x.zyh and c.cjfxdm = '" + lwGcnrmbb.getCjfxdm() + "' and x.xh = '" + xh + "'";
			List<com.alibaba.fastjson.JSONObject> jsonObjects = CommonUtils.queryJsonDataBySql(sql);
			if (jsonObjects.size() > 0) {

				com.alibaba.fastjson.JSONObject zcqd = jsonObjects.get(0);
				/*sql = "select p.pfbzid," +
                        "       p.pfxmdm," +
                        "       p.pfxmmc," +
                        "       p.pfxmsm," +
                        "       p.pfxmxh," +
                        "       p.fjpfxm," +
                        "       p.pfxmmf," +
                        "       (select count(1) from lw_fxcj_pfbzb pf where p.pfbzid = pf.pfbzid and p.pfxmdm = pf.fjpfxm) as childnums," +
                        "       (select c.pfxmdf from lw_fxcj_pfxmdfb c where p.pfbzid = c.pfbzid and c.cjfxdm = '" + zcqd.get("cjfxdm") + "'" +
                        "           and c.zxjxjhh = '" + xtb.getId().getZxjxjhh() + "' and c.tmbh = '" + xtb.getId().getTmbh() + "'" +
                        "           and c.xh = '" + xh + "' and p.pfxmdm = c.pfxmdm) as pfxmdf" +
                        "  from lw_fxcj_pfbzb p" +
                        " where p.pfbzid = '" + zcqd.get("pfbzid") + "' and p.fjpfxm is null order by p.pfxmxh";
                fxpfList = CommonUtils.queryJsonDataBySql(sql);
                sql = "select p.pfbzid," +
                        "       p.pfxmdm," +
                        "       p.pfxmmc," +
                        "       p.pfxmsm," +
                        "       p.pfxmxh," +
                        "       p.fjpfxm," +
                        "       p.pfxmmf," +
                        "       (select count(1) from lw_fxcj_pfbzb pf where p.pfbzid = pf.pfbzid and p.pfxmdm = pf.fjpfxm) as childnums," +
                        "       (select c.pfxmdf from lw_fxcj_pfxmdfb c where p.pfbzid = c.pfbzid and c.cjfxdm = '" + zcqd.get("cjfxdm") + "'" +
                        "           and c.zxjxjhh = '" + xtb.getId().getZxjxjhh() + "' and c.tmbh = '" + xtb.getId().getTmbh() + "'" +
                        "           and c.xh = '" + xh + "' and p.pfxmdm = c.pfxmdm) as pfxmdf" +
                        "  from lw_fxcj_pfbzb p" +
                        " where p.pfbzid = '" + zcqd.get("pfbzid") + "' and p.fjpfxm is not null order by p.pfxmxh";
                pfList = CommonUtils.queryJsonDataBySql(sql);*/
				//LwPcb lwPcb = processManagementService.queryEntityById(LwPcb.class, xtb.getId().getZxjxjhh());
				sql = "select pkg_cj.f_grade2c(f.fxcj) as fxcj,f.cjfxpy from lw_lrcjfxb f where f.cjfxdm = '" + zcqd.get("cjfxdm") + "'" +
						" and f.zxjxjhh = '" + xtb.getId().getZxjxjhh() + "' and f.tmbh = '" + xtb.getId().getTmbh() + "' and f.xh = '" + xh + "'";
				List<com.alibaba.fastjson.JSONObject> fxcjList = CommonUtils.queryJsonDataBySql(sql);
				com.alibaba.fastjson.JSONObject fxcjs = fxcjList.size() == 0 ? null : fxcjList.get(0);
				if (fxcjs != null) {
					cjfxpy = fxcjs.get("cjfxpy") + "";
					fxcj = fxcjs.get("fxcj") + "";
				}
			}
		}

		map.put("gcpjcj", lwGcnrmbb.getGcpjcj());
		map.put("pfList", pfList);
		map.put("fxpfList", fxpfList);
		map.put("cjfxpy", cjfxpy);
		map.put("fxcj", fxcj);
		List<LwGcnrzdb> gcnrzdbList = null;
		if (eaApplys != null) {
			if ("3".equals(eaApplys.getApplyStatus()) && "1".equals(eaApplys.getEaRslt())) {
				gcnrzdbList = refactoringLwGcnrzdbList(gclx, rid, gcmbid + "", "1");
				map.put("gcnrzdbList", gcnrzdbList);
			}
		}
		return UrpResult.ok(map);
	}

	@RequestMapping("/student/personalManagement/processManagement/refactoringLwGcnrzdbList")
	@ResponseBody
	public UrpResult refactoringLwGcnrzdbList(Model model, @RequestParam("gclx") String gclx, @RequestParam("rid") String rid, @RequestParam("gcmbid") String gcmbid, @RequestParam("tbrlx") String tbrlx) {
		Map<String, Object> map = new HashMap<>();
		List<LwGcnrzdb> gcnrzdbList = refactoringLwGcnrzdbList(gclx, rid, gcmbid + "", tbrlx);
		map.put("gcnrzdbList", gcnrzdbList);
		return UrpResult.ok(map);
	}

	private List<LwGcnrzdb> refactoringLwGcnrzdbList(String gclx, String rid, String gcmbid, String tbrlx) {

		List<LwGcnrzdb> gcnrzdbList = processManagementService.loadLwGcnrzdbByLwmb(gcmbid, tbrlx);
		for (LwGcnrzdb zdb : gcnrzdbList) {
			if (StringUtils.isNotBlank(rid)) {
				if ("01".equals(gclx)) {
					LwKtbgnrb ktbgnrb = processManagementService.loadLwKtbgnrb(rid, zdb.getCid());
					zdb.setKtbgnrb(ktbgnrb);
				} else if ("03".equals(gclx)) {
					LwZqjcnrb zqjcnrb = processManagementService.loadLwZqjcnrb(rid, zdb.getCid());
					zdb.setZqjcnrb(zqjcnrb);

				} else if ("05".equals(gclx)) {
					LwJtbgnrb jtbgnrb = processManagementService.loadLwJtbgnrb(rid, zdb.getCid());
					zdb.setJtbgnrb(jtbgnrb);
				}
			}
			if (!"04".equals(zdb.getSjlx()) && !"07".equals(zdb.getSjlx())) {
				if ("01".equals(zdb.getSrlx())) {
					LwGcnrSzlqzfwb qzfwb = baseService.queryEntityById(LwGcnrSzlqzfwb.class, zdb.getCid());
					zdb.setQzfwb(qzfwb);
				} else {
					List<LwGcnrLmjzb> mjzbList = processManagementService.loadLwGcnrLmjzb(zdb.getCid());
					zdb.setMjzbList(mjzbList);
				}
			} else {
				if ("07".equals(zdb.getSjlx())) {
					LwGcnrFjlxzb fjlxzb = processManagementService.queryEntityById(LwGcnrFjlxzb.class, zdb.getCid());
					zdb.setFjlxzb(fjlxzb);
				}
			}
		}
		return gcnrzdbList;
	}


	/**
	 * 保存
	 *
	 * @param request
	 * @param session
	 * @param shzt
	 * @param gcmbid
	 * @param gclx
	 * @param type
	 * @param rid
	 * @param fjids
	 * @param sqfj
	 * @param spjsh
	 * @param ckwxDatas
	 * @return
	 */
	@RequestMapping("/student/personalManagement/processManagement/doSave")
	@ResponseBody
	public UrpResult doSave(HttpServletRequest request, HttpSession session,
							@RequestParam("shzt") String shzt,
							@RequestParam("gcmbid") String gcmbid,
							@RequestParam("gclx") String gclx,
							@RequestParam("type") String type,
							String rid, String fjids,
							@RequestParam(required = false) MultipartFile[] sqfj,
							String spjsh,
							CkwxList ckwxList) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (!csrfToken.isTokenValid(request)) {
			map.put("result", csrfToken.gotoAjaxIndex());
			return UrpResult.ok(map);
		} else {
			if (!"look".equals(type)) {
				map = processManagementService.doSave(request, shzt, gcmbid, gclx, type, rid, fjids, sqfj, spjsh, ckwxList);
			}
		}
		map.put("token", session.getAttribute("token_in_session").toString());
		return UrpResult.ok(map);
	}

	/**
	 * 弹出指定审批人页面
	 *
	 * @param model
	 * @param sqbh
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/processManagement/selectApprover", method = RequestMethod.GET)
	public String selectApprover(Model model, String gclx) {
		String sqlx = "";
		if ("01".equals(gclx)) {
			sqlx = "10016";
		} else if ("03".equals(gclx)) {
			sqlx = "10017";
		} else if ("05".equals(gclx)) {
			sqlx = "10018";
		}
		model.addAttribute("apply_type", sqlx);
		return "/student/personalManagement/processManagement/selectApprover";
	}

	/**
	 * 查询审批人
	 *
	 * @param model
	 * @param request
	 * @param session
	 * @param jsSqb
	 * @param apply_status
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/processManagement/queryApprover")
	@ResponseBody
	public UrpResult queryApprover(Model model, HttpServletRequest request, HttpSession session, String apply_type) {
		Map<String, Object> map = new HashMap<String, Object>();
		List<Object[]> list = applyCommonService.queryEalByApplyType(apply_type);
		Object[] names = list.get(0);
		String czr = AuthUtil.getCurrentUser().getIdNumber();
		String sqbh = applyCommonService.querySqbhByApply();
		LwXtb xtb = processManagementService.loadLwXtb(czr);
		LwPcb lwPcb = processManagementService.queryEntityById(LwPcb.class, xtb.getId().getZxjxjhh());

		String sql = "";
		if ("10016".equals(apply_type)) {
			sql = "insert into LW_KTBGB (rid, xnxq, tmbh, xh, GCMBID) values ('" + sqbh + "', '" + lwPcb.getPch() + "', '" + xtb.getId().getTmbh() + "', '" + czr + "', 'xxxx')";
		} else if ("10017".equals(apply_type)) {
			sql = "insert into lw_zqjcb (rid, xnxq, tmbh, xh, GCMBID) values ('" + sqbh + "', '" + lwPcb.getPch() + "', '" + xtb.getId().getTmbh() + "', '" + czr + "', 'xxxx')";
		} else if ("10018".equals(apply_type)) {
			sql = "insert into lw_jtbgb (rid, xnxq, tmbh, xh, GCMBID) values ('" + sqbh + "', '" + lwPcb.getPch() + "', '" + xtb.getId().getTmbh() + "', '" + czr + "', 'xxxx')";
		}
		map = applyCommonService.queryApprovers(sql, apply_type, names, sqbh, lwPcb.getZxjxjhh(), "");
		map.put("eal_name", names[0]);
		map.put("eap_code", names[2]);
		map.put("eal_code", names[3]);
		return UrpResult.ok(map);
	}

	/**
	 * 删除
	 *
	 * @param request
	 * @param session
	 * @param cid
	 * @return
	 */
	@RequestMapping("/student/personalManagement/processManagement/doDel")
	@ResponseBody
	public UrpResult doDel(HttpServletRequest request, HttpSession session, @RequestParam("gclx") String gclx, @RequestParam("rid") String rid) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (!csrfToken.isTokenValid(request)) {
			map.put("result", csrfToken.gotoAjaxIndex());
			return UrpResult.ok(map);
		} else {
			map = processManagementService.doDel(gclx, rid);
		}
		map.put("token", session.getAttribute("token_in_session").toString());
		return UrpResult.ok(map);
	}

	/**
	 * 附件下载
	 *
	 * @param id
	 */
	@RequestMapping(value = "/student/personalManagement/processManagement/doDownFj/{gclx}/{nrid}")
	public void doDownFj(
			HttpServletResponse response, @PathVariable("gclx") String gclx,
			@PathVariable("nrid") String nrid) {
		if ("01".equals(gclx)) {
			LwKtbgnrb ktbgnrb = baseService.queryEntityById(LwKtbgnrb.class, nrid);
			if (ktbgnrb != null) {
				commonExportService.exportCommon(ktbgnrb.getLwbz(), ktbgnrb.getBlobz(), response);
			}
		} else if ("03".equals(gclx)) {
			LwZqjcnrb zqjcnrb = baseService.queryEntityById(LwZqjcnrb.class, nrid);
			if (zqjcnrb != null) {
				commonExportService.exportCommon(zqjcnrb.getLwbz(), zqjcnrb.getBlobz(), response);
			}
		} else if ("05".equals(gclx)) {
			LwJtbgnrb jtbgnrb = baseService.queryEntityById(LwJtbgnrb.class, nrid);
			if (jtbgnrb != null) {
				commonExportService.exportCommon(jtbgnrb.getLwbz(), jtbgnrb.getBlobz(), response);
			}
		}
	}

	@RequestMapping("/student/personalManagement/processManagement/downLoad/{rid}/{gclx}")
	public void downLoad(@PathVariable("rid") String rid, @PathVariable("gclx") String gclx, HttpServletResponse response) {
		if (rid.startsWith("gc")) {
			List<Object[]> objs = processManagementService.queryListBySql("select wjm, wdmb from lw_gcnrmbb where gcmbid='" + rid.substring(2) + "'");

			if (CollectionUtils.isNotEmpty(objs)) {
				Object[] obj = objs.get(0);
				commonExportService.exportCommon(obj[0] + "", BlobToBytes((Blob) obj[1]), response);
			}
		} else {
			if ("01".equals(gclx)) {
				LwKtbgb ktbgb = processManagementService.queryEntityById(LwKtbgb.class, rid);
				MinioUtils.download(response, ktbgb.getFjlj(), ktbgb.getWjmc());
			}
			if ("03".equals(gclx)) {
				LwZqjcb zqjcb = processManagementService.queryEntityById(LwZqjcb.class, rid);
				MinioUtils.download(response, zqjcb.getFjlj(), zqjcb.getFjmc());
			}
			if ("05".equals(gclx)) {
				LwJtbgb jtbgb = processManagementService.queryEntityById(LwJtbgb.class, rid);
				MinioUtils.download(response, jtbgb.getFjlj(), jtbgb.getWjmc());
			}
		}
	}

	private byte[] BlobToBytes(Blob blob) {
		BufferedInputStream bis = null;

		try {
			bis = new BufferedInputStream(blob.getBinaryStream());
			byte[] bytes = new byte[(int) blob.length()];
			int len = bytes.length;
			int offset = 0;
			int read = 0;
			while (offset < len && (read = bis.read(bytes, offset, len - offset)) > 0) {
				offset += read;
			}
			return bytes;
		} catch (SQLException | IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		} finally {
			if (bis != null) {
				try {
					bis.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * 校验是否存在模板
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/processManagement/print/checkTemplate", method = RequestMethod.POST)
	@ResponseBody
	public UrpResult checkTemplate(Model model) {
		String result = "";
		PrintTemplateTablePK id = new PrintTemplateTablePK();
		id.setMkid("lwgl");
		id.setMbid("ktbg");
		PrintTemplateTable template = processManagementService.queryEntityById(PrintTemplateTable.class, id);

		if (template != null && template.getMbnr() != null && template.getMbnr().length > 0) {
			result = "ok";
		} else {
			result = "null";
		}
		return UrpResult.ok(result);
	}

	@RequestMapping(value = "/student/personalManagement/processManagement/print")
	public void printSq(HttpServletResponse response, Model model, String sqbh) {
		processManagementService.exportReportCard(response, sqbh);

	}

	/**
	 * 进入论文写作计划页面
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/student/personalManagement/processManagement/writingPlan/index")
	public String xzjhIndex(Model model) {

		String xh = AuthUtil.getCurrentUser().getIdNumber();

		String sql = "SELECT count(1)  FROM lw_pcb b WHERE b.pczt = '1' ORDER BY b.pczt";
		long pcnum = processManagementService.queryCounBySql(sql);
		if (pcnum < 1) {
			model.addAttribute("errorMessage", "没有开启的论文批次！");
			return "student/personalManagement/processManagement/writingPlan/index";
		}

		String schoolCode = CommonUtils.queryParamValue();
		if ("100030".equals(schoolCode)) {
			List<Object[]> lwpcxxbList = processManagementService.loadLwpcxx(xh);
			if (lwpcxxbList != null && lwpcxxbList.size() > 0) {
				for (Object[] obj : lwpcxxbList) {
					if (!(obj[8] != null && "1".equals(obj[8].toString()))) {
						model.addAttribute("errorMessage", "没有确认【承诺与使用授权书】,点击<a style='cursor: pointer;text-decoration: underline;' onclick='returnOther();'>这里</a>前往确认！");
						return "student/personalManagement/processManagement/writingPlan/index";
					}
				}

			} else {
				model.addAttribute("errorMessage", "您不在当前论文批次的学生名单中！");
				return "student/personalManagement/processManagement/writingPlan/index";
			}
		}

		return "student/personalManagement/processManagement/writingPlan/index";
	}

	@RequestMapping(value = "/student/personalManagement/processManagement/writingPlan/queyXzjhInfo", method = RequestMethod.GET)
	@ResponseBody
	public UrpResult queyXzjhInfo(Model model, @RequestParam(defaultValue = "1") int pageNum, @RequestParam(defaultValue = "100") int pageSize, String fajhh) {
		Map<String, Object> map = new HashMap<String, Object>();
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		QueryInfo info = new QueryInfo();
		info.setPageNum(pageNum);
		info.setMaxResult(pageSize);
		info.setSql("select r.*,rownum rn from (select a.pch,a.pcmc,a.pczt,a.zxjxjhh,pn.xnxqmc(a.zxjxjhh) zxjxjhm,b.fajhh,c.famc,d.tmbh," +
				"to_char(to_date(decode(d.daggdtjsj, null, (select t.kssj from LW_YXJDKZB t where t.lwjdm='lwtg' and t.xsh=pn.xs_xsh(d.xh)),substr(d.daggdtjsj, 0, 14)), 'yyyymmddHH24miss'),'yyyy-mm-dd hh24:mi:ss') kssj_dag," +
				"to_char(to_date(decode(d.daggdtjsj, null, (select t.jssj from LW_YXJDKZB t where t.lwjdm='lwtg' and t.xsh=pn.xs_xsh(d.xh)),substr(d.daggdtjsj, 16,28)), 'yyyymmddHH24miss'),'yyyy-mm-dd hh24:mi:ss') jzsj_dag," +
				"to_char(to_date(decode(d.cggdtjsj, null, (select t.kssj from LW_YXJDKZB t where t.lwjdm='lwcg' and t.xsh=pn.xs_xsh(d.xh)),substr(d.cggdtjsj, 0, 14)), 'yyyymmddHH24miss'),'yyyy-mm-dd hh24:mi:ss') kssj_cg," +
				"to_char(to_date(decode(d.cggdtjsj, null, (select t.jssj from LW_YXJDKZB t where t.lwjdm='lwcg' and t.xsh=pn.xs_xsh(d.xh)),substr(d.cggdtjsj, 16,28)), 'yyyymmddHH24miss'),'yyyy-mm-dd hh24:mi:ss') jzsj_cg," +
				"to_char(to_date(decode(d.eggdtjsj, null, (select t.kssj from LW_YXJDKZB t where t.lwjdm='lweg' and t.xsh=pn.xs_xsh(d.xh)),substr(d.eggdtjsj, 0, 14)), 'yyyymmddHH24miss'),'yyyy-mm-dd hh24:mi:ss') kssj_eg," +
				"to_char(to_date(decode(d.eggdtjsj, null, (select t.jssj from LW_YXJDKZB t where t.lwjdm='lweg' and t.xsh=pn.xs_xsh(d.xh)),substr(d.eggdtjsj, 16,28)), 'yyyymmddHH24miss'),'yyyy-mm-dd hh24:mi:ss') jzsj_eg," +
				"to_char(to_date(decode(d.dggdtjsj, null, (select t.kssj from LW_YXJDKZB t where t.lwjdm='lwdg' and t.xsh=pn.xs_xsh(d.xh)),substr(d.dggdtjsj, 0, 14)), 'yyyymmddHH24miss'),'yyyy-mm-dd hh24:mi:ss') kssj_dg," +
				"to_char(to_date(decode(d.dggdtjsj, null, (select t.jssj from LW_YXJDKZB t where t.lwjdm='lwdg' and t.xsh=pn.xs_xsh(d.xh)),substr(d.dggdtjsj, 16,28)), 'yyyymmddHH24miss'),'yyyy-mm-dd hh24:mi:ss') jzsj_dg " +
				"from lw_pcb a, lw_kxtbmd b, jh_fajhb c, lw_xtb d where a.pch=b.pch and b.fajhh=c.fajhh and b.pch=d.zxjxjhh and b.xh=d.xh " +
				"and d.xtztdm='02' " + (StringUtils.isNotBlank(fajhh) ? " and b.fajhh='" + fajhh + "' " : "") + " and b.xh='" + xh + "') r ");
		Page<Object> page = processManagementService.queryPage(map, pageService.queryPageBySql(info), xh);
		map.put("page", page);
		return UrpResult.ok(map);
	}

	/**
	 * 保存
	 *
	 * @param request
	 * @param session
	 * @param tjzt
	 * @param lwxzjhb
	 * @return
	 */
	@RequestMapping("/student/personalManagement/processManagement/writingPlan/saveXzjhInfo")
	@ResponseBody
	public UrpResult saveXzjhInfo(HttpServletRequest request, HttpSession session,
								  @RequestParam("tjzt") String tjzt, LwLwxzjhb lwxzjhb) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (!csrfToken.isTokenValid(request)) {
			map.put("result", csrfToken.gotoAjaxIndex());
			return UrpResult.ok(map);
		}
		map.put("result", processManagementService.saveXzjhInfo(request, tjzt, lwxzjhb));
		map.put("token", session.getAttribute("token_in_session").toString());
		return UrpResult.ok(map);
	}

	@RequestMapping("/student/personalManagement/processManagement/writingPlan/history")
	public String history(Model model, @RequestParam("zxjxjhh") String zxjxjhh, @RequestParam("tmbh") String tmbh) {
		model.addAttribute("zxjxjhh", zxjxjhh);
		model.addAttribute("tmbh", tmbh);
		return "student/personalManagement/processManagement/writingPlan/history";
	}

	@RequestMapping(value = "/student/personalManagement/processManagement/writingPlan/getApplyHistory", method = RequestMethod.GET)
	@ResponseBody
	public UrpResult getApplyHistory(Model model, @RequestParam(defaultValue = "1") int pageNum, @RequestParam(defaultValue = "100") int pageSize,
									 @RequestParam("zxjxjhh") String zxjxjhh, @RequestParam("tmbh") String tmbh) {
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		String sql = "select r.*,rownum rn from (select a.jhid,a.zxjxjhh,a.tmbh,a.tmmc,a.tjzt," +
				"decode(b.daggdtjsj,null,'',to_char(to_date(substr(b.daggdtjsj,0,14), 'yyyymmddHH24miss'), 'yyyy-mm-dd hh24:mi:ss')) kssj_dag," +
				"decode(b.daggdtjsj,null,'',to_char(to_date(substr(b.daggdtjsj,16,28), 'yyyymmddHH24miss'), 'yyyy-mm-dd hh24:mi:ss')) jzsj_dag," +
				"decode(b.cggdtjsj,null,'',to_char(to_date(substr(b.cggdtjsj,0,14), 'yyyymmddHH24miss'), 'yyyy-mm-dd hh24:mi:ss')) kssj_cg," +
				"decode(b.cggdtjsj,null,'',to_char(to_date(substr(b.cggdtjsj,16,28), 'yyyymmddHH24miss'), 'yyyy-mm-dd hh24:mi:ss')) jzsj_cg," +
				"decode(b.eggdtjsj,null,'',to_char(to_date(substr(b.eggdtjsj,0,14), 'yyyymmddHH24miss'), 'yyyy-mm-dd hh24:mi:ss')) kssj_eg," +
				"decode(b.eggdtjsj,null,'',to_char(to_date(substr(b.eggdtjsj,16,28), 'yyyymmddHH24miss'), 'yyyy-mm-dd hh24:mi:ss')) jzsj_eg," +
				"decode(b.dggdtjsj,null,'',to_char(to_date(substr(b.dggdtjsj,0,14), 'yyyymmddHH24miss'), 'yyyy-mm-dd hh24:mi:ss')) kssj_dg," +
				"decode(b.dggdtjsj,null,'',to_char(to_date(substr(b.dggdtjsj,16,28), 'yyyymmddHH24miss'), 'yyyy-mm-dd hh24:mi:ss')) jzsj_dg," +
				"to_char(to_date(a.czsj, 'yyyymmddHH24miss'), 'yyyy-mm-dd hh24:mi:ss') czsj,a.zdjsjlm,to_char(to_date(a.zdjsczsj, 'yyyymmddHH24miss'), 'yyyy-mm-dd hh24:mi:ss') zdjsczsj," +
				"a.zdjsyj from LW_LWXZJHB a, lw_xtb b where a.zxjxjhh = b.zxjxjhh and a.tmbh = b.tmbh and a.xh = b.xh and a.zxjxjhh='" + zxjxjhh + "' and a.tmbh='" + tmbh + "' and a.xh='" + xh + "' order by a.czsj desc) r ";
		QueryInfo info = new QueryInfo();
		info.setPageNum(pageNum);
		info.setMaxResult(pageSize);
		info.setSql(sql);
		Page<JSONObject> page = pageService.queryPageBySql(info);
		return UrpResult.ok(page);
	}

}