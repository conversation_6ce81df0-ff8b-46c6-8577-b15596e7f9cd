<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>奖学金申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 奖学金申请页面样式 */
        .scholarship-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .scholarship-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .scholarship-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .action-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-add-application {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .btn-return {
            background: var(--text-secondary);
            color: white;
        }
        
        .applications-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .applications-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .applications-section-title i {
            color: var(--success-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .application-content {
            flex: 1;
        }
        
        .application-batch {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .application-time {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            align-items: flex-start;
        }
        
        .detail-label {
            font-weight: 500;
            min-width: 80px;
            margin-right: var(--margin-sm);
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
            word-break: break-word;
        }
        
        .application-reason {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
        }
        
        .reason-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .reason-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .application-note {
            background: var(--info-light);
            color: var(--info-dark);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-application-action {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .scholarship-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .action-section,
            .applications-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .detail-value {
                text-align: left;
                margin-top: 4px;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">奖学金申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 奖学金申请头部 -->
        <div class="scholarship-header">
            <div class="scholarship-title">奖学金申请</div>
            <div class="scholarship-desc">申请和管理奖学金</div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-section">
            <div class="action-buttons">
                <c:if test="${flag == 'showAdd'}">
                    <button class="btn-mobile btn-add-application" onclick="openToEdit();">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>填写奖学金申请</span>
                    </button>
                </c:if>
                <button class="btn-mobile btn-return" onclick="returnIndex();">
                    <i class="ace-icon fa fa-reply"></i>
                    <span>返回</span>
                </button>
            </div>
        </div>
        
        <!-- 申请列表 -->
        <div class="applications-section">
            <div class="applications-section-header">
                <div class="applications-section-title">
                    <i class="ace-icon fa fa-trophy"></i>
                    奖学金申请列表
                </div>
            </div>
            
            <div id="applicationsList">
                <!-- 动态加载申请列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreApplications();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-trophy"></i>
            <div class="empty-state-title">暂无申请记录</div>
            <div class="empty-state-desc">您还没有提交任何奖学金申请</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let applicationsData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;

        $(function() {
            initPage();
            loadApplications(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载更多申请
        function loadMoreApplications() {
            if (hasMore) {
                loadApplications(currentPage + 1, false);
            }
        }

        // 加载申请数据
        function loadApplications(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/scholarshipApplication/index/query/getPage",
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            applicationsData = data.records;
                        } else {
                            applicationsData = applicationsData.concat(data.records);
                        }

                        totalCount = data.pageContext.totalCount;
                        currentPage = page;
                        hasMore = applicationsData.length < totalCount;

                        renderApplicationsList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            applicationsData = [];
                            renderApplicationsList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    showEmptyState(true);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplicationsList(reset = false) {
            const container = $('#applicationsList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : applicationsData.length - pageSize;
            const endIndex = applicationsData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (applicationsData[i]) {
                    const itemHtml = createApplicationItem(applicationsData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建申请项目HTML
        function createApplicationItem(application, index) {
            return `
                <div class="application-item">
                    <div class="application-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="application-index">${index + 1}</div>
                            <div class="application-content">
                                <div class="application-batch">${application.PCM || ''}</div>
                                <div class="application-time">申请时间：${application.CZSJ || ''}</div>
                            </div>
                        </div>
                    </div>

                    <div class="application-details">
                        <div class="detail-item">
                            <span class="detail-label">排名比</span>
                            <span class="detail-value">${application.PMB || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">处分信息</span>
                            <span class="detail-value">${application.CFXX || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">不及格课程</span>
                            <span class="detail-value">${application.BJGKCXX || ''}</span>
                        </div>
                    </div>

                    ${application.SQYY ? `
                    <div class="application-reason">
                        <div class="reason-title">申请原因</div>
                        <div class="reason-content">${application.SQYY}</div>
                    </div>
                    ` : ''}

                    ${application.BZ ? `
                    <div class="application-note">
                        <strong>备注：</strong>${application.BZ}
                    </div>
                    ` : ''}

                    <div class="application-actions">
                        <button class="btn-application-action btn-delete"
                                onclick="revokeInfo('${application.SQBH}', '${application.PCH}');">
                            <i class="ace-icon fa fa-trash-o"></i>
                            <span>撤销申请</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // 添加/修改申请
        function openToEdit() {
            const url = "/student/personalManagement/individualApplication/scholarshipApplication/index/edit/openToEdit";

            if (parent && parent.addTab) {
                parent.addTab('填写奖学金申请', url);
            } else {
                window.open(url, '_blank');
            }
        }

        // 撤销申请
        function revokeInfo(sqbh, pch) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm("确定要撤销申请？", function(confirmed) {
                    if (confirmed) {
                        doRevokeInfo(sqbh, pch);
                    }
                });
            } else {
                if (confirm("确定要撤销申请？")) {
                    doRevokeInfo(sqbh, pch);
                }
            }
        }

        // 执行撤销操作
        function doRevokeInfo(sqbh, pch) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/scholarshipApplication/index/revokeInfo",
                type: "post",
                data: "sqbh=" + sqbh + "&pch=" + pch + "&tokenValue=" + $("#tokenValue").val(),
                dataType: "json",
                success: function(response) {
                    if (response.status != 200) {
                        showError(response.msg || '操作失败');
                    } else {
                        if (response.data.result.indexOf("/") != -1) {
                            window.location.href = response.data.result;
                        } else {
                            if (response.data.result == "ok") {
                                showSuccess("撤销成功！");
                                loadApplications(1, true);
                            }
                        }
                        $("#tokenValue").val(response.data.token);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 返回首页
        function returnIndex() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                location.href = "/student/application/index";
            }
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && applicationsData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (applicationsData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.applications-section').hide();
            } else {
                $('#emptyState').hide();
                $('.applications-section').show();
            }
        }

        // 刷新数据
        function refreshData() {
            loadApplications(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
