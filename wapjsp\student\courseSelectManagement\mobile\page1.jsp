<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html>
<html>
<head>
    <title>选择培养方案</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="/wapjsp/css/mobile-framework.css">
    <link rel="stylesheet" href="/wapjsp/css/font-awesome.min.css">
    <script src="/wapjsp/js/jquery.min.js"></script>
    <script src="/wapjsp/js/layer.js"></script>
    <script src="/wapjsp/js/urp.js"></script>
    <style>
        .selection-container {
            padding: 16px;
        }
        
        .section-mobile {
            margin-bottom: 24px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title i {
            color: var(--primary-color);
        }
        
        .option-button {
            width: 100%;
            background: var(--card-background);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            transition: all 0.3s ease;
            cursor: pointer;
            min-height: 60px;
            position: relative;
            overflow: hidden;
        }
        
        .option-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--card-shadow-hover);
        }
        
        .option-button.selected {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            color: white;
            box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3);
        }
        
        .option-content {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }
        
        .option-icon {
            font-size: 20px;
            width: 24px;
            text-align: center;
        }
        
        .option-text {
            flex: 1;
        }
        
        .option-badge {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            background: var(--background-secondary);
            color: var(--text-secondary);
            margin-left: 8px;
        }
        
        .option-button.selected .option-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .check-icon {
            font-size: 24px;
            color: white;
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s ease;
        }
        
        .option-button.selected .check-icon {
            opacity: 1;
            transform: scale(1);
        }
        
        .major-option {
            border-color: var(--success-color);
        }
        
        .major-option.selected {
            background: linear-gradient(135deg, var(--success-light), var(--success-color));
            border-color: var(--success-color);
            box-shadow: 0 4px 20px rgba(82, 196, 26, 0.3);
        }
        
        .minor-option {
            border-color: var(--info-color);
        }
        
        .minor-option.selected {
            background: linear-gradient(135deg, var(--info-light), var(--info-color));
            border-color: var(--info-color);
            box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3);
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: white;
            font-size: 16px;
            font-weight: 500;
        }
        
        .continue-button {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 16px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(100px);
        }
        
        .continue-button.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .continue-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(24, 144, 255, 0.4);
        }
        
        .selection-summary {
            background: var(--background-secondary);
            border-radius: 8px;
            padding: 12px;
            margin-top: 16px;
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }
        
        .summary-value {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(24, 144, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0); }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">选择培养方案</div>
            <div class="navbar-action">
                <i class="ace-icon fa fa-graduation-cap"></i>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-mobile">
            <div class="selection-container">
                <!-- 培养方案选择 -->
                <div class="section-mobile">
                    <h3 class="section-title">
                        <i class="fa fa-book"></i>
                        选择方案名称
                    </h3>
                    <div id="div_pyfaList">
                        <!-- 培养方案列表将在这里动态生成 -->
                    </div>
                </div>

                <!-- 选课方式选择 -->
                <div class="section-mobile">
                    <h3 class="section-title">
                        <i class="fa fa-list"></i>
                        选择选课方式
                    </h3>
                    <div id="div_xkfsList">
                        <button id="jhxk" class="option-button" data-type="course-method">
                            <div class="option-content">
                                <i class="fa fa-check-square-o option-icon"></i>
                                <span class="option-text">推荐选课</span>
                                <span class="option-badge">智能推荐</span>
                            </div>
                            <i class="fa fa-check check-icon"></i>
                        </button>
                        
                        <button id="faxk" class="option-button" data-type="course-method">
                            <div class="option-content">
                                <i class="fa fa-sitemap option-icon"></i>
                                <span class="option-text">方案选课</span>
                                <span class="option-badge">按方案</span>
                            </div>
                            <i class="fa fa-check check-icon"></i>
                        </button>
                        
                        <button id="xirxk" class="option-button" data-type="course-method">
                            <div class="option-content">
                                <i class="fa fa-university option-icon"></i>
                                <span class="option-text">系任选课</span>
                                <span class="option-badge">系内选择</span>
                            </div>
                            <i class="fa fa-check check-icon"></i>
                        </button>
                        
                        <button id="xarxk" class="option-button" data-type="course-method">
                            <div class="option-content">
                                <i class="fa fa-globe option-icon"></i>
                                <span class="option-text">校任选课</span>
                                <span class="option-badge">全校选择</span>
                            </div>
                            <i class="fa fa-check check-icon"></i>
                        </button>
                        
                        <button id="zyxk" class="option-button" data-type="course-method">
                            <div class="option-content">
                                <i class="fa fa-star option-icon"></i>
                                <span class="option-text">自由选课</span>
                                <span class="option-badge">自由选择</span>
                            </div>
                            <i class="fa fa-check check-icon"></i>
                        </button>
                        
                        <button id="cxxk" class="option-button" data-type="course-method">
                            <div class="option-content">
                                <i class="fa fa-repeat option-icon"></i>
                                <span class="option-text">重修选课</span>
                                <span class="option-badge">重新学习</span>
                            </div>
                            <i class="fa fa-check check-icon"></i>
                        </button>
                        
                        <button id="fxxk" class="option-button" data-type="course-method" style="display: none;">
                            <div class="option-content">
                                <i class="fa fa-refresh option-icon"></i>
                                <span class="option-text">复修选课</span>
                                <span class="option-badge">复习提高</span>
                            </div>
                            <i class="fa fa-check check-icon"></i>
                        </button>
                    </div>
                </div>

                <!-- 选择摘要 -->
                <div class="selection-summary" id="selectionSummary" style="display: none;">
                    <div class="summary-item">
                        <span>选择的方案:</span>
                        <span class="summary-value" id="selectedPlan">未选择</span>
                    </div>
                    <div class="summary-item">
                        <span>选课方式:</span>
                        <span class="summary-value" id="selectedMethod">未选择</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 继续按钮 -->
        <button class="continue-button" id="continueButton" onclick="submitSelection()">
            <i class="fa fa-arrow-right"></i>
            进入选课
        </button>

        <!-- 加载遮罩 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在跳转，请稍候...</div>
        </div>
    </div>

    <!-- 隐藏表单 -->
    <form name="form" action="/student/courseSelect/selectCourse/index" method="get" style="display: none;">
        <input type="hidden" name="mobile" value="true"/>
        <input type="hidden" id="pyfa" name="fajhh" value=""/>
        <input type="hidden" id="xkfs" name="xkfs" value=""/>
    </form>

    <script>
        // 全局变量
        let selectedPlan = null;
        let selectedMethod = null;
        let pyfaList = [];
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            initializePage();
            bindEvents();
        });
        
        // 初始化页面
        function initializePage() {
            // 根据权限控制显示的选课方式
            if("${yxcxfx}" == "1" || ("${xkjdlx}" == "005" && "${xxbm}" == "100010")) {
                $("#div_xkfsList button").hide();
                $("#faxk").show();
                $("#cxxk").show();
            } else {
                var xsfaxk = '${xsfaxk}';
                var xszyxk = '${xszyxk}';
                var xsfxxk = '${xsfxxk}';
                
                if (xsfaxk == "0") {
                    $("#faxk").hide();
                }
                if (xszyxk == "0") {
                    $("#zyxk").hide();
                }
                if (xsfxxk == "1") {
                    $("#fxxk").show();
                }
            }
            
            // 加载培养方案列表
            loadPyfaList();
        }
        
        // 加载培养方案列表
        function loadPyfaList() {
            try {
                pyfaList = eval('(${pyblist})');
                let pyfaCont = "";
                
                pyfaList.forEach(function(plan, index) {
                    const isMainMajor = plan.xdlxmc === "主修";
                    const buttonClass = isMainMajor ? "major-option" : "minor-option";
                    const iconClass = isMainMajor ? "fa-graduation-cap" : "fa-book";
                    
                    const buttonHtml = `
                        <button id="${plan.id.fajhh}" class="option-button ${buttonClass}" data-type="plan" data-plan-name="${plan.famc}" data-plan-type="${plan.xdlxmc}">
                            <div class="option-content">
                                <i class="fa ${iconClass} option-icon"></i>
                                <span class="option-text">${plan.famc}</span>
                                <span class="option-badge">${plan.xdlxmc}</span>
                            </div>
                            <i class="fa fa-check check-icon"></i>
                        </button>
                    `;
                    
                    if (isMainMajor) {
                        pyfaCont = buttonHtml + pyfaCont; // 主修放在前面
                    } else {
                        pyfaCont += buttonHtml;
                    }
                });
                
                $("#div_pyfaList").html(pyfaCont);
                
                // 默认选择第一个方案
                setTimeout(function() {
                    $("#div_pyfaList button:first").click();
                }, 100);
                
            } catch (e) {
                console.error("加载培养方案失败:", e);
                showError("加载培养方案失败，请刷新页面重试");
            }
        }
        
        // 绑定事件
        function bindEvents() {
            // 培养方案选择事件
            $(document).on('click', '[data-type="plan"]', function() {
                selectPlan(this);
            });
            
            // 选课方式选择事件
            $(document).on('click', '[data-type="course-method"]', function() {
                selectCourseMethod(this);
            });
        }
        
        // 选择培养方案
        function selectPlan(button) {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            // 更新选择状态
            $('#div_pyfaList .option-button').removeClass('selected');
            $(button).addClass('selected');
            
            // 更新选择信息
            selectedPlan = {
                id: $(button).attr('id'),
                name: $(button).data('plan-name'),
                type: $(button).data('plan-type')
            };
            
            $("#pyfa").val(selectedPlan.id);
            updateSelectionSummary();
            checkCanContinue();
        }
        
        // 选择选课方式
        function selectCourseMethod(button) {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            // 更新选择状态
            $('#div_xkfsList .option-button').removeClass('selected');
            $(button).addClass('selected');
            
            // 更新选择信息
            selectedMethod = {
                id: $(button).attr('id'),
                name: $(button).find('.option-text').text()
            };
            
            $("#xkfs").val(selectedMethod.id);
            updateSelectionSummary();
            checkCanContinue();
            
            // 如果是选课方式，延迟提交
            setTimeout(function() {
                submitSelection();
            }, 800);
        }
        
        // 更新选择摘要
        function updateSelectionSummary() {
            if (selectedPlan || selectedMethod) {
                $('#selectedPlan').text(selectedPlan ? `${selectedPlan.name}(${selectedPlan.type})` : '未选择');
                $('#selectedMethod').text(selectedMethod ? selectedMethod.name : '未选择');
                $('#selectionSummary').show();
            }
        }
        
        // 检查是否可以继续
        function checkCanContinue() {
            if (selectedPlan && selectedMethod) {
                $('#continueButton').addClass('show pulse-animation');
            } else if (selectedPlan) {
                $('#continueButton').removeClass('show pulse-animation');
            }
        }
        
        // 提交选择
        function submitSelection() {
            if (!selectedPlan) {
                showError("请先选择培养方案");
                return;
            }
            
            if (!selectedMethod) {
                showError("请先选择选课方式");
                return;
            }
            
            // 显示加载状态
            showLoading();
            
            // 提交表单
            setTimeout(function() {
                document.form.submit();
            }, 1000);
        }
        
        // 显示加载状态
        function showLoading() {
            $('#loadingOverlay').addClass('show');
        }
        
        // 隐藏加载状态
        function hideLoading() {
            $('#loadingOverlay').removeClass('show');
        }
        
        // 显示错误信息
        function showError(message) {
            layer.alert(message, {
                icon: 2,
                title: '提示',
                skin: 'layer-mobile'
            });
        }
        
        // 兼容原有的togglefa函数
        function togglefa(obj) {
            if ($(obj).data('type') === 'plan') {
                selectPlan(obj);
            } else {
                selectCourseMethod(obj);
            }
        }
        
        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                hideLoading();
            }
        });
    </script>
</body>
</html>
