<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>重修选课</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 重修选课页面样式 */
        .rebuild-header {
            background: linear-gradient(135deg, var(--primary-color), var(--warning-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .rebuild-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .rebuild-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .time-info-section {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
            font-size: var(--font-size-small);
            line-height: 1.5;
        }
        
        .contact-info-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .contact-info-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .contact-info-title i {
            color: var(--info-color);
        }
        
        .contact-info-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .contact-item {
            display: flex;
            flex-direction: column;
            font-size: var(--font-size-small);
        }
        
        .contact-label {
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .contact-value {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .btn-update-contact {
            width: 100%;
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-update-contact:hover {
            background: var(--info-dark);
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-title i {
            color: var(--primary-color);
        }
        
        .filter-form {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }
        
        .filter-select {
            flex: 1;
            padding: var(--padding-sm);
            border: 1px solid var(--divider-color);
            border-radius: 6px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
        }
        
        .btn-search {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-sm) var(--padding-md);
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-search:hover {
            background: var(--primary-dark);
        }
        
        .action-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }
        
        .btn-action {
            padding: var(--padding-sm);
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-add {
            background: var(--success-color);
            color: white;
        }
        
        .btn-add:hover {
            background: var(--success-dark);
        }
        
        .btn-update-payment {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-update-payment:hover {
            background: var(--warning-dark);
        }
        
        .applications-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .applications-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .applications-section-title i {
            color: var(--warning-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-index {
            background: var(--warning-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .application-content {
            flex: 1;
        }
        
        .application-time {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .status-badges {
            display: flex;
            gap: var(--spacing-xs);
            margin-bottom: var(--margin-sm);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-applied {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-cancelled {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-waiting {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-selected {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-dropped {
            background: var(--text-disabled);
            color: white;
        }
        
        .payment-unpaid {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .payment-paying {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .payment-paid {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .payment-failed {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .payment-refunded {
            background: var(--text-disabled);
            color: white;
        }
        
        .payment-exception {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .payment-free {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .course-info {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .course-section {
            margin-bottom: var(--margin-sm);
        }
        
        .course-section:last-child {
            margin-bottom: 0;
        }
        
        .course-section-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xs);
        }
        
        .course-detail-item {
            font-size: var(--font-size-mini);
        }
        
        .application-reason {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-application-action {
            flex: 1;
            min-width: 80px;
            padding: 6px 8px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--error-color);
            color: white;
        }
        
        .btn-pay {
            background: var(--success-color);
            color: white;
        }
        
        .btn-refresh {
            background: var(--info-color);
            color: white;
        }
        
        .btn-select-course {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-drop-course {
            background: var(--error-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        @media (max-width: 480px) {
            .rebuild-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .time-info-section,
            .contact-info-section,
            .filter-section,
            .action-section,
            .applications-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .contact-info-content {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
            
            .course-details {
                grid-template-columns: 1fr;
            }
            
            .application-actions {
                flex-direction: column;
            }
            
            .btn-application-action {
                flex: none;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">重修选课</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 重修选课头部 -->
        <div class="rebuild-header">
            <div class="rebuild-title">重修选课</div>
            <div class="rebuild-desc">管理您的重修选课申请记录</div>
        </div>
        
        <!-- 时间信息 -->
        <div class="time-info-section">
            <div><strong>重修申请时间：</strong>
                <fmt:parseDate var="sqkssj" value="${sqkssj}" pattern="yyyyMMddHHssmm"/>
                <fmt:formatDate pattern="yyyy-MM-dd HH:ss:mm" value="${sqkssj}"/>~
                <fmt:parseDate var="sqjssj" value="${sqjssj}" pattern="yyyyMMddHHssmm"/>
                <fmt:formatDate pattern="yyyy-MM-dd HH:ss:mm" value="${sqjssj}"/>
            </div>
            <div>最多允许申请${sq_maxxf}学分，已申请${cxzxf}学分</div>
            <br>
            <div><strong>重修选课时间：</strong>
                <fmt:parseDate var="xkkssj" value="${xkkssj}" pattern="yyyyMMddHHssmm"/>
                <fmt:formatDate pattern="yyyy-MM-dd HH:ss:mm" value="${xkkssj}"/>~
                <fmt:parseDate var="xkjssj" value="${xkjssj}" pattern="yyyyMMddHHssmm"/>
                <fmt:formatDate pattern="yyyy-MM-dd HH:ss:mm" value="${xkjssj}"/>
            </div>
            <div>最多允许申请${xk_maxxf}学分，已申请${xkzxf}学分</div>
        </div>
        
        <!-- 联系信息 -->
        <div class="contact-info-section">
            <div class="contact-info-title">
                <i class="ace-icon fa fa-user"></i>
                联系信息
            </div>
            <div class="contact-info-content">
                <div class="contact-item">
                    <div class="contact-label">联系电话</div>
                    <div class="contact-value" id="lxdh">${lxdh}</div>
                </div>
                <div class="contact-item">
                    <div class="contact-label">银行卡号</div>
                    <div class="contact-value" id="yhkh">${yhkh}</div>
                </div>
            </div>
            <c:if test="${sq_flag=='showAdd'}">
                <button class="btn-update-contact" onclick="saveGrxxb();">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>维护联系电话和银行卡号</span>
                </button>
            </c:if>
        </div>
        
        <!-- 查询条件 -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            <form name="ApplyInfoForm" class="filter-form">
                <select class="filter-select" name="zxjxjhh">
                    <cache:query var="view" region="jh_zxjxjhb_view" orderby="zxjxjhh desc"/>
                    <option value="">全部</option>
                    <c:forEach items="${view}" var="view">
                        <option value="${view.zxjxjhh}" <c:if test="${view.zxjxjhh==zxjxjhh}">selected</c:if>>${view.zxjxjhm}</option>
                    </c:forEach>
                </select>
                <button type="button" class="btn-search" onclick="getApplyInfo(1, '30_sl', true);">
                    <i class="ace-icon fa fa-search"></i>
                    <span>查询</span>
                </button>
            </form>
        </div>
        
        <!-- 操作按钮 -->
        <c:if test="${sq_flag=='showAdd'}">
            <div class="action-section">
                <div class="action-buttons">
                    <button class="btn-action btn-add" onclick="addApply('add','');">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>申请</span>
                    </button>
                    <button class="btn-action btn-update-payment" onclick="doUpdatePaymentStatus('all','');">
                        <i class="ace-icon fa fa-refresh"></i>
                        <span>批量更新缴费状态</span>
                    </button>
                </div>
            </div>
        </c:if>
        
        <!-- 申请列表 -->
        <div class="applications-section">
            <div class="applications-section-header">
                <div class="applications-section-title">
                    <i class="ace-icon fa fa-list"></i>
                    申请列表
                </div>
            </div>
            
            <div id="applicationsList">
                <!-- 动态加载申请列表 -->
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-graduation-cap"></i>
            <div class="empty-state-title">暂无申请记录</div>
            <div class="empty-state-desc">您还没有提交任何重修选课申请</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let applicationData = [];
        let lxdh = "${lxdh}";
        let yhkh = "${yhkh}";

        $(function() {
            initPage();
            getApplyInfo(1, "30_sl", true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 分页查询
        var pageConditions = "";
        function getApplyInfo(page, pageSizeVal, conditionChanged) {
            if (pageSizeVal == undefined) {
                pageSizeVal = "100_sl";
                page = "1";
            }

            if (conditionChanged) {
                pageConditions = $(document.ApplyInfoForm).serialize();
            }

            var parr = (pageSizeVal + "").split("_");
            var pageSize = parseInt(parr[0]);

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/rebuildCourseSelection/queryPages",
                cache: false,
                type: "post",
                data: pageConditions + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records) {
                        applicationData = data.records;
                        totalCount = data.pageContext ? data.pageContext.totalCount : 0;

                        if (applicationData.length > 0) {
                            renderApplications();
                        } else {
                            showEmptyState();
                        }
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplications() {
            const container = $('#applicationsList');
            container.empty();

            if (applicationData.length === 0) {
                showEmptyState();
                return;
            }

            applicationData.forEach(function(application, index) {
                const applicationHtml = createApplicationItem(application, index);
                container.append(applicationHtml);
            });

            hideEmptyState();
        }

        // 创建申请项目HTML
        function createApplicationItem(application, index) {
            const statusInfo = getStatusInfo(application.SQZT);
            const paymentInfo = getPaymentInfo(application.STATUS);

            return `
                <div class="application-item">
                    <div class="application-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="application-index">${index + 1}</div>
                            <div class="application-content">
                                <div class="application-time">${application.SQSJ || ''}</div>
                                <div class="status-badges">
                                    <div class="status-badge ${statusInfo.class}">${statusInfo.text}</div>
                                    <div class="status-badge ${paymentInfo.class}">${paymentInfo.text}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="course-info">
                        <div class="course-section">
                            <div class="course-section-title">申请课程</div>
                            <div class="course-details">
                                <div class="course-detail-item">课程号：${application.KCH || ''}</div>
                                <div class="course-detail-item">课程名：${application.KCM || ''}</div>
                                <div class="course-detail-item">学时：${application.XS || ''}</div>
                                <div class="course-detail-item">学分：${application.XF || ''}</div>
                                <div class="course-detail-item">课程成绩：${application.KCCJ || ''}</div>
                            </div>
                        </div>

                        ${application.TDKCH || application.TDKCM ? `
                            <div class="course-section">
                                <div class="course-section-title">重修课程</div>
                                <div class="course-details">
                                    <div class="course-detail-item">课程号：${application.TDKCH || ''}</div>
                                    <div class="course-detail-item">课程名：${application.TDKCM || ''}</div>
                                    <div class="course-detail-item">学时：${application.TDKCXS || ''}</div>
                                    <div class="course-detail-item">学分：${application.TDKCXF || ''}</div>
                                    <div class="course-detail-item">课序号：${application.KXH || ''}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>

                    ${application.CXYY ? `
                        <div class="application-reason">
                            <strong>申请原因：</strong>${application.CXYY}
                        </div>
                    ` : ''}

                    <div class="application-actions">
                        ${getActionButtons(application)}
                    </div>
                </div>
            `;
        }

        // 获取状态信息
        function getStatusInfo(status) {
            switch(status) {
                case "01":
                    return { class: 'status-applied', text: '已申请' };
                case "02":
                    return { class: 'status-cancelled', text: '已撤销' };
                case "03":
                    return { class: 'status-waiting', text: '待选课' };
                case "04":
                    return { class: 'status-selected', text: '已选课' };
                case "05":
                    return { class: 'status-dropped', text: '已退课' };
                default:
                    return { class: 'status-applied', text: '未知' };
            }
        }

        // 获取缴费状态信息
        function getPaymentInfo(status) {
            switch(status) {
                case "0":
                    return { class: 'payment-unpaid', text: '未交费' };
                case "1":
                    return { class: 'payment-paying', text: '缴费中' };
                case "2":
                    return { class: 'payment-paid', text: '已缴费' };
                case "3":
                    return { class: 'payment-failed', text: '缴费失败' };
                case "4":
                    return { class: 'payment-refunded', text: '已退款' };
                case "9":
                    return { class: 'payment-exception', text: '操作异常' };
                case "-99":
                    return { class: 'payment-free', text: '无需缴费' };
                default:
                    return { class: 'payment-unpaid', text: '未知' };
            }
        }

        // 获取操作按钮
        function getActionButtons(application) {
            let buttons = [];

            if (application.SQZT == "01") {
                // 已申请状态
                if ("${sq_flag}" == "showAdd" && application.STATUS != "1" && application.STATUS != "2") {
                    buttons.push(`
                        <button class="btn-application-action btn-edit" onclick="addApply('edit','${application.SQBH}');">
                            <i class="ace-icon fa fa-edit"></i>
                            <span>修改</span>
                        </button>
                    `);
                    buttons.push(`
                        <button class="btn-application-action btn-cancel" onclick="replyApply('${application.SQZT}','${application.SQBH}');">
                            <i class="ace-icon fa fa-reply"></i>
                            <span>撤销</span>
                        </button>
                    `);
                }

                if (("${sq_flag}" == "showAdd" || "${xk_flag}" == "showAdd") &&
                    (application.STATUS == "0" || application.STATUS == "3" || application.STATUS == "4" || application.STATUS == "9")) {
                    buttons.push(`
                        <button class="btn-application-action btn-pay" onclick="print('one','${application.SQBH}');">
                            <i class="ace-icon fa fa-yen"></i>
                            <span>我要缴费</span>
                        </button>
                    `);
                }

                buttons.push(`
                    <button class="btn-application-action btn-refresh" onclick="doUpdatePaymentStatus('one','${application.SQBH}');">
                        <i class="ace-icon fa fa-refresh"></i>
                        <span>更新缴费状态</span>
                    </button>
                `);
            } else if ("${xk_flag}" == "showAdd" && application.SQZT == "03") {
                // 待选课状态
                buttons.push(`
                    <button class="btn-application-action btn-select-course" onclick="queryCourse('${application.SQBH}');">
                        <i class="ace-icon fa fa-shopping-cart"></i>
                        <span>选课</span>
                    </button>
                `);
                buttons.push(`
                    <button class="btn-application-action btn-cancel" onclick="replyApply('${application.SQZT}','${application.SQBH}');">
                        <i class="ace-icon fa fa-times"></i>
                        <span>放弃重修</span>
                    </button>
                `);
            } else if ("${xk_flag}" == "showAdd" && application.SQZT == "04") {
                // 已选课状态
                buttons.push(`
                    <button class="btn-application-action btn-drop-course" onclick="replyApply('${application.SQZT}','${application.SQBH}');">
                        <i class="ace-icon fa fa-shopping-cart"></i>
                        <span>退课</span>
                    </button>
                `);
            }

            return buttons.join('');
        }

        // 维护个人信息
        function saveGrxxb() {
            if (typeof urp !== 'undefined' && urp.prompt) {
                // 使用urp的prompt功能
                const content = `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px;">联系电话：</label>
                            <input type="text" id="edit_lxdh" value="${lxdh}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px;">银行卡号：</label>
                            <input type="text" id="edit_yhkh" value="${yhkh}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>
                `;

                urp.confirm(content, function(confirmed) {
                    if (confirmed) {
                        doSaveGrxxb();
                    }
                }, "维护联系电话和银行卡号");
            } else {
                // 简单的prompt方式
                const newLxdh = prompt("请输入联系电话：", lxdh);
                if (newLxdh === null) return;

                const newYhkh = prompt("请输入银行卡号：", yhkh);
                if (newYhkh === null) return;

                if (validateContact(newLxdh, newYhkh)) {
                    doSaveContact(newLxdh, newYhkh);
                }
            }
        }

        // 验证联系信息
        function validateContact(phone, bankCard) {
            if (!phone || phone.trim() === "") {
                showError("请填写你的联系电话！");
                return false;
            }

            const phoneReg = /^1[3456789]\d{9}$/;
            if (!phoneReg.test(phone) || phone.length != 11) {
                showError("请输入合法的手机号！");
                return false;
            }

            if (!/^\d+$/.test(bankCard) || !bankCard || bankCard.trim() === "" || bankCard.length < 16) {
                showError("请输入正确的银行卡号！");
                return false;
            }

            return true;
        }

        // 执行保存联系信息
        function doSaveContact(phone, bankCard) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/rebuildCourseSelection/saveGrxxb",
                type: "post",
                data: "lxdh=" + phone + "&yhkh=" + bankCard + "&tokenValue=" + $("#tokenValue").val(),
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data.result.indexOf("/") != -1) {
                        window.location.href = data.result;
                    } else {
                        $("#tokenValue").val(data.token);
                        if (data.result == "ok") {
                            showSuccess("保存成功！");
                            lxdh = phone;
                            yhkh = bankCard;
                            $("#lxdh").html(lxdh);
                            $("#yhkh").html(yhkh);
                        } else {
                            showError(data.result);
                        }
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 新增/修改申请
        function addApply(type, sqbh) {
            if (lxdh && yhkh) {
                const url = "/student/personalManagement/individualApplication/rebuildCourseSelection/addApply?type=" + type + "&sqbh=" + sqbh;

                if (parent && parent.addTab) {
                    parent.addTab(type === 'add' ? '新增申请' : '修改申请', url);
                } else {
                    window.location.href = url;
                }
            } else {
                showError("请填写你的联系电话和银行卡号！");
            }
        }

        // 撤销申请
        function replyApply(sqzt, sqbh) {
            let msg = "";
            if (sqzt == "03") {
                msg = "是否确认放弃重修？";
            } else if (sqzt == "04") {
                msg = "是否确认退课？";
            } else {
                msg = "是否确认撤销当前申请？";
            }

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(msg, function(confirmed) {
                    if (confirmed) {
                        doReplyApply(sqbh, sqzt);
                    }
                });
            } else {
                if (confirm(msg)) {
                    doReplyApply(sqbh, sqzt);
                }
            }
        }

        // 执行撤销申请
        function doReplyApply(sqbh, sqzt) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/rebuildCourseSelection/doReplyApply",
                cache: false,
                type: "post",
                data: {
                    "tokenValue": $("#tokenValue").val(),
                    "sqbh": sqbh,
                    "sqzt": sqzt
                },
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    $("#tokenValue").val(data.token);
                    if (data.result.indexOf("/") != -1) {
                        window.location.href = data.result;
                    } else {
                        if (data.result == "ok") {
                            getApplyInfo(1, "30_sl", true);
                            showSuccess("操作成功！");
                        } else {
                            showError(data.result);
                        }
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 更新缴费状态
        function doUpdatePaymentStatus(type, sqbh) {
            if (type == "all") {
                // 批量更新所有已申请的记录
                sqbh = "";
                applicationData.forEach(function(app) {
                    if (app.SQZT == "01") {
                        sqbh += sqbh == "" ? "" : ",";
                        sqbh += app.SQBH;
                    }
                });

                if (sqbh === "") {
                    showError("没有需要更新缴费状态的申请记录！");
                    return;
                }
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/rebuildCourseSelection/doUpdatePaymentStatus",
                cache: false,
                type: "post",
                data: {
                    "tokenValue": $("#tokenValue").val(),
                    "sqbh": sqbh
                },
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    $("#tokenValue").val(data.token);
                    if (data.result.indexOf("/") != -1) {
                        window.location.href = data.result;
                    } else {
                        if (data.result == "ok") {
                            getApplyInfo(1, "30_sl", true);
                            showSuccess("操作成功！");
                        } else {
                            showError(data.result);
                        }
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 缴费
        function print(type, sqbh) {
            // 这里应该调用缴费接口，暂时用alert提示
            showError("缴费功能需要在实际环境中配置！");
        }

        // 选课
        function queryCourse(sqbh) {
            const url = "/student/personalManagement/individualApplication/rebuildCourseSelection/selectCourse?sqbh=" + sqbh;

            if (parent && parent.addTab) {
                parent.addTab('选课', url);
            } else {
                window.location.href = url;
            }
        }

        // 显示空状态
        function showEmptyState() {
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 返回首页
        function returnIndex() {
            location.href = "/student/application/index";
        }

        // 刷新数据
        function refreshData() {
            getApplyInfo(1, "30_sl", true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
