<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>打印准考证</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 打印准考证页面样式 */
        .certificate-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .certificate-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .certificate-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .exam-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            background: var(--bg-tertiary);
            padding: 0;
            border-bottom: 1px solid var(--divider-color);
            overflow-x: auto;
            white-space: nowrap;
        }
        
        .tabs-nav {
            display: flex;
            min-width: max-content;
        }
        
        .tab-item {
            padding: var(--padding-md);
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-base);
            white-space: nowrap;
            text-align: center;
            line-height: 1.3;
        }
        
        .tab-item.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: var(--bg-primary);
        }
        
        .tab-content {
            padding: var(--padding-md);
        }
        
        .time-info {
            background: var(--success-light);
            color: var(--success-dark);
            padding: var(--padding-md);
            border-radius: 6px;
            margin-bottom: var(--margin-md);
            font-size: var(--font-size-small);
            text-align: center;
        }
        
        .courses-section {
            margin-bottom: var(--margin-md);
        }
        
        .courses-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .courses-section-title i {
            color: var(--primary-color);
        }
        
        .course-item {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--primary-color);
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .course-content {
            flex: 1;
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .course-number {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .print-section {
            margin-top: var(--margin-md);
            text-align: center;
        }
        
        .btn-print {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md) var(--padding-lg);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
            transition: all var(--transition-base);
        }
        
        .btn-print:hover {
            background: var(--success-dark);
        }
        
        .btn-print:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .print-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            overflow-y: auto;
        }
        
        .print-modal-content {
            background: var(--bg-primary);
            margin: 20px;
            border-radius: 8px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        
        .print-modal-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1001;
        }
        
        .print-modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .btn-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
        }
        
        .print-modal-body {
            padding: var(--padding-md);
        }
        
        .print-embed {
            width: 100%;
            height: 60vh;
            border: none;
            border-radius: 6px;
        }
        
        @media (max-width: 480px) {
            .certificate-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .exam-tabs {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .course-details {
                grid-template-columns: 1fr;
            }
            
            .print-modal-content {
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    <input type="hidden" id="currentKsbh" name="currentKsbh" value=""/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">打印准考证</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 打印准考证头部 -->
        <div class="certificate-header">
            <div class="certificate-title">打印准考证</div>
            <div class="certificate-desc">查看和打印考试准考证</div>
        </div>
        
        <!-- 考试选项卡 -->
        <c:if test="${fn:length(ksList) > 0}">
            <div class="exam-tabs">
                <div class="tabs-header">
                    <div class="tabs-nav" id="examTabsNav">
                        <c:forEach var="kwksb" items="${ksList}" varStatus="status">
                            <button class="tab-item ${status.index == 0 ? 'active' : ''}" 
                                    data-ksbh="${kwksb.id.ksbh}" 
                                    data-bmfs="${kwksb.ksbmfs}"
                                    onclick="switchExamTab('${kwksb.id.ksbh}', '${kwksb.ksbmfs}', this);">
                                ${kwksb.ksmc}
                            </button>
                        </c:forEach>
                    </div>
                </div>
                
                <div class="tab-content">
                    <!-- 打印时间信息 -->
                    <div class="time-info" id="timeInfo">
                        <!-- 动态加载时间信息 -->
                    </div>
                    
                    <!-- 考试课程列表 -->
                    <div class="courses-section">
                        <div class="courses-section-title">
                            <i class="ace-icon fa fa-list"></i>
                            考试课程列表
                        </div>
                        <div id="coursesList">
                            <!-- 动态加载课程列表 -->
                        </div>
                    </div>
                    
                    <!-- 打印按钮 -->
                    <div class="print-section" id="printSection" style="display: none;">
                        <button class="btn-print" id="btnPrint" onclick="doPrint();">
                            <i class="ace-icon fa fa-print"></i>
                            <span>打印准考证</span>
                        </button>
                    </div>
                </div>
            </div>
        </c:if>
        
        <!-- 空状态 -->
        <c:if test="${fn:length(ksList) < 1}">
            <div class="empty-state">
                <i class="ace-icon fa fa-file-text-o"></i>
                <div class="empty-state-title">暂无考试信息</div>
                <div class="empty-state-desc">暂时没有需要打印准考证的考试信息，请确认时间!</div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>
    
    <!-- 打印模态框 -->
    <div class="print-modal" id="printModal">
        <div class="print-modal-content">
            <div class="print-modal-header">
                <div class="print-modal-title">准考证预览</div>
                <button class="btn-close" onclick="closePrintModal();">
                    <i class="ace-icon fa fa-times"></i>
                </button>
            </div>
            <div class="print-modal-body">
                <div id="printContent">
                    <!-- 动态加载打印内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentKsbh = '';
        let currentBmfs = '';
        let examData = null;

        $(function() {
            initPage();
            initFirstTab();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化第一个选项卡
        function initFirstTab() {
            const firstTab = $('.tab-item').first();
            if (firstTab.length > 0) {
                const ksbh = firstTab.data('ksbh');
                const bmfs = firstTab.data('bmfs');
                switchExamTab(ksbh, bmfs, firstTab[0]);
            }
        }

        // 切换考试选项卡
        function switchExamTab(ksbh, bmfs, element) {
            if (currentKsbh === ksbh && currentBmfs === bmfs) return;

            // 更新选项卡状态
            $('.tab-item').removeClass('active');
            $(element).addClass('active');

            // 设置当前选项卡
            currentKsbh = ksbh;
            currentBmfs = bmfs;
            $('#currentKsbh').val(ksbh);

            // 加载考试信息
            loadExamInfo(ksbh, bmfs);
        }

        // 加载考试信息
        function loadExamInfo(ksbh, bmfs) {
            showLoading(true);

            $.ajax({
                url: "/student/examinationManagement/print/admissionCertificate/info",
                type: "post",
                data: "ksbh=" + ksbh + "&bmfs=" + bmfs,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data) {
                        examData = data;
                        renderExamInfo(data);
                        renderCoursesList(data, bmfs);

                        // 控制打印按钮显示
                        if (data.count > 0 && data.bkcount > 0) {
                            $('#printSection').show();
                        } else {
                            $('#printSection').hide();
                        }
                    } else {
                        showError("获取考试信息失败");
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染考试信息
        function renderExamInfo(data) {
            const kwKsbmjdb = data.kwKsbmjdb;
            if (kwKsbmjdb) {
                const timeInfo = `打印准考证时间：${kwKsbmjdb.dyzkzkssjstr} 至 ${kwKsbmjdb.dyzkzjssjstr}`;
                $('#timeInfo').text(timeInfo);
            }
        }

        // 渲染课程列表
        function renderCoursesList(data, bmfs) {
            const container = $('#coursesList');
            container.empty();

            let courses = [];

            if (bmfs === "free") {
                // 自由报名考试
                const kskm = data.kskm;
                if (kskm) {
                    courses.push({
                        kch: kskm[3] || '',
                        kcm: kskm[4] || '',
                        kxh: '0',
                        zxjxjhm: kskm[2] || ''
                    });
                }
            } else {
                // 正常报名考试
                courses = data.kcbkDetails || [];
            }

            if (courses.length === 0) {
                container.html('<div class="empty-state"><i class="ace-icon fa fa-file-text-o"></i><div>暂无课程信息</div></div>');
                return;
            }

            courses.forEach(function(course, index) {
                const courseHtml = createCourseItem(course, index);
                container.append(courseHtml);
            });
        }

        // 创建课程项目HTML
        function createCourseItem(course, index) {
            return `
                <div class="course-item">
                    <div class="course-header">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="course-index">${index + 1}</div>
                            <div class="course-content">
                                <div class="course-name">${course.kcm || ''}</div>
                                <div class="course-number">课程号：${course.kch || ''}</div>
                            </div>
                        </div>
                    </div>

                    <div class="course-details">
                        <div class="detail-item">
                            <span class="detail-label">课序号</span>
                            <span>${course.kxh || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">考试学期</span>
                            <span>${course.zxjxjhm || ''}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 执行打印
        function doPrint() {
            const ksbh = $('#currentKsbh').val();
            if (!ksbh) {
                showError("请先选择考试");
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/examinationManagement/print/admissionCertificate/checkTemplate",
                type: "post",
                data: "ksbh=" + ksbh,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data === "ok") {
                        openPrintModal(ksbh);
                    } else {
                        showError("当前数据还没有准考证模板，请联系管理员！");
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 打开打印模态框
        function openPrintModal(ksbh) {
            const printUrl = `/student/examinationManagement/print/admissionCertificate/toPrint?ksbh=${ksbh}`;
            const embedHtml = `<embed class="print-embed" src="${printUrl}" onload="embedOnload(this);"></embed>`;

            $('#printContent').html(embedHtml);
            $('#printModal').show();
        }

        // 关闭打印模态框
        function closePrintModal() {
            $('#printModal').hide();
            $('#printContent').empty();
        }

        // 嵌入内容加载完成
        function embedOnload(obj) {
            // 可以在这里添加加载完成后的处理
        }

        // 刷新数据
        function refreshData() {
            if (currentKsbh && currentBmfs) {
                loadExamInfo(currentKsbh, currentBmfs);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#printModal').click(function(e) {
            if (e.target === this) {
                closePrintModal();
            }
        });
    </script>
</body>
</html>
