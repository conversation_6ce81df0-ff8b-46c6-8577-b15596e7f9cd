<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>阶段文档提交</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 阶段文档提交页面样式 */
        .documents-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .documents-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .documents-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .thesis-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            background: var(--bg-tertiary);
            padding: 0;
            border-bottom: 1px solid var(--divider-color);
            overflow-x: auto;
            white-space: nowrap;
        }
        
        .tabs-nav {
            display: flex;
            min-width: max-content;
        }
        
        .tab-item {
            padding: var(--padding-md);
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-base);
            white-space: nowrap;
            text-align: center;
            line-height: 1.3;
        }
        
        .tab-item.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: var(--bg-primary);
        }
        
        .tab-content {
            padding: var(--padding-md);
        }
        
        .stage-info-section {
            margin-bottom: var(--margin-md);
        }
        
        .stage-info-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stage-info-title i {
            color: var(--info-color);
        }
        
        .stage-item {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--info-color);
        }
        
        .stage-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .stage-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .stage-detail {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .stage-file-types {
            background: var(--warning-light);
            color: var(--warning-dark);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
        }
        
        .documents-section {
            margin-bottom: var(--margin-md);
        }
        
        .documents-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .documents-section-title i {
            color: var(--success-color);
        }
        
        .document-item {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--primary-color);
        }
        
        .document-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .document-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .document-content {
            flex: 1;
        }
        
        .document-thesis-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .document-student {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .document-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .document-detail {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-uploaded {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .document-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-document-action {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-upload {
            background: var(--success-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        @media (max-width: 480px) {
            .documents-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .thesis-tabs {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .stage-details,
            .document-details {
                grid-template-columns: 1fr;
            }
            
            .document-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">阶段文档提交</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 文档提交头部 -->
        <div class="documents-header">
            <div class="documents-title">阶段文档提交</div>
            <div class="documents-desc">论文子阶段文档提交管理</div>
        </div>
        
        <!-- 论文选项卡 -->
        <div class="thesis-tabs">
            <div class="tabs-header">
                <div class="tabs-nav" id="thesisTabsNav">
                    <c:forEach items="${xsxtfabs}" var="xsxtfab" varStatus="status">
                        <button class="tab-item ${status.index == 0 ? 'active' : ''}" 
                                data-pch="${xsxtfab.pch}" 
                                data-tmbh="${xsxtfab.tmbh}"
                                onclick="switchThesisTab('${xsxtfab.pch}', '${xsxtfab.tmbh}', this);">
                            ${xsxtfab.tmbh}/${xsxtfab.tmmc}
                        </button>
                    </c:forEach>
                </div>
            </div>
            
            <div class="tab-content">
                <!-- 阶段信息 -->
                <div class="stage-info-section">
                    <div class="stage-info-title">
                        <i class="ace-icon fa fa-clock-o"></i>
                        提交时间安排
                    </div>
                    <div id="stageInfoList">
                        <!-- 动态加载阶段信息 -->
                    </div>
                </div>
                
                <!-- 文档列表 -->
                <div class="documents-section">
                    <div class="documents-section-title">
                        <i class="ace-icon fa fa-file-text"></i>
                        文档提交列表
                    </div>
                    <div id="documentsList">
                        <!-- 动态加载文档列表 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无文档数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPch = '';
        let currentTmbh = '';
        let stageData = [];
        let documentsData = [];

        $(function() {
            initPage();
            initFirstTab();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化第一个选项卡
        function initFirstTab() {
            const firstTab = $('.tab-item').first();
            if (firstTab.length > 0) {
                const pch = firstTab.data('pch');
                const tmbh = firstTab.data('tmbh');
                switchThesisTab(pch, tmbh, firstTab[0]);
            }
        }

        // 切换论文选项卡
        function switchThesisTab(pch, tmbh, element) {
            if (currentPch === pch && currentTmbh === tmbh) return;

            // 更新选项卡状态
            $('.tab-item').removeClass('active');
            $(element).addClass('active');

            // 设置当前选项卡
            currentPch = pch;
            currentTmbh = tmbh;

            // 加载数据
            loadStageData(pch, tmbh);
        }

        // 加载阶段数据
        function loadStageData(pch, tmbh) {
            showLoading(true);

            $.ajax({
                url: "/student/thesis/stageddocuments/uploadsub/main/query/stagedata",
                type: "post",
                data: "pch=" + pch + "&tmbh=" + tmbh,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data) {
                        stageData = data.jdxxbs || [];
                        documentsData = data.xsjdwjbs || [];

                        renderStageInfo();
                        renderDocumentsList();
                        showEmptyState(false);
                    } else {
                        stageData = [];
                        documentsData = [];
                        renderStageInfo();
                        renderDocumentsList();
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    showEmptyState(true);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染阶段信息
        function renderStageInfo() {
            const container = $('#stageInfoList');
            container.empty();

            if (stageData.length === 0) {
                return;
            }

            stageData.forEach(function(stage) {
                const stageHtml = createStageItem(stage);
                container.append(stageHtml);
            });
        }

        // 创建阶段项目HTML
        function createStageItem(stage) {
            return `
                <div class="stage-item">
                    <div class="stage-name">${stage.zjdmc || ''}</div>
                    <div class="stage-details">
                        <div class="stage-detail">
                            <span class="detail-label">开始时间</span>
                            <span>${stage.kssj || ''}</span>
                        </div>
                        <div class="stage-detail">
                            <span class="detail-label">结束时间</span>
                            <span>${stage.jzsj || ''}</span>
                        </div>
                        <div class="stage-detail">
                            <span class="detail-label">需要审批</span>
                            <span>${stage.wjxysp || ''}</span>
                        </div>
                    </div>
                    ${stage.wjlx ? `
                    <div class="stage-file-types">
                        <strong>可上传文件类型：</strong>${stage.wjlx}
                    </div>
                    ` : ''}
                </div>
            `;
        }

        // 渲染文档列表
        function renderDocumentsList() {
            const container = $('#documentsList');
            container.empty();

            if (documentsData.length === 0) {
                return;
            }

            documentsData.forEach(function(document, index) {
                const documentHtml = createDocumentItem(document, index);
                container.append(documentHtml);
            });
        }

        // 创建文档项目HTML
        function createDocumentItem(document, index) {
            // 获取状态信息
            const statusInfo = getDocumentStatusInfo(document.wdztdm, document.wdztmc);

            // 构建操作按钮
            let actionButtons = '';
            const canUpload = document.kzsj == "1" &&
                             (urp.isEmpty(document.wdztdm) ||
                              document.wdztdm == '-1' ||
                              document.wdztdm == '0' ||
                              document.wdztdm == '9');

            if (canUpload) {
                actionButtons = `
                    <div class="document-actions">
                        <button class="btn-document-action btn-upload"
                                onclick="openUploadDialog('${document.zxjxjhh}', '${document.tmbh}', '${document.eid}', '${document.wdid}');">
                            <i class="ace-icon fa fa-upload"></i>
                            <span>上传文档</span>
                        </button>
                    </div>
                `;
            } else {
                actionButtons = `
                    <div class="document-actions">
                        <button class="btn-document-action btn-view"
                                onclick="openViewDialog('${document.wdid}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看详情</span>
                        </button>
                    </div>
                `;
            }

            return `
                <div class="document-item">
                    <div class="document-header">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="document-index">${index + 1}</div>
                            <div class="document-content">
                                <div class="document-thesis-title">${document.tmmc || ''}</div>
                                <div class="document-student">学生：${document.xm || ''}</div>
                            </div>
                        </div>
                        <div>
                            <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                        </div>
                    </div>

                    <div class="document-details">
                        <div class="document-detail">
                            <span class="detail-label">题目编号</span>
                            <span>${document.tmbh || ''}</span>
                        </div>
                        <div class="document-detail">
                            <span class="detail-label">英文题目</span>
                            <span>${document.tmywmc || ''}</span>
                        </div>
                        <div class="document-detail">
                            <span class="detail-label">文档名称</span>
                            <span>${document.zjdmc || ''}</span>
                        </div>
                    </div>

                    ${actionButtons}
                </div>
            `;
        }

        // 获取文档状态信息
        function getDocumentStatusInfo(wdztdm, wdztmc) {
            let text = wdztmc || '暂未上传';
            let statusClass = 'status-pending';

            if (wdztdm) {
                switch (wdztdm) {
                    case '1':
                        statusClass = 'status-uploaded';
                        break;
                    case '2':
                        statusClass = 'status-approved';
                        break;
                    case '3':
                        statusClass = 'status-rejected';
                        break;
                    default:
                        statusClass = 'status-pending';
                }
            }

            return { text, class: statusClass };
        }

        // 打开上传对话框
        function openUploadDialog(zxjxjhh, tmbh, eid, wdid) {
            const url = "/student/thesis/stageddocuments/uploadsub/main/get/update?zxjxjhh=" +
                       zxjxjhh + "&tmbh=" + tmbh + "&eid=" + eid + "&wdid=" + wdid;

            if (parent && parent.addTab) {
                parent.addTab('上传文档', url);
            } else {
                window.open(url, '_blank');
            }
        }

        // 打开查看对话框
        function openViewDialog(wdid) {
            const url = "/student/thesis/stageddocuments/uploadsub/show/get/index?wdid=" + wdid;

            if (parent && parent.addTab) {
                parent.addTab('查看文档', url);
            } else {
                window.open(url, '_blank');
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.thesis-tabs').hide();
            } else {
                $('#emptyState').hide();
                $('.thesis-tabs').show();
            }
        }

        // 刷新数据
        function refreshData() {
            if (currentPch && currentTmbh) {
                loadStageData(currentPch, currentTmbh);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // urp工具函数兼容
        if (typeof urp === 'undefined') {
            window.urp = {
                isEmpty: function(value) {
                    return value === null || value === undefined || value === '';
                }
            };
        }
    </script>
</body>
</html>
