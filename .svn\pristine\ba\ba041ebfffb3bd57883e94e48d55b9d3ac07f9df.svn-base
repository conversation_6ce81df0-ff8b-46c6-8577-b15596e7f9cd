<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>

<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=GBK">
	<title>选课公告</title>
	<c:if test="${mobile}">
		<link rel="stylesheet" href="/css/phone/phone.css" type="text/css"></link>
	</c:if>
	<style>
		.self-margin .header {
		    margin-top: 6px !important;
		    margin-bottom: 10px !important;
		    padding-bottom: 4px !important;
		    border-bottom: 1px solid #CCC;
		    line-height: 28px;
		}
		
		.header.grey {
		    border-bottom-color: #c3c3c3;
		}
		h4.smaller {
		    font-size: 17px;
		}
		.header {
		    line-height: 28px;
		    margin-bottom: 16px;
		    margin-top: 18px;
		    padding-bottom: 4px;
		    border-bottom: 1px solid #CCC;
		}
		.grey {
		    color: #777 !important;
		}
		.lighter {
		    font-weight: lighter;
		}
           
           .btn.btn-round {
		    border-radius: 4px !important;
		}
		
		.btn.btn-bold, .btn.btn-round {
		    border-bottom-width: 2px;
		}
		.btn, .btn-default, .btn-default.focus, .btn-default:focus, .btn.focus, .btn:focus {
		    background-color: #ABBAC3 !important;
		    border-color: #ABBAC3;
		}
		.btn-group-xs>.btn, .btn-xs {
		    padding-top: 3px;
		    padding-bottom: 3px;
		    border-width: 3px;
		}
		.btn {
		    color: #FFF !important;
		    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);
		    background-image: none !important;
		    border: 5px solid #FFF;
		    border-radius: 0;
		    box-shadow: none !important;
		    -webkit-transition: background-color .15s, border-color .15s, opacity .15s;
		    -o-transition: background-color .15s,border-color .15s,opacity .15s;
		    transition: background-color .15s, border-color .15s, opacity .15s;
		    vertical-align: middle;
		    margin: 0;
		    position: relative;
		    font-weight: 400;
		}
		.breadcrumb, .breadcrumb>li>a, .btn {
		    display: inline-block;
		}
		.btn, .dropdown-colorpicker a {
		    cursor: pointer;
		}
		.btn-group-xs>.btn, .btn-xs {
		    padding: 1px 5px;
		    font-size: 12px;
		    line-height: 1.3;
		    border-radius: 3px;
		}
		.btn, .btn-danger.active, .btn-danger:active, .btn-default.active, .btn-default:active, .btn-info.active, .btn-info:active, .btn-primary.active, .btn-primary:active, .btn-warning.active, .btn-warning:active, .btn.active, .btn:active, .dropdown-menu>.disabled>a:focus, .dropdown-menu>.disabled>a:hover, .form-control, .navbar-toggle, .open>.dropdown-toggle.btn-danger, .open>.dropdown-toggle.btn-default, .open>.dropdown-toggle.btn-info, .open>.dropdown-toggle.btn-primary, .open>.dropdown-toggle.btn-warning {
		    background-image: none;
		}
		button, input, select, textarea {
		    font-family: inherit;
		    font-size: inherit;
		    line-height: inherit;
		}
		button, html input[type=button], input[type=reset], input[type=submit] {
		    -webkit-appearance: button;
		    cursor: pointer;
		}
		button, select {
		    text-transform: none;
		}
		button {
		    overflow: visible;
		}
		button, input, optgroup, select, textarea {
		    color: inherit;
		    font: inherit;
		    margin: 0;
		}
		.btn-info, .btn-info.focus, .btn-info:focus {
		    background-color: #6FB3E0 !important;
		    border-color: #6FB3E0;
		}
		.btn-success, .btn-success.focus, .btn-success:focus {
		    background-color: #87B87F !important;
		    border-color: #87B87F;
		}
	</style>
	<script src="/js/jQuery/jquery-3.4.1.min.js"></script>
	<script type="text/javascript" src="/assets/layer/layer.js"></script>
	<script type="text/javascript">
		function Urp() {
		};
		
		Urp.prototype = {
			"alert": function (msg, callback) {
				layer.msg(msg);
				if (typeof callback === 'function') {
					setTimeout(callback, 1000);
				}
			}
		};
		urp = new Urp();
		
		function downfile(tzid){
			var fm=document.getElementById("frm");
			fm.action="/student/courseSelect/courseSelectNotice/downloadFile/"+tzid;
			fm.submit();
		}
		
		var question = eval('${question}');
		$(function() {
			var tmArea = $("#tmArea");
			if(tmArea) {
				var cont = "";
				$(question).each(function(i, e) {
					cont += "<div>";
					cont += "<p style='font-size: 18px; margin-bottom: 5px;'>\
								<input type='hidden' id='"+ e[0] +"'/>\
								"+ (i+1) +"、"+ e[1] +"（"+ (e[2]=="1" ? "单选" : "多选") +"）\
							</p><p>";
					var tmxx = e[3];
					$(tmxx.split(",")).each(function(j, v) {
						var va = v.split(":");
						cont += "<label>";
						cont += "<input type='"+ (e[2]=="1" ? "radio" : "checkbox") +"' name='"+ e[0] +"' value='"+ va[0] +"' class='ace'/>\
								<span class='lbl'>"+ va[0] + ". " + va[1] +"</span>";
						cont += "</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
					});
					cont += "</p></div>";
				});
				$("#tmArea").html(cont);
			}
		});
		
		function gotoSelectCourse() {
			var info = "";
			<c:if test="${qzdt == '1' && hasPass == '0'}">
				var ques = $("#tmArea input:hidden");
				var allR = true;
				$(ques).each(function(i, e) {
					var id = $(e).attr("id");
					info += info=="" ? "" : ",";
					info += id + "@";
					var so = $("input[name="+ id +"]:checked");
					if(so.length == 0) {
						urp.alert("还有未答的题目哦~");
						allR = false;
						return false;
					}
					$(so).each(function(j, v) {
						info += v.value;
					});
				});
				if(!allR) {
					return;
				}
				
				var index;
		        $.ajax({
		            url: "/student/courseSelect/viewSelectCoursePaper/checkDa",
		            cache: false,
		            type: "post",
		            data: {info: info},
		            dataType: "json",
		            beforeSend: function () {
		                index = layer.load(0, {
		                    shade: [0.2, "#000"]
		                });
		            },
		            success: function (d) {
		            	if(d.result == "ok") {
		            		window.location.href = "/student/courseSelect/gotoSelect/index?mobile=${mobile}";
		            	} else {
		            		urp.alert(d.result);
		            	}
		            },
		            error: function (xhr) {
		                layer.close(index);
		                urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
		            },
		            complete: function () {
		                layer.close(index);
		            }
		        });
			</c:if>
			<c:if test="${qzdt == '0' || hasPass == '1'}">
				window.location.href = "/student/courseSelect/gotoSelect/index?mobile=${mobile}";
			</c:if>
		}
	</script>
	<style>
		.content_wrap { width:initial; margin: 20px 20px 0;}
		.content_wrap h1 { color:#28a8f1; text-align:center; font-size:22px; font-family:"宋体"; }
		.content_wrap h2 { color:#858585; text-align:right; font-size:12px; font-family:"宋体"; margin:20px 0px; font-weight:normal; }
		.content_wrap h3 { color:#ababab; font-size:12px; font-family:"宋体"; margin:20px 0px 0px; font-weight:normal; }
		.content_wrap p { color:#414141; line-height:27px; font-size:14px; margin:25px 0px; text-indent:2em; }
	</style>
</head>
<body>
	<c:if test="${mobile}">
		<h5 class="phone-header smaller lighter grey" style="height: 38px; line-height: 38px; margin: -10px; top: -10px; position: relative;">
			<span class="ace-icon fa fa-angle-left bigger-130 phone-header-left" style="font-weight: bold; font-size: 20px !important; top: auto;" onclick="parent.closeFrame()">✕</span>
			<span class="phone-header-center">选课</span>
		</h5>
	</c:if>
	<div class="row" style="overflow: AUTO; height: calc(100vh - 65px);">
        <div class="<c:if test='${qzdt == \"0\" || hasPass == \"1\"}'>col-md-12</c:if><c:if test='${qzdt == \"1\" && hasPass == \"0\"}'>col-md-8</c:if> self-margin">
			<div>
				<h4 class="header smaller lighter grey" >
					<i class="ace-icon fa fa-bullhorn"></i> ⏰选课公告
					<c:if test='${qzdt == \"1\" && hasPass == \"0\"}'>
						<span class="right_top_oper" style="color: green;">
							<i class="fa fa-arrow-up"></i> 上滑答题
						</span>
					</c:if>
					<c:if test='${qzdt == \"0\" || hasPass == \"1\"}'>
						<span class="right_top_oper">
							<button type="button" class="btn btn-xs btn-info btn-round"
								onclick="gotoSelectCourse(); return false;">
								<i class="fa fa-arrow-right"></i> 继续选课
							</button>
						</span>
					</c:if>
				</h4>
			</div>
			<div class="content_wrap">
				<form id="frm" name="frm" action="" method="get">
					<h1>${xxfbTzView.title }</h1>
		            <h2>发布时间：${xxfbTzView.fbrq }&nbsp;起止日期：${xxfbTzView.ksrq }/${xxfbTzView.jzrq }</h2>
		            <div style='overflow: auto; height: calc(100vh - 250px); border-top: 1px solid #e4e4e4;'>
			            <h3>${xxfbTzView.tznr }</h3>
			            <h3>附件：</h3>
			            <p><a onclick="downfile('${xxfbTzView.tzid }')">${xxfbTzView.fjmc }</a></p>
			            <div style='clear: both;'></div>
		            </div>
				</form>
			</div>
		</div>
		<c:if test='${qzdt == \"1\" && hasPass == \"0\"}'>
			<div class="col-md-4" style="padding-left: 0;">
				<h4 class="header smaller lighter grey" style="margin-top: 6px;">
					<i class="ace-icon fa fa-question-circle"></i> 📄公告题目
					<span class="right_top_oper">
						<button type="button" class="btn btn-xs btn-info btn-round"
							onclick="gotoSelectCourse(); return false;">
							<i class="fa fa-arrow-right"></i> 继续选课
						</button>
					</span>
				</h4>
				<div style="overflow: auto; height: calc(100vh - 170px);" id="tmArea">
				</div>
			</div>
			
		</c:if>
	</div>
</body>
</html>



