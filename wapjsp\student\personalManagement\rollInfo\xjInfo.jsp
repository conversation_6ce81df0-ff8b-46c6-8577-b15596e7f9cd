<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="data" uri="http://www.urpSoft.com/data" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学籍信息</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学籍信息页面样式 */
        .student-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .student-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .student-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .info-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .info-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .info-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-section-title i {
            color: var(--primary-color);
        }
        
        .info-content {
            padding: var(--padding-md);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: var(--padding-sm) 0;
            border-bottom: 1px solid var(--divider-color);
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
            min-width: 80px;
            margin-right: var(--margin-sm);
        }
        
        .info-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            flex: 1;
            text-align: right;
            word-break: break-word;
        }
        
        .plan-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .plan-selector-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .plan-selector-title i {
            color: var(--success-color);
        }
        
        .plan-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }
        
        .btn-plan {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }
        
        .btn-plan:active {
            transform: scale(0.98);
        }
        
        .btn-plan.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .btn-plan.success {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }
        
        .btn-plan i {
            font-size: 24px;
        }
        
        .plan-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            overflow-y: auto;
        }
        
        .plan-detail-content {
            background: var(--bg-primary);
            margin: 20px;
            border-radius: 8px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        
        .plan-detail-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1001;
        }
        
        .plan-detail-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .btn-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
        }
        
        .plan-detail-body {
            padding: var(--padding-md);
        }
        
        .tree-container {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-md);
            max-height: 400px;
            overflow-y: auto;
        }
        
        .tree-node {
            padding: var(--padding-sm);
            margin: 2px 0;
            border-radius: 4px;
            cursor: pointer;
            font-size: var(--font-size-small);
            color: var(--text-primary);
            transition: all var(--transition-base);
        }
        
        .tree-node:hover {
            background: var(--primary-light);
        }
        
        .tree-node.selected {
            background: var(--primary-color);
            color: white;
        }
        
        .tree-node.level-1 {
            margin-left: 0;
            font-weight: 500;
        }
        
        .tree-node.level-2 {
            margin-left: 20px;
        }
        
        .tree-node.level-3 {
            margin-left: 40px;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        @media (max-width: 480px) {
            .student-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .info-section,
            .plan-selector {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .plan-buttons {
                grid-template-columns: 1fr;
            }
            
            .plan-detail-content {
                margin: 10px;
            }
            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .info-value {
                text-align: left;
                margin-top: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学籍信息</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 学籍信息头部 -->
        <div class="student-header">
            <div class="student-title">学籍信息</div>
            <div class="student-desc">查看个人学籍详细信息</div>
        </div>
        
        <!-- 基本信息 -->
        <div class="info-section">
            <div class="info-section-header">
                <div class="info-section-title">
                    <i class="ace-icon fa fa-user"></i>
                    基本信息
                </div>
            </div>
            <div class="info-content">
                <div class="info-grid" id="basicInfo">
                    <!-- 动态加载基本信息 -->
                </div>
            </div>
        </div>
        
        <!-- 培养方案选择 -->
        <div class="plan-selector">
            <div class="plan-selector-title">
                <i class="ace-icon fa fa-graduation-cap"></i>
                培养方案
            </div>
            <div class="plan-buttons">
                <button class="btn-plan primary" onclick="viewPlan('zx');">
                    <i class="ace-icon fa fa-book"></i>
                    <span>主修方案</span>
                </button>
                <button class="btn-plan success" onclick="viewPlan('fx');">
                    <i class="ace-icon fa fa-plus-square"></i>
                    <span>辅修方案</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-user"></i>
            <div>暂无学籍信息</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>
    
    <!-- 培养方案详情模态框 -->
    <div class="plan-detail-modal" id="planDetailModal">
        <div class="plan-detail-content">
            <div class="plan-detail-header">
                <div class="plan-detail-title" id="planDetailTitle">培养方案详情</div>
                <button class="btn-close" onclick="closePlanDetail();">
                    <i class="ace-icon fa fa-times"></i>
                </button>
            </div>
            <div class="plan-detail-body">
                <div id="planDetailContent">
                    <!-- 动态加载方案详情 -->
                </div>
                <div class="tree-container" id="planTreeContainer" style="display: none;">
                    <div id="planTree">
                        <!-- 动态加载方案树 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let studentInfo = null;
        let currentPlanType = '';
        let planData = null;

        $(function() {
            initPage();
            loadStudentInfo();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载学生信息
        function loadStudentInfo() {
            showLoading(true);

            $.ajax({
                url: "/student/rollManagement/rollInfo/index",
                type: "get",
                dataType: "json",
                success: function(response) {
                    if (response && response.data) {
                        studentInfo = response.data;
                        renderBasicInfo();
                        showEmptyState(false);
                    } else {
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    showEmptyState(true);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染基本信息
        function renderBasicInfo() {
            if (!studentInfo) return;

            const container = $('#basicInfo');
            container.empty();

            // 基本信息字段映射
            const fields = [
                { label: '学号', key: 'xh' },
                { label: '姓名', key: 'xm' },
                { label: '性别', key: 'xbm' },
                { label: '出生日期', key: 'csrq' },
                { label: '民族', key: 'mzm' },
                { label: '政治面貌', key: 'zzmm' },
                { label: '身份证号', key: 'sfzh' },
                { label: '入学日期', key: 'rxrq' },
                { label: '学院', key: 'xsm' },
                { label: '专业', key: 'zym' },
                { label: '班级', key: 'bjm' },
                { label: '年级', key: 'njm' },
                { label: '学制', key: 'xz' },
                { label: '学习形式', key: 'xxfsm' },
                { label: '学生类别', key: 'xslbm' },
                { label: '学籍状态', key: 'xjztm' },
                { label: '在校状态', key: 'zxztm' }
            ];

            fields.forEach(function(field) {
                const value = studentInfo[field.key] || '';
                const itemHtml = `
                    <div class="info-item">
                        <span class="info-label">${field.label}</span>
                        <span class="info-value">${value}</span>
                    </div>
                `;
                container.append(itemHtml);
            });
        }

        // 查看培养方案
        function viewPlan(planType) {
            currentPlanType = planType;
            const planName = planType === 'zx' ? '主修方案' : '辅修方案';

            $('#planDetailTitle').text(planName);
            $('#planDetailModal').show();

            loadPlanData(planType);
        }

        // 加载培养方案数据
        function loadPlanData(planType) {
            showPlanLoading(true);

            const fajhh = planType === 'zx' ? $('#zx').val() : $('#fx').val();
            const url = "/student/rollManagement/project/" + fajhh + "/1/detail";

            $.ajax({
                url: url,
                type: "get",
                dataType: "json",
                success: function(response) {
                    if (response && response.treeList) {
                        planData = response;
                        renderPlanDetail(response);
                        renderPlanTree(response.treeList);
                    } else {
                        showPlanError("没有查询到培养方案信息");
                    }
                },
                error: function(xhr) {
                    showPlanError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取方案数据失败！");
                },
                complete: function() {
                    showPlanLoading(false);
                }
            });
        }

        // 渲染培养方案详情
        function renderPlanDetail(data) {
            if (!data.jhFajhb) return;

            const jhFajhb = data.jhFajhb;
            const container = $('#planDetailContent');

            const detailHtml = `
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">方案名称</span>
                        <span class="info-value">${jhFajhb.famc || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">计划名称</span>
                        <span class="info-value">${jhFajhb.jhmc || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">年级</span>
                        <span class="info-value">${jhFajhb.njmc || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">院系</span>
                        <span class="info-value">${jhFajhb.xsm || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">专业</span>
                        <span class="info-value">${jhFajhb.zym || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">专业方向</span>
                        <span class="info-value">${jhFajhb.zyfxm || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">学位</span>
                        <span class="info-value">${jhFajhb.xwm || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">毕业类型</span>
                        <span class="info-value">${jhFajhb.bylxmc || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">学制类型</span>
                        <span class="info-value">${jhFajhb.xzlxmc || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">修读类型</span>
                        <span class="info-value">${jhFajhb.xdlxmc || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">要求总学分</span>
                        <span class="info-value">${jhFajhb.yqzxf || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">课程总学分</span>
                        <span class="info-value">${jhFajhb.kczxf || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">课程总门数</span>
                        <span class="info-value">${jhFajhb.kczms || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">课程总学时</span>
                        <span class="info-value">${jhFajhb.kczxs || ''}</span>
                    </div>
                </div>
            `;

            container.html(detailHtml);
        }

        // 渲染培养方案树
        function renderPlanTree(treeList) {
            if (!treeList || treeList.length === 0) return;

            const container = $('#planTree');
            container.empty();

            treeList.forEach(function(node) {
                const nodeHtml = createTreeNode(node);
                container.append(nodeHtml);
            });

            $('#planTreeContainer').show();
        }

        // 创建树节点
        function createTreeNode(node) {
            const level = node.level || 1;
            const className = `tree-node level-${level}`;

            return `
                <div class="${className}" data-id="${node.id}" onclick="selectTreeNode('${node.id}', this);">
                    ${node.name || ''}
                </div>
            `;
        }

        // 选择树节点
        function selectTreeNode(nodeId, element) {
            $('.tree-node').removeClass('selected');
            $(element).addClass('selected');

            // 这里可以加载节点详情
            loadNodeDetail(nodeId);
        }

        // 加载节点详情
        function loadNodeDetail(nodeId) {
            // 根据节点ID加载详细信息
            // 这里可以调用相应的API获取课程、课组等详细信息
        }

        // 关闭培养方案详情
        function closePlanDetail() {
            $('#planDetailModal').hide();
            $('#planTreeContainer').hide();
            $('#planDetailContent').empty();
            $('#planTree').empty();
        }

        // 显示方案加载状态
        function showPlanLoading(show) {
            if (show) {
                $('#planDetailContent').html('<div class="loading-container"><i class="ace-icon fa fa-spinner fa-spin"></i><span>加载中...</span></div>');
            }
        }

        // 显示方案错误
        function showPlanError(message) {
            $('#planDetailContent').html(`<div class="empty-state"><i class="ace-icon fa fa-exclamation-triangle"></i><div>${message}</div></div>`);
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.info-section, .plan-selector').hide();
            } else {
                $('#emptyState').hide();
                $('.info-section, .plan-selector').show();
            }
        }

        // 刷新数据
        function refreshData() {
            loadStudentInfo();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#planDetailModal').click(function(e) {
            if (e.target === this) {
                closePlanDetail();
            }
        });
    </script>
</body>
</html>
