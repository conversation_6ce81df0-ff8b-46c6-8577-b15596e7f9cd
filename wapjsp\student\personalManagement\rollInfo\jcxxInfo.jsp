<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>奖惩信息</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 奖惩信息页面样式 */
        .rewards-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .rewards-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .rewards-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .rewards-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .rewards-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .rewards-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .rewards-section-title i {
            color: var(--success-color);
        }
        
        .reward-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .reward-item:last-child {
            border-bottom: none;
        }
        
        .reward-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .reward-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .reward-content {
            flex: 1;
        }
        
        .reward-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .reward-date {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .reward-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            align-items: flex-start;
        }
        
        .detail-label {
            font-weight: 500;
            min-width: 80px;
            margin-right: var(--margin-sm);
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
            word-break: break-word;
        }
        
        .reward-reason {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
        }
        
        .reason-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .reason-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-reward {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-punishment {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .reward-note {
            background: var(--info-light);
            color: var(--info-dark);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        @media (max-width: 480px) {
            .rewards-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .rewards-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .detail-value {
                text-align: left;
                margin-top: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">奖惩信息</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 奖惩信息头部 -->
        <div class="rewards-header">
            <div class="rewards-title">奖惩信息</div>
            <div class="rewards-desc">查看个人奖励和惩罚记录</div>
        </div>
        
        <!-- 奖惩信息列表 -->
        <c:if test="${!empty jcxxList}">
            <div class="rewards-section">
                <div class="rewards-section-header">
                    <div class="rewards-section-title">
                        <i class="ace-icon fa fa-trophy"></i>
                        奖惩记录列表
                    </div>
                </div>
                
                <c:forEach var="item" items="${jcxxList}" varStatus="status">
                    <div class="reward-item">
                        <div class="reward-header-info">
                            <div style="display: flex; align-items: flex-start;">
                                <div class="reward-index">${status.index + 1}</div>
                                <div class="reward-content">
                                    <div class="reward-type">${item.codeJclb.jclb}</div>
                                    <div class="reward-date">日期：${item.xsjcbPk.jcrq}</div>
                                </div>
                            </div>
                            <div>
                                <c:choose>
                                    <c:when test="${item.jhc == '奖'}">
                                        <span class="status-badge status-reward">奖励</span>
                                    </c:when>
                                    <c:otherwise>
                                        <span class="status-badge status-punishment">惩罚</span>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </div>

                        <div class="reward-details">
                            <div class="detail-item">
                                <span class="detail-label">学号</span>
                                <span class="detail-value">${item.xsjcbPk.xh}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">奖惩类别</span>
                                <span class="detail-value">${item.codeJclb.jclb}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">奖或惩</span>
                                <span class="detail-value">${item.jhc}</span>
                            </div>
                        </div>

                        <c:if test="${!empty item.jcyy}">
                            <div class="reward-reason">
                                <div class="reason-title">奖惩原因</div>
                                <div class="reason-content">${item.jcyy}</div>
                            </div>
                        </c:if>

                        <c:if test="${!empty item.bz}">
                            <div class="reward-note">
                                <strong>备注：</strong>${item.bz}
                            </div>
                        </c:if>
                    </div>
                </c:forEach>
            </div>
        </c:if>
        
        <!-- 空状态 -->
        <c:if test="${empty jcxxList}">
            <div class="empty-state">
                <i class="ace-icon fa fa-trophy"></i>
                <div class="empty-state-title">暂无奖惩记录</div>
                <div class="empty-state-desc">您目前还没有任何奖励或惩罚记录</div>
            </div>
        </c:if>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
