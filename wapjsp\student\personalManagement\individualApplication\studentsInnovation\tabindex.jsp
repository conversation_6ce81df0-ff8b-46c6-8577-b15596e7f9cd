<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生创新项目</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生创新项目页面样式 */
        .students-innovation-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .students-innovation-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .students-innovation-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .time-info-section {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--info-dark);
            font-size: var(--font-size-small);
            line-height: 1.5;
        }
        
        .category-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .tabs-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tabs-title i {
            color: var(--success-color);
        }
        
        .tabs-nav {
            display: flex;
            overflow-x: auto;
            padding: var(--padding-sm);
            gap: var(--spacing-sm);
        }
        
        .tab-item {
            flex-shrink: 0;
            padding: var(--padding-sm) var(--padding-md);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            border: 1px solid var(--divider-color);
            background: var(--bg-primary);
            color: var(--text-secondary);
        }
        
        .tab-item.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .action-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-add-application {
            width: 100%;
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-add-application:hover {
            background: var(--success-dark);
        }
        
        .btn-add-application:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .projects-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .projects-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .projects-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .projects-section-title i {
            color: var(--success-color);
        }
        
        .project-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .project-item:last-child {
            border-bottom: none;
        }
        
        .project-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .project-index {
            background: var(--success-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .project-content {
            flex: 1;
        }
        
        .project-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
            cursor: pointer;
        }
        
        .project-name:hover {
            color: var(--primary-color);
        }
        
        .project-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            align-items: flex-start;
        }
        
        .detail-label {
            font-weight: 500;
            min-width: 60px;
            margin-right: var(--margin-sm);
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
            word-break: break-word;
        }
        
        .approval-status {
            display: flex;
            gap: var(--spacing-xs);
            margin-bottom: var(--margin-sm);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .project-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-project-action {
            flex: 1;
            min-width: 80px;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-download {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-change {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-upload {
            background: var(--success-color);
            color: white;
        }
        
        .btn-print {
            background: var(--text-secondary);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .students-innovation-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .time-info-section,
            .category-tabs,
            .action-section,
            .projects-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .project-details {
                grid-template-columns: 1fr;
            }
            
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .detail-value {
                text-align: left;
                margin-top: 4px;
            }
            
            .project-actions {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    <input type="hidden" id="xmlxm" name="xmlxm" value="${xmlxm}"/>
    <input type="hidden" id="xmlx" name="xmlx" value="${xmlx}"/>
    <input type="hidden" id="xmlybsid" name="xmlybsid" value="${xmlybsid}"/>
    <input type="hidden" id="xmlybs" name="xmlybs" value="${xmlybs}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生创新项目</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 学生创新项目头部 -->
        <div class="students-innovation-header">
            <div class="students-innovation-title">申请${xmlybs}</div>
            <div class="students-innovation-desc">管理您的创新创业训练计划项目</div>
        </div>
        
        <!-- 申请时间信息 -->
        <div class="time-info-section" id="xmlxtimemessage">
            <!-- 动态显示申请时间信息 -->
        </div>
        
        <!-- 项目类型选择 -->
        <div class="category-tabs">
            <div class="tabs-header">
                <div class="tabs-title">
                    <i class="ace-icon fa fa-tags"></i>
                    项目类型
                </div>
            </div>
            <div class="tabs-nav" id="categoryTabs">
                <cache:query var="xmlxblist" region="chx_code_chxxmlxb" orderby="xmlxm"/>
                <c:forEach items="${xmlxblist}" var="xmlxvo" varStatus="status">
                    <div class="tab-item ${xmlxm == xmlxvo.xmlxm ? 'active' : ''}" 
                         onclick="clickTab('${xmlxvo.xmlxm}', '${xmlxvo.xmlx}')">
                        ${xmlxvo.xmlx}
                    </div>
                </c:forEach>
            </div>
        </div>
        
        <!-- 添加申请按钮 -->
        <div class="action-section" id="actionSection" style="display: none;">
            <button class="btn-add-application" onclick="toMaintenanceInnovation('', 'add', '');">
                <i class="ace-icon fa fa-plus"></i>
                <span>申请项目</span>
            </button>
        </div>
        
        <!-- 项目列表 -->
        <div class="projects-section">
            <div class="projects-section-header">
                <div class="projects-section-title">
                    <i class="ace-icon fa fa-list"></i>
                    我的项目列表
                </div>
            </div>
            
            <div id="projectsList">
                <!-- 动态加载项目列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreProjects();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-lightbulb-o"></i>
            <div class="empty-state-title">暂无项目记录</div>
            <div class="empty-state-desc">您还没有申请任何创新创业训练计划项目</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let projectData = [];
        let getApplyListDate = "";

        $(function() {
            initPage();
            initClickTab();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化跳转
        function initClickTab() {
            var xmlxm = $("#xmlxm").val();
            var xmlx = $("#xmlx").val();
            clickTab(xmlxm, xmlx);
        }

        // 点击标签页
        function clickTab(xmlxm, xmlx) {
            $("#xmlxm").val(xmlxm);
            $("#xmlx").val(xmlx);

            // 更新标签页状态
            $('.tab-item').removeClass('active');
            $('.tab-item').each(function() {
                if ($(this).text().trim() === xmlx) {
                    $(this).addClass('active');
                }
            });

            validateXmsqKg(xmlxm);
            getApplyList(1, "30_sl", true);
        }

        // 验证项目申请的开关
        function validateXmsqKg(xmlxm) {
            showLoading(true);

            $.ajax({
                url: "/students/studentsInnovation/apply/getXmsqKg",
                cache: false,
                type: "get",
                data: {"xmlxm": xmlxm},
                dataType: "json",
                async: false,
                success: function(response) {
                    const sqkg = response[0].sqkg;
                    if ("关" == sqkg) {
                        $("#actionSection").hide();
                    } else {
                        $("#actionSection").show();
                    }

                    const xmkgkzb = JSON.parse(response[1].xmkgkzb);
                    if (xmkgkzb != null) {
                        let start = xmkgkzb.kssjstr;
                        let end = xmkgkzb.jssjstr;
                        if ((start != null && start != "") && (end != null && end != "")) {
                            $("#xmlxtimemessage").html("立项申请时间：" + start + "~" + end);
                            $("#xmlxtimemessage").show();
                        } else {
                            $("#xmlxtimemessage").hide();
                        }
                    } else {
                        $("#xmlxtimemessage").hide();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 方案信息查询表格
        function getApplyList(page, pageSizeVal, conditionChanged) {
            if (pageSizeVal == undefined) {
                pageSizeVal = "30_sl";
                page = "1";
            }

            var parr = (pageSizeVal + "").split("_");
            var pageSize = parseInt(parr[0]);

            if (conditionChanged) {
                getApplyListDate = "xmlxm=" + $("#xmlxm").val() + "&xmlx=" + $("#xmlx").val() +
                                  "&xmlybsid=" + $("#xmlybsid").val() + "&xmlybs=" + $("#xmlybs").val();
            }

            showLoading(true);

            $.ajax({
                url: "/students/studentsInnovation/apply/selfapplypage",
                cache: false,
                type: "post",
                data: getApplyListDate + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records) {
                        projectData = data.records;
                        totalCount = data.pageContext ? data.pageContext.totalCount : 0;

                        if (projectData.length > 0) {
                            renderProjects();
                        } else {
                            showEmptyState();
                        }
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染项目列表
        function renderProjects() {
            const container = $('#projectsList');
            container.empty();

            if (projectData.length === 0) {
                showEmptyState();
                return;
            }

            projectData.forEach(function(project, index) {
                const projectHtml = createProjectItem(project, index);
                container.append(projectHtml);
            });

            hideEmptyState();
        }

        // 创建项目项目HTML
        function createProjectItem(project, index) {
            const jssp = project.JSSPJL || "";
            const yxsp = project.YXSPJL || "";
            const jwcsp = project.JWCSPJL || "";
            const editButton = (jssp == yxsp && jwcsp == yxsp && yxsp == "待审批");
            const spflag = ("通过" == jssp && "通过" == yxsp && "通过" == jwcsp);

            return `
                <div class="project-item">
                    <div class="project-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="project-index">${index + 1}</div>
                            <div class="project-content">
                                <div class="project-name" onclick="showInfoDetail('${project.ID}', '${project.XMMC}');">
                                    ${project.XMMC || ''}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="project-details">
                        <div class="detail-item">
                            <span class="detail-label">年度</span>
                            <span class="detail-value">${project.SQNF || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">学院</span>
                            <span class="detail-value">${project.XSM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">专业</span>
                            <span class="detail-value">${project.ZYM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">指导教师</span>
                            <span class="detail-value">${project.ZDJSM || ''}</span>
                        </div>
                    </div>

                    <div class="approval-status">
                        <div class="status-badge ${getStatusClass(jssp)}">${getStatusText(jssp, '教师')}</div>
                        <div class="status-badge ${getStatusClass(yxsp)}">${getStatusText(yxsp, '院系')}</div>
                        <div class="status-badge ${getStatusClass(jwcsp)}">${getStatusText(jwcsp, '教务处')}</div>
                    </div>

                    <div class="project-actions">
                        ${getProjectActionButtons(project, editButton, spflag)}
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case "通过":
                    return "status-approved";
                case "不通过":
                    return "status-rejected";
                default:
                    return "status-pending";
            }
        }

        // 获取状态文本
        function getStatusText(status, prefix) {
            return prefix + ":" + (status || "待审批");
        }

        // 获取项目操作按钮
        function getProjectActionButtons(project, editButton, spflag) {
            let buttons = [];

            // 下载申请书
            buttons.push(`
                <button class="btn-project-action btn-download" onclick="downSqFile('${project.ID}');">
                    <i class="ace-icon fa fa-download"></i>
                    <span>下载</span>
                </button>
            `);

            // 修改和删除按钮
            if (editButton) {
                buttons.push(`
                    <button class="btn-project-action btn-edit" onclick="toMaintenanceInnovation('${project.ID}', 'edit', '${project.SQNF || ''}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                `);
                buttons.push(`
                    <button class="btn-project-action btn-delete" onclick="deleteMaintenanceInnovation('${project.ID}');">
                        <i class="ace-icon fa fa-trash"></i>
                        <span>删除</span>
                    </button>
                `);
            }

            // 负责人变更
            buttons.push(`
                <button class="btn-project-action btn-change" onclick="syncStudentInfo('${project.ID}', '${project.XH}', '${project.XM}', ${spflag});">
                    <i class="ace-icon fa fa-exchange"></i>
                    <span>变更</span>
                </button>
            `);

            // 打印证明
            if (project.JCJLM == "01") {
                buttons.push(`
                    <button class="btn-project-action btn-print" onclick="doPrint('${project.ID}');">
                        <i class="ace-icon fa fa-print"></i>
                        <span>打印</span>
                    </button>
                `);
            }

            return buttons.join('');
        }

        // 下载申请书
        function downSqFile(sqid) {
            const name = encodeURIComponent("/students/studentsInnovation/apply/xmsqExportApplyFile?sqid=" + sqid);
            const url = "/pdf/web/viewer.html?file=" + name;

            if (parent && parent.addTab) {
                parent.addTab('项目申请书', url);
            } else {
                window.open(url, '_blank');
            }
        }

        // 查看项目详情
        function showInfoDetail(id, xmmc) {
            const url = "/students/studentsInnovation/apply/detail?id=" + id;

            if (parent && parent.addTab) {
                parent.addTab('项目详情', url);
            } else {
                window.location.href = url;
            }
        }

        // 维护创新项目
        function toMaintenanceInnovation(id, type, sqnf) {
            const url = "/students/studentsInnovation/apply/edit?id=" + id + "&type=" + type + "&sqnf=" + sqnf;

            if (parent && parent.addTab) {
                parent.addTab(type === 'add' ? '新增项目' : '修改项目', url);
            } else {
                window.location.href = url;
            }
        }

        // 删除项目
        function deleteMaintenanceInnovation(id) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm("确定要删除该项目？", function(confirmed) {
                    if (confirmed) {
                        doDeleteProject(id);
                    }
                });
            } else {
                if (confirm("确定要删除该项目？")) {
                    doDeleteProject(id);
                }
            }
        }

        // 执行删除项目
        function doDeleteProject(id) {
            showLoading(true);

            $.ajax({
                url: "/students/studentsInnovation/apply/delete",
                type: "post",
                data: "id=" + id + "&tokenValue=" + $("#tokenValue").val(),
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    $("#tokenValue").val(data.token);
                    if (data.result === "success") {
                        showSuccess("删除成功！");
                        getApplyList(1, "30_sl", true);
                    } else {
                        showError(data.result);
                    }
                },
                error: function() {
                    showError("删除失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 同步学生信息（负责人变更）
        function syncStudentInfo(id, xh, xm, spflag) {
            const url = "/students/studentsInnovation/apply/changeLeader?id=" + id + "&xh=" + xh + "&xm=" + xm + "&spflag=" + spflag;

            if (parent && parent.addTab) {
                parent.addTab('负责人变更', url);
            } else {
                window.location.href = url;
            }
        }

        // 打印证明
        function doPrint(id) {
            const url = "/students/studentsInnovation/apply/print?id=" + id;

            if (parent && parent.addTab) {
                parent.addTab('打印证明', url);
            } else {
                window.open(url, '_blank');
            }
        }

        // 显示空状态
        function showEmptyState() {
            $('#emptyState').show();
            $('#loadMoreContainer').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 返回首页
        function returnIndex() {
            location.href = "/student/application/index";
        }

        // 刷新数据
        function refreshData() {
            getApplyList(1, "30_sl", true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
