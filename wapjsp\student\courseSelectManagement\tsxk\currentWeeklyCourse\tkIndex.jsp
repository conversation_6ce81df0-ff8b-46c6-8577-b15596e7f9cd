<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>退课</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 退课页面样式 */
        .quit-course-header {
            background: linear-gradient(135deg, var(--error-color), var(--warning-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }
        
        .quit-course-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .quit-course-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .course-list-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .list-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .list-title i {
            color: var(--error-color);
        }
        
        .course-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            background: var(--bg-primary);
        }
        
        .course-item:last-child {
            border-bottom: none;
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-info {
            flex: 1;
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .course-code {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .course-teacher {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .quit-button {
            background: var(--error-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
            min-width: 60px;
            justify-content: center;
        }
        
        .quit-button:hover {
            background: var(--error-dark);
        }
        
        .quit-button:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            background: var(--bg-secondary);
            padding: var(--padding-sm);
            border-radius: 4px;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: 2px;
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .time-place-section {
            margin-top: var(--margin-sm);
        }
        
        .time-place-title {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .time-place-item {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 4px;
            padding: var(--padding-xs);
            margin-bottom: var(--margin-xs);
            font-size: var(--font-size-small);
            color: var(--info-dark);
        }
        
        .time-place-item:last-child {
            margin-bottom: 0;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .warning-notice {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
        }
        
        .warning-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .warning-title i {
            color: var(--warning-color);
        }
        
        .warning-list {
            font-size: var(--font-size-small);
            line-height: 1.6;
        }
        
        .warning-list li {
            margin-bottom: 4px;
        }
        
        @media (max-width: 480px) {
            .quit-course-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .course-list-section,
            .warning-notice {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .course-details {
                grid-template-columns: 1fr;
            }
            
            .course-header {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-sm);
            }
            
            .quit-button {
                align-self: flex-end;
                min-width: 80px;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" value="${token}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">退课</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 退课头部 -->
        <div class="quit-course-header">
            <div class="quit-course-title">退课管理</div>
            <div class="quit-course-desc">管理您已选择的课程</div>
        </div>
        
        <!-- 已选课程列表 -->
        <div class="course-list-section">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    已选课程
                </div>
            </div>
            
            <div id="courseList">
                <c:choose>
                    <c:when test="${not empty xsxkHash}">
                        <c:forEach var="xsxk" items="${xsxkHash}">
                            <div class="course-item">
                                <div class="course-header">
                                    <div class="course-info">
                                        <div class="course-name">${xsxk.value.courseName}
                                            <c:if test="${not empty xsxk.value.fsktms}">_${xsxk.value.zkxh} + ${xsxk.value.fsktms}</c:if>_${xsxk.value.id.coureSequenceNumber}
                                        </div>
                                        <div class="course-code">课程号：${xsxk.value.id.coureNumber}</div>
                                        <div class="course-teacher">教师：${xsxk.value.attendClassTeacher}</div>
                                    </div>
                                    <button class="quit-button" onclick="quitCourse('${xsxk.value.programPlanNumber}', '${xsxk.value.id.coureNumber}', '${xsxk.value.id.coureSequenceNumber}');">
                                        <i class="ace-icon fa fa-trash-o"></i>
                                        <span>退课</span>
                                    </button>
                                </div>
                                
                                <div class="course-details">
                                    <div class="detail-item">
                                        <div class="detail-label">培养方案</div>
                                        <div class="detail-value">${xsxk.value.programPlanName}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">学分</div>
                                        <div class="detail-value">${xsxk.value.unit}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">课程属性</div>
                                        <div class="detail-value">${xsxk.value.coursePropertiesName}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">考试类型</div>
                                        <div class="detail-value">${xsxk.value.examTypeName}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">修读方式</div>
                                        <div class="detail-value">${xsxk.value.studyModeName}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">选课状态</div>
                                        <div class="detail-value">${xsxk.value.selectCourseStatusName}</div>
                                    </div>
                                </div>
                                
                                <c:if test="${not empty xsxk.value.timeAndPlaceList}">
                                    <div class="time-place-section">
                                        <div class="time-place-title">上课时间地点</div>
                                        <c:forEach var="sjdd" items="${xsxk.value.timeAndPlaceList}">
                                            <div class="time-place-item">
                                                ${sjdd.weekDescription} 星期${sjdd.classDay} 第${sjdd.classSessions}节(${sjdd.continuingSession}节) 
                                                ${sjdd.campusName} ${sjdd.teachingBuildingName} ${sjdd.classroomName}
                                            </div>
                                        </c:forEach>
                                    </div>
                                </c:if>
                            </div>
                        </c:forEach>
                    </c:when>
                    <c:otherwise>
                        <div class="empty-state">
                            <i class="ace-icon fa fa-calendar-times-o"></i>
                            <div class="empty-state-text">暂无已选课程</div>
                            <div class="empty-state-desc">您还没有选择任何课程</div>
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
        
        <!-- 退课注意事项 -->
        <div class="warning-notice">
            <div class="warning-title">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                退课注意事项
            </div>
            <ul class="warning-list">
                <li>请在规定时间内完成退课，逾期系统将自动关闭</li>
                <li>退课后请及时查看课表确认变更</li>
                <li>部分课程退课后可能影响学分要求，请谨慎操作</li>
                <li>如有疑问请及时联系教务处或开课院系</li>
                <li>退课操作不可撤销，请确认后再操作</li>
            </ul>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>处理中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        const zynum = '${zynum}';

        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 退课操作
        function quitCourse(fajhh, kch, kxh) {
            showConfirm("确定要退选这门课程吗？", function(confirmed) {
                if (confirmed) {
                    performQuitCourse(fajhh, kch, kxh);
                }
            });
        }

        // 执行退课操作
        function performQuitCourse(fajhh, kch, kxh) {
            showLoading(true);

            // 禁用所有退课按钮
            $('.quit-button').prop('disabled', true);

            $.ajax({
                url: "/student/courseSelect/currentWeeklyCourse/deleteOne",
                type: "post",
                data: {
                    'fajhh': fajhh,
                    'kch': kch,
                    'kxh': kxh,
                    'tokenValue': $("#tokenValue").val()
                },
                dataType: "text",
                success: function(response) {
                    if (response.indexOf("/") != -1) {
                        // 重定向
                        window.location.href = response;
                    } else if (response) {
                        // 显示消息并刷新页面
                        showAlert(response, function() {
                            window.location.href = "/student/courseSelectManagement/tsxk/currentWeeklyDropCourse/tkIndex";
                        });
                    } else {
                        // 退课成功，刷新页面
                        showAlert("退课成功！", function() {
                            window.location.reload();
                        });
                    }
                },
                error: function(xhr) {
                    showAlert("退课失败，请重试！");
                },
                complete: function() {
                    showLoading(false);
                    // 重新启用退课按钮
                    $('.quit-button').prop('disabled', false);
                }
            });
        }

        // 返回上一页
        function returnIndex() {
            history.back();
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示确认对话框
        function showConfirm(message, callback) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, callback);
            } else {
                const result = confirm(message);
                callback(result);
            }
        }

        // 显示提示信息
        function showAlert(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 处理课程项的交互效果
        $('.course-item').on('click', function(e) {
            // 如果点击的不是退课按钮，则添加选中效果
            if (!$(e.target).closest('.quit-button').length) {
                $('.course-item').removeClass('selected');
                $(this).addClass('selected');
            }
        });

        // 添加选中样式
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .course-item.selected {
                    background: var(--info-light) !important;
                    border-color: var(--info-color) !important;
                }

                .course-item {
                    cursor: pointer;
                    transition: all var(--transition-base);
                    border: 1px solid transparent;
                }

                .course-item:hover {
                    background: var(--bg-tertiary) !important;
                }
            `)
            .appendTo('head');
    </script>
</body>
</html>
