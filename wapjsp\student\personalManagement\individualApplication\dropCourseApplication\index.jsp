<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>
        <c:if test="${apply_type == '10002'}">退课申请</c:if>
        <c:if test="${apply_type == '10010'}">自学重修申请</c:if>
        <c:if test="${apply_type == '10011'}">缓考申请</c:if>
    </title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 退课申请页面样式 */
        .dropcourse-header {
            background: linear-gradient(135deg, var(--primary-color), var(--error-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .dropcourse-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .dropcourse-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .action-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-add-application {
            width: 100%;
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-add-application:hover {
            background: var(--success-dark);
        }
        
        .btn-add-application:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .applications-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .applications-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .applications-section-title i {
            color: var(--error-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-index {
            background: var(--error-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .application-content {
            flex: 1;
        }
        
        .application-number {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .application-course {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            align-items: flex-start;
        }
        
        .detail-label {
            font-weight: 500;
            min-width: 60px;
            margin-right: var(--margin-sm);
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
            word-break: break-word;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-draft {
            background: var(--text-disabled);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-processing {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-completed {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-revoked {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .approval-result {
            margin-top: var(--margin-sm);
            padding: var(--padding-sm);
            border-radius: 6px;
            font-size: var(--font-size-small);
            text-align: center;
        }
        
        .approval-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .approval-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .approval-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .approval-revoked {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .application-reason {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .application-note {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-application-action {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-revoke {
            background: var(--error-color);
            color: white;
        }
        
        .btn-download {
            background: var(--success-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .warning-alert {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .warning-alert i {
            color: var(--warning-color);
            font-size: 20px;
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .dropcourse-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .action-section,
            .applications-section,
            .warning-alert {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .application-details {
                grid-template-columns: 1fr;
            }
            
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .detail-value {
                text-align: left;
                margin-top: 4px;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">
                <c:if test="${apply_type == '10002'}">退课申请</c:if>
                <c:if test="${apply_type == '10010'}">自学重修申请</c:if>
                <c:if test="${apply_type == '10011'}">缓考申请</c:if>
            </div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 警告信息 -->
        <c:if test="${count == 0}">
            <div class="warning-alert">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>
                    <c:if test="${apply_type == '10002'}">退课申请</c:if>
                    <c:if test="${apply_type == '10010'}">自学重修申请</c:if>
                    <c:if test="${apply_type == '10011'}">缓考申请</c:if>
                    无审批流程，请联系管理员！
                </span>
            </div>
        </c:if>
        
        <c:if test="${count != 0}">
            <!-- 申请头部 -->
            <div class="dropcourse-header">
                <div class="dropcourse-title">
                    <c:if test="${apply_type == '10002'}">退课申请</c:if>
                    <c:if test="${apply_type == '10010'}">自学重修申请</c:if>
                    <c:if test="${apply_type == '10011'}">缓考申请</c:if>
                </div>
                <div class="dropcourse-desc">
                    管理您的
                    <c:if test="${apply_type == '10002'}">退课</c:if>
                    <c:if test="${apply_type == '10010'}">自学重修</c:if>
                    <c:if test="${apply_type == '10011'}">缓考</c:if>
                    申请记录
                </div>
            </div>
            
            <!-- 添加申请按钮 -->
            <c:if test="${flag == 'showAdd'}">
                <div class="action-section">
                    <button class="btn-add-application" onclick="editInfo('', '');">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>
                            填写
                            <c:if test="${apply_type == '10002'}">退课申请</c:if>
                            <c:if test="${apply_type == '10010'}">自学重修申请</c:if>
                            <c:if test="${apply_type == '10011'}">缓考申请</c:if>
                        </span>
                    </button>
                </div>
            </c:if>
            
            <!-- 申请列表 -->
            <div class="applications-section">
                <div class="applications-section-header">
                    <div class="applications-section-title">
                        <i class="ace-icon fa fa-list"></i>
                        <c:if test="${apply_type == '10002'}">退课申请</c:if>
                        <c:if test="${apply_type == '10010'}">自学重修申请</c:if>
                        <c:if test="${apply_type == '10011'}">缓考申请</c:if>
                        列表
                    </div>
                </div>
                
                <div id="applicationsList">
                    <!-- 动态加载申请列表 -->
                </div>
                
                <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                    <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreApplications();">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>加载更多</span>
                    </button>
                </div>
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-minus-circle"></i>
                <div class="empty-state-title">暂无申请记录</div>
                <div class="empty-state-desc">
                    您还没有提交任何
                    <c:if test="${apply_type == '10002'}">退课</c:if>
                    <c:if test="${apply_type == '10010'}">自学重修</c:if>
                    <c:if test="${apply_type == '10011'}">缓考</c:if>
                    申请
                </div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let applicationData = [];

        $(function() {
            initPage();
            loadApplications(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载申请列表
        function loadApplications(page, conditionChanged) {
            if (conditionChanged) {
                currentPage = 1;
            }

            showLoading(true);

            const url = "/student/personalManagement/individualApplication/dropCourseApplication/index/getPage";

            $.ajax({
                url: url,
                cache: false,
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize + "&apply_type=${apply_type}",
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records) {
                        applicationData = data.records;
                        totalCount = data.pageContext ? data.pageContext.totalCount : 0;

                        if (applicationData.length > 0) {
                            renderApplications();
                        } else {
                            showEmptyState();
                        }
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplications() {
            const container = $('#applicationsList');
            container.empty();

            if (applicationData.length === 0) {
                showEmptyState();
                return;
            }

            applicationData.forEach(function(application, index) {
                const applicationHtml = createApplicationItem(application, index);
                container.append(applicationHtml);
            });

            hideEmptyState();
        }

        // 创建申请项目HTML
        function createApplicationItem(application, index) {
            const statusInfo = getStatusInfo(application.APPLY_STATUS);
            const approvalInfo = getApprovalInfo(application.EA_RSLT, application.APPLY_STATUS);

            return `
                <div class="application-item">
                    <div class="application-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="application-index">${index + 1}</div>
                            <div class="application-content">
                                <div class="application-number">${application.APPLY_ID || ''}</div>
                                <div class="application-course">${application.KC || ''}</div>
                            </div>
                        </div>
                        <div class="status-badge ${statusInfo.class}">${statusInfo.text}</div>
                    </div>

                    <div class="application-details">
                        <div class="detail-item">
                            <span class="detail-label">申请日期</span>
                            <span class="detail-value">${application.CZSJ || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">附件</span>
                            <span class="detail-value">${application.FJ > 0 ? '有' : '无'}</span>
                        </div>
                        ${getExtraDetails(application)}
                    </div>

                    ${getReasonSection(application)}

                    <div class="approval-result ${approvalInfo.class}">
                        ${approvalInfo.text}
                    </div>

                    ${application.NOTE ? `
                        <div class="application-note">
                            <strong>备注：</strong>${application.NOTE}
                        </div>
                    ` : ''}

                    <div class="application-actions">
                        ${getActionButtons(application)}
                    </div>
                </div>
            `;
        }

        // 获取额外详情
        function getExtraDetails(application) {
            let details = '';

            // 缓考原因（仅缓考申请且状态不为0时显示）
            if ("${apply_type}" == "10011" && "${hkyyzt}" != "0" && application.HKYYMC) {
                details += `
                    <div class="detail-item">
                        <span class="detail-label">缓考原因</span>
                        <span class="detail-value">${application.HKYYMC}</span>
                    </div>
                `;
            }

            // 动态字段（kzl）
            <c:forEach items="${kzl}" var="l">
                if (application.${fn:toUpperCase(l[0])}) {
                    details += `
                        <div class="detail-item">
                            <span class="detail-label">${l[1]}</span>
                            <span class="detail-value">${application.${fn:toUpperCase(l[0])}}</span>
                        </div>
                    `;
                }
            </c:forEach>

            return details;
        }

        // 获取原因区域
        function getReasonSection(application) {
            let reason = '';

            if ("${apply_type}" == "10002" && application.TKYY) {
                reason = `
                    <div class="application-reason">
                        <strong>退课原因：</strong>${application.TKYY}
                    </div>
                `;
            } else if ("${apply_type}" != "10002" && application.SQYY) {
                const label = "${xxbm}" == "100006" ? "情况说明" : "申请原因";
                reason = `
                    <div class="application-reason">
                        <strong>${label}：</strong>${application.SQYY}
                    </div>
                `;
            }

            return reason;
        }

        // 获取状态信息
        function getStatusInfo(status) {
            switch(status) {
                case -1:
                    return { class: 'status-revoked', text: '撤销' };
                case 0:
                    return { class: 'status-draft', text: '待提交' };
                case 1:
                    return { class: 'status-pending', text: '已提交' };
                case 2:
                    return { class: 'status-processing', text: '审批中' };
                case 3:
                    return { class: 'status-completed', text: '审批结束' };
                default:
                    return { class: 'status-draft', text: '未知' };
            }
        }

        // 获取审批结果信息
        function getApprovalInfo(result, status) {
            if (status == -1) {
                return { class: 'approval-revoked', text: '已撤销' };
            }

            switch(result) {
                case "0":
                    return { class: 'approval-rejected', text: '拒绝' };
                case "1":
                    return { class: 'approval-approved', text: '批准' };
                default:
                    return { class: 'approval-pending', text: '待审批' };
            }
        }

        // 获取操作按钮
        function getActionButtons(application) {
            let buttons = [];

            // 查看按钮
            if (application.APPLY_STATUS == 1 || application.APPLY_STATUS == 2 || application.APPLY_STATUS == 3 || application.APPLY_STATUS == -1) {
                buttons.push(`
                    <button class="btn-application-action btn-view" onclick="seeInfo('${application.APPLY_ID}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看</span>
                    </button>
                `);
            }

            // 修改按钮
            if (application.APPLY_STATUS == 0) {
                buttons.push(`
                    <button class="btn-application-action btn-edit" onclick="editInfo('${application.APPLY_ID}', 'oneApply');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                `);
            }

            // 撤回/撤销按钮
            if ("${apply_type}" == "10011") {
                // 缓考申请的撤销逻辑
                if (application.APPLY_STATUS > 0 &&
                    (application.EA_RSLT == 1 || application.EA_RSLT == "") &&
                    "${yxxscx}" != "0" &&
                    (("${yxxscx}" == "1" && application.APPLY_STATUS == 1) ||
                     ("${yxxscx}" == "2" && (application.APPLY_STATUS == 1 || application.APPLY_STATUS == 2)) ||
                     "${yxxscx}" == "3")) {
                    buttons.push(`
                        <button class="btn-application-action btn-revoke" onclick="revokeInfoByApply('${application.APPLY_ID}');">
                            <i class="ace-icon fa fa-reply"></i>
                            <span>撤销</span>
                        </button>
                    `);
                }
            } else {
                // 其他申请的撤回逻辑
                if (application.APPLY_STATUS == 0) {
                    buttons.push(`
                        <button class="btn-application-action btn-revoke" onclick="revokeInfo('${application.APPLY_ID}');">
                            <i class="ace-icon fa fa-reply"></i>
                            <span>撤回</span>
                        </button>
                    `);
                }
            }

            // 下载按钮
            if (application.FJ > 0) {
                buttons.push(`
                    <button class="btn-application-action btn-download" onclick="doDownload('${application.APPLY_ID}');">
                        <i class="ace-icon fa fa-download"></i>
                        <span>下载</span>
                    </button>
                `);
            }

            return buttons.join('');
        }

        // 查看申请
        function seeInfo(sqbh) {
            const url = "/student/application/index/seeInfo?applyId=" + sqbh + "&applyType=${apply_type}";

            if (parent && parent.addTab) {
                parent.addTab('查看申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 撤销申请（简单撤回）
        function revokeInfo(sqbh) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm("确定要撤销申请？", function(confirmed) {
                    if (confirmed) {
                        doRevokeApplication(sqbh);
                    }
                });
            } else {
                if (confirm("确定要撤销申请？")) {
                    doRevokeApplication(sqbh);
                }
            }
        }

        // 执行撤销申请
        function doRevokeApplication(sqbh) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/dropCourseApplication/revokeInfo",
                type: "post",
                data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val() + "&apply_type=${apply_type}",
                dataType: "json",
                success: function(response) {
                    if (response.status != 200) {
                        showError(response.msg);
                    } else {
                        const data = response.data;
                        if (data.result.indexOf("/") !== -1) {
                            window.location.href = data.result;
                        } else {
                            if (data.result === "ok") {
                                showSuccess("撤销成功！");
                                loadApplications(1, true);
                            } else {
                                showError(data.result);
                            }
                        }
                        $("#tokenValue").val(data.token);
                    }
                },
                error: function() {
                    showError("撤销失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 带原因的撤销申请
        function revokeInfoByApply(sqbh) {
            const reason = prompt("请输入撤销原因：");
            if (reason === null) {
                return; // 用户取消
            }

            if (reason.trim() === "") {
                showError("撤销原因不能为空！");
                return;
            }

            // 计算字符长度（中文算2个字符）
            let length = reason.length;
            for (let i = 0; i < reason.length; i++) {
                if (reason.charCodeAt(i) > 19967) {
                    length++;
                }
            }

            if (length > 500) {
                showError("最大长度不能超过500个字符，一个汉字为两个字符！");
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/dropCourseApplication/revokeInfoByApply",
                type: "post",
                data: "sqbh=" + sqbh + "&cxyy=" + reason + "&tokenValue=" + $("#tokenValue").val() + "&apply_type=${apply_type}",
                dataType: "json",
                success: function(response) {
                    if (response.status != 200) {
                        showError(response.msg);
                    } else {
                        const data = response.data;
                        if (data.result.indexOf("/") !== -1) {
                            window.location.href = data.result;
                        } else {
                            if (data.result === "ok") {
                                showSuccess("撤销成功！");
                                loadApplications(1, true);
                            } else {
                                showError(data.result);
                            }
                        }
                        $("#tokenValue").val(data.token);
                    }
                },
                error: function() {
                    showError("撤销失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 添加/修改申请
        function editInfo(sqbh, applyTimes) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/dropCourseApplication/checkApplyTimes",
                cache: false,
                type: "post",
                dataType: "json",
                success: function(response) {
                    if (response.result === "pass") {
                        location.href = "/student/personalManagement/individualApplication/dropCourseApplication/editInfo?sqbh=" + sqbh + "&apply_type=${apply_type}&apply_times=" + applyTimes;
                    } else {
                        showError(response.result);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:校验失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 下载附件
        function doDownload(id) {
            location.href = "/student/application/index/dodownload/" + id;
        }

        // 显示空状态
        function showEmptyState() {
            $('#emptyState').show();
            $('#loadMoreContainer').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 返回首页
        function returnIndex() {
            location.href = "/student/application/index";
        }

        // 刷新数据
        function refreshData() {
            loadApplications(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
