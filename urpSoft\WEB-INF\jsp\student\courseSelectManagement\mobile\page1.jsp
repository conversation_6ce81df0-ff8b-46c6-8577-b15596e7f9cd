<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link rel="stylesheet" href="/css/phone/phone.css" type="text/css"></link>
<title>选择培养方案</title>
<style type="text/css">
	body {
		margin: 0;
	}
	body .btn {
		border: 0;
	}

	.btn-selected {
		border: 4px solid #5813df !important;
	}
	
	body .fa-check {
		position: absolute;
	    right: -8px;
	    bottom: -4px;
	    color: #5813df;
	    display: none;
	}
	
	#div_score th {
        border-top: 3px solid #ddd !important;
        background-color: transparent;
    }
	
	.self-margin .header {
	    margin-top: 6px !important;
	    margin-bottom: 10px !important;
	    padding-bottom: 4px !important;
	    border-bottom: 1px solid #CCC;
	    line-height: 28px;
	}
	
	.header.grey {
	    border-bottom-color: #c3c3c3;
	}
	h4.smaller {
	    font-size: 17px;
	}
	.header {
	    line-height: 28px;
	    margin-bottom: 16px;
	    margin-top: 18px;
	    padding-bottom: 4px;
	    border-bottom: 1px solid #CCC;
	}
	.grey {
	    color: #777 !important;
	}
	.lighter {
	    font-weight: lighter;
	}
          
          .btn.btn-round {
	    border-radius: 4px !important;
	}
	
	.btn.btn-bold, .btn.btn-round {
	    border-bottom-width: 2px;
	}
	
	.btn-group-xs>.btn, .btn-xs {
	    padding-top: 3px;
	    padding-bottom: 3px;
	    border-width: 3px;
	}
	.btn {
	    color: #FFF !important;
	    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);
	    background-image: none !important;
	    border: 5px solid #FFF;
	    border-radius: 0;
	    box-shadow: none !important;
	    -webkit-transition: background-color .15s, border-color .15s, opacity .15s;
	    -o-transition: background-color .15s,border-color .15s,opacity .15s;
	    transition: background-color .15s, border-color .15s, opacity .15s;
	    vertical-align: middle;
	    margin: 0;
	    position: relative;
	    font-weight: 400;
	}
	.breadcrumb, .breadcrumb>li>a, .btn {
	    display: inline-block;
	}
	.btn, .dropdown-colorpicker a {
	    cursor: pointer;
	}
	.btn-group-xs>.btn, .btn-xs {
	    padding: 1px 5px;
	    font-size: 12px;
	    line-height: 1.3;
	    border-radius: 3px;
	}
	.btn, .btn-danger.active, .btn-danger:active, .btn-default.active, .btn-default:active, .btn-info.active, .btn-info:active, .btn-primary.active, .btn-primary:active, .btn-warning.active, .btn-warning:active, .btn.active, .btn:active, .dropdown-menu>.disabled>a:focus, .dropdown-menu>.disabled>a:hover, .form-control, .navbar-toggle, .open>.dropdown-toggle.btn-danger, .open>.dropdown-toggle.btn-default, .open>.dropdown-toggle.btn-info, .open>.dropdown-toggle.btn-primary, .open>.dropdown-toggle.btn-warning {
	    background-image: none;
	}
	button, input, select, textarea {
	    font-family: inherit;
	    font-size: inherit;
	    line-height: inherit;
	}
	button, html input[type=button], input[type=reset], input[type=submit] {
	    -webkit-appearance: button;
	    cursor: pointer;
	}
	button, select {
	    text-transform: none;
	}
	button {
	    overflow: visible;
	}
	button, input, optgroup, select, textarea {
	    color: inherit;
	    font: inherit;
	    margin: 0;
	}
	.btn, .btn-default, .btn-default.focus, .btn-default:focus, .btn.focus, .btn:focus {
	    background-color: #ABBAC3 !important;
	    border-color: #ABBAC3;
	}
	.btn-info, .btn-info.focus, .btn-info:focus {
	    background-color: #6FB3E0 !important;
	    border-color: #6FB3E0;
	}
	.btn-success, .btn-success.focus, .btn-success:focus {
	    background-color: #87B87F !important;
	    border-color: #87B87F;
	}
	.row-btn {
		margin-bottom:10px;
		padding: 10px 30px 10px 0;
		width: 100%;
	}
	.modal-open .modal {
	    overflow-x: hidden;
	    overflow-y: auto;
	}
	.fade.in {
	    opacity: 1;
	}
	
	.modal {
	    display: none;
	    position: fixed;
	    top: 0;
	    z-index: 1050;
	    -webkit-overflow-scrolling: touch;
	    outline: 0;
	}
	.modal, .modal-backdrop {
	    right: 0;
	    bottom: 0;
	    left: 0;
	}
	
	.modal.fade .modal-dialog {
	    -webkit-transform: translate(0, -25%);
	    -ms-transform: translate(0,-25%);
	    -o-transform: translate(0,-25%);
	    transform: translate(0, -25%);
	    -webkit-transition: -webkit-transform .3s ease-out;
	    -moz-transition: -moz-transform .3s ease-out;
	    -o-transition: -o-transform .3s ease-out;
	    transition: transform .3s ease-out;
	}
	.modal-dialog {
	    position: relative;
	    width: auto;
	    margin: 90px auto;
	}
	
	.modal-content {
	    border-radius: 0;
	    -webkit-box-shadow: none;
	    box-shadow: none;
	}
	
	.modal-content {
	    position: relative;
	    background-color: #fff;
	    border: 1px solid #999;
	    border: 1px solid rgba(0, 0, 0, .2);
	    border-radius: 6px;
	    -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
	    box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
	    outline: 0;
	}
	.modal-content, .popover {
	    background-clip: padding-box;
	}
	.no-padding {
	    padding: 0 !important;
	}
	
	.modal-header {
	    padding: 15px;
	    border-bottom: 1px solid #e5e5e5;
	}
	.table-header {
	    background-color: #307ECC;
	    color: #FFF;
	    font-size: 14px;
	    line-height: 38px;
	    padding-left: 12px;
	    margin-bottom: 1px;
	}
	.table-header .close {
	    margin-right: 8px;
	    margin-top: 0;
	    opacity: .45;
	    filter: alpha(opacity=45);
	}
	
	.modal-header .close {
	    font-size: 32px;
	}
	.modal-header .close {
	    margin-top: 2px;
	    color: white;
	}
	button.close {
	    padding: 0;
	    cursor: pointer;
	    background: 0 0;
	    border: 0;
	    -webkit-appearance: none;
	}
	.close {
	    float: right;
	    font-size: 21px;
	    color: #000;
	    text-shadow: 0 1px 0 #fff;
	    opacity: .2;
	    filter: alpha(opacity=20);
	}
	.alert .alert-link, .close {
	    font-weight: 700;
	}
	.badge, .close, .label {
	    line-height: 1;
	}
	.modal-body {
	    position: relative;
	    padding: 15px;
	}
	.modal-footer {
	    padding-top: 12px;
	    padding-bottom: 14px;
	    border-top-color: #E4E9EE;
	    -webkit-box-shadow: none;
	    box-shadow: none;
	    background-color: #EFF3F8;
	}
	
	.no-margin-top {
	    margin-top: 0 !important;
	}
	.modal-footer {
	    padding: 15px;
	    text-align: right;
	    border-top: 1px solid #e5e5e5;
	}
	
	.modal-backdrop.in {
	    opacity: .5;
	    filter: alpha(opacity=50);
	}
	
	.modal-backdrop {
	    position: fixed;
	    top: 0;
	    z-index: 1040;
	    background-color: #000;
	}
	.fade {
	    opacity: 0;
	    -webkit-transition: opacity .15s linear;
	    -o-transition: opacity .15s linear;
	    transition: opacity .15s linear;
	}
	
	.self {
	    width: 100% !important;
	}
	
	.profile-user-info-striped {
	    border: 1px solid #DCEBF7;
	}
	.profile-user-info {
	    display: table;
	    width: 98%;
	    width: calc(100% - 24px);
	    margin: 0 auto;
	}
	.profile-info-row {
	    display: table-row;
	}
	.profile-info-row:first-child .profile-info-name, .profile-info-row:first-child .profile-info-value {
	    border-top: none;
	}
	
	.self .profile-info-name {
	    padding: 2px 6px;
	    min-width: 95px;
	}
	.profile-user-info-striped .profile-info-name {
	    color: #336199;
	    background-color: #EDF3F4;
	    border-top: 1px solid #F7FBFF;
	}
	.profile-info-name {
	    text-align: right;
	    padding: 6px 10px 6px 4px;
	    font-weight: 400;
	    color: #667E99;
	    background-color: transparent;
	    border-top: 1px dotted #D5E4F1;
	    display: table-cell;
	    width: 110px;
	    vertical-align: middle;
	}
	.profile-info-row:first-child .profile-info-name, .profile-info-row:first-child .profile-info-value {
	    border-top: none;
	}
	
	.self .profile-info-value {
	    padding: 2px 6px;
	    vertical-align: middle;
	}
	.profile-user-info-striped .profile-info-value {
	    border-top: 1px dotted #DCEBF7;
	    padding-left: 12px;
	}
	.profile-info-value {
	    display: table-cell;
	    padding: 6px 4px 6px 6px;
	    border-top: 1px dotted #D5E4F1;
	}
	
	.table-bordered, td, th {
	    border-radius: 0 !important;
	}
	
	.table-bordered, .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
	    border: 1px solid #ddd;
	}
	.table {
	    width: 100%;
	    max-width: 100%;
	    margin-bottom: 20px;
	}
	pre code, table {
	    background-color: transparent;
	}
	table {
	    border-collapse: collapse;
	    border-spacing: 0;
	}
	.table>thead>tr {
	    color: #707070;
	    font-weight: 400;
	    background: repeat-x #F2F2F2;
	    background-image: -webkit-linear-gradient(top, #F8F8F8 0, #ECECEC 100%);
	    background-image: -o-linear-gradient(top,#F8F8F8 0,#ECECEC 100%);
	    background-image: linear-gradient(to bottom, #F8F8F8 0, #ECECEC 100%);
	    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff8f8f8', endColorstr='#ffececec', GradientType=0);
	}
	.table.table-bordered>thead>tr>th:first-child {
	    border-left-color: #ddd;
	}
	
	.table>caption+thead>tr:first-child>td, .table>caption+thead>tr:first-child>th, .table>colgroup+thead>tr:first-child>td, .table>colgroup+thead>tr:first-child>th, .table>thead:first-child>tr:first-child>td, .table>thead:first-child>tr:first-child>th {
	    border-top: 0;
	}
	.table.table-bordered>thead>tr>th {
	    vertical-align: middle;
	}
	.table>thead>tr>th:first-child {
	    border-left-color: #F1F1F1;
	}
	.table>thead>tr>th {
	    border-color: #ddd;
	    font-weight: 700;
	}
	.table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
	    border-bottom-width: 2px;
	}
	.table-bordered, .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
	    border: 1px solid #ddd;
	}
	.table>thead>tr>th {
	    vertical-align: bottom;
	    border-bottom: 2px solid #ddd;
	}
	.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
	    padding: 8px;
	    line-height: 1.42857143;
	    vertical-align: top;
	    border-top: 1px solid #ddd;
	}
	.table-bordered, td, th {
	    border-radius: 0 !important;
	}
	caption, th {
	    text-align: left;
	}
	.table-striped>tbody>tr:nth-of-type(odd) {
	    background-color: #f9f9f9;
	}
</style>
<script src="/js/jQuery/jquery-3.4.1.min.js"></script>
<script type="text/javascript" src="/assets/layer/layer.js"></script>
<script type="text/javascript">
	function Urp() {
	};
	
	Urp.prototype = {
		"alert": function (msg, callback) {
			layer.msg(msg);
			if (typeof callback === 'function') {
				setTimeout(callback, 1000);
			}
		},
		"fixedheader": function (tableDivId, divHeight) {
			var h = (divHeight == undefined || divHeight == null) ? '\'450\'' : divHeight;
	
			if ($("#" + tableDivId).length > 0) {
				$("#" + tableDivId).addClass('table-cont');
				$("#" + tableDivId + " table").css({"margin-top": "-2px", "display": "table", "border-collapse": "separate"});
				//$("#"+tableDivId).css({"max-height":h,"overflow":"auto"});
				var tableCont = document.querySelector('#' + tableDivId);
	
				function scrollHandle(e) {
					var scrollTop = this.scrollTop - 1;
					this.querySelector('thead').style.transform = 'translateY(' + scrollTop + 'px)';
				}
	
				tableCont.addEventListener('scroll', scrollHandle);
			}
		},
		"confirm": function (msg, callback) {
			layer.confirm(
				msg,
				{btn: ['确定', '取消'], closeBtn: 0, title: "提示信息"},
				function () {
					if (typeof callback === 'function') {
						layer.closeAll('dialog');
						callback(true);
					}
				},
				function () {
					if (typeof callback === 'function') {
						callback(false);
					}
				}
			);
		}
	};
	urp = new Urp();
	//加载培养方案
	$(function(){
		if("${yxcxfx}" == "1" || ("${xkjdlx}" == "005" && "${xxbm}" == "100010")) {
			$("#div_xkfsList button").hide();
			$("#faxk").show();
			$("#cxxk").show();
		} else {
			var xsfaxk = '${xsfaxk}';
			var xszyxk = '${xszyxk}';
			var xsfxxk = '${xsfxxk}';
			if (xsfaxk == "0") {
				$("#faxk").hide();
			}
			if (xszyxk == "0") {
				$("#zyxk").hide();
			}
			if (xsfxxk == "1") {
				$("#fxxk").show();
			}
		}
		
		var pyfaList = eval('(${pyblist})');
		var pyfaCont = "";
		$.each(pyfaList,function(i,v){
			var tCont = "<button id='"+ v.id.fajhh +"' onclick='togglefa(this);' type='button' class='btn btn-md btn-round row-btn";
			if(v.xdlxmc=="主修") {
				tCont += " btn-success'> 📒"+v.famc+"("+v.xdlxmc+")</button>";
				pyfaCont = tCont + pyfaCont;
			} else {
				tCont += " btn-info'> 📒"+v.famc+"("+v.xdlxmc+")</button>";
				pyfaCont += tCont;
			}
			
		});
		
		$("#div_pyfaList").html(pyfaCont);
		$("#div_pyfaList button:first").click();
		

		$("#div_xkfsList button").on("click", function(e) {
			togglefa(e.target);
			setTimeout(()=>{
				document.form.submit();
			}, 500);
			layer.msg("正在跳转，请稍候...");
		});
		
		<c:if test="${xxbm == '100020'}">
			urp.fixedheader("div_score","450");
			$("#view-filterData").addClass("in");
			$("body").append("<div class='modal-backdrop fade in'></div>");
			$("#view-filterData").css("display", "block");
		</c:if>
	});
	
	function togglefa(obj) {
		let divName = $(obj).parents("#div_xkfsList").length > 0 ? "div_xkfsList" : "div_pyfaList";
		$("#"+ divName +" button").removeClass("btn-selected");
		$("#"+ divName +" .fa-check").hide();
		$(obj).addClass("btn-selected");
		$(obj).find(".fa-check").show();
		$("#"+ divName.substring(4, 8) +"").val($(obj).attr("id"));
	}
	
	<c:if test="${xxbm == '100020'}">
		function closeModel() {
			$(".modal-backdrop").remove();
			$("#view-filterData").css("display", "none");
		}
	</c:if>
</script>
</head>
<body>
	<h5 class="phone-header smaller lighter grey" style="height: 38px; line-height: 38px;">
		<span class="ace-icon fa fa-angle-left bigger-130 phone-header-left" style="font-weight: bold; font-size: 20px !important; top: auto;" onclick="parent.closeFrame()">✕</span>
		<span class="phone-header-center">选课</span>
	</h5>
	<div style="padding: 0 12px; overflow: auto; height: calc(100vh - 50px);">
		<h4 class="header smaller lighter grey" style="margin-top: 6px">
			<i class="fa fa-list"></i> 📒选择方案名称
		</h4>
		<form name="form"
			action="/student/courseSelect/selectCourse/index"
			method="get">
			<input type="hidden" name="mobile" value="true"/>
			<input type="hidden" id="pyfa" name="fajhh" value=""/>
			<input type="hidden" id="xkfs" name="xkfs" value=""/>
			<!-- 方案计划号 -->
			<div id="div_pyfaList"></div>
		</form>
		<h4 class="header smaller lighter grey" style="margin-top: 6px">
			<i class="fa fa-list"></i> ✅选择选课方式
		</h4>
		<div id="div_xkfsList">
			<button id="jhxk" type='button' class='btn btn-md btn-round btn-info row-btn'>
				✅推荐选课
			</button>
			<button id="faxk" type='button' class='btn btn-md btn-round btn-info row-btn'>
				✅方案选课
			</button>
			<c:if test="${xxbm != '100008'}">
				<button id="xirxk" type='button' class='btn btn-md btn-round btn-info row-btn'>
					✅系任选课
				</button>
				<button id="xarxk" type='button' class='btn btn-md btn-round btn-info row-btn'>
					✅校任选课
				</button>
			</c:if>
			<button id="zyxk" type='button' class='btn btn-md btn-round btn-info row-btn'>
				✅自由选课
			</button>
			<button id="cxxk" type='button' class='btn btn-md btn-round btn-info row-btn'>
				✅重修选课
			</button>
			<button id="fxxk" type='button' class='btn btn-md btn-round btn-info row-btn' style="display: none;">
				✅复修选课
			</button>
		</div>
	</div>
	<c:if test="${xxbm == '100020'}">
		<div class="modal fade" id="view-filterData" tabindex="1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog" style="width:80%;">
				<div class="modal-content">
					<div class="modal-header no-padding">
						<div class="table-header">
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true" onclick="closeModel();">
								<span class="white">×</span>
							</button>
							<i class="fa fa-filter"></i> 选课数据分析
						</div>
					</div>
					<div class="modal-body no-padding" style="padding: 0 12px !important;">
						<div class="profile-user-info profile-user-info-striped self">
							<div class="profile-info-row">
								<div class="profile-info-name" style="width: 150px;">学位主干课程平均分</div>
								<div class="profile-info-value">${pjf == null || pjf[0] == null ? 0 : pjf[0]}</div>
							</div>
							<div class="profile-info-row">
								<div class="profile-info-name" style="width: 150px;">英语课程平均分</div>
								<div class="profile-info-value">${pjf == null || pjf[1] == null ? 0 : pjf[1]}</div>
							</div>
							<div class="profile-info-row">
								<div class="profile-info-name" style="width: 150px;">计算机课程平均分</div>
								<div class="profile-info-value">${pjf == null || pjf[2] == null ? 0 : pjf[2]}</div>
							</div>
						</div>
						
						<h4 class="header smaller lighter grey" style="margin-top: 6px">
							<i class="fa fa-list"></i> 本学期已开课应重修报名课程
						</h4>
						<div id="div_score" style="overflow: auto; max-height: calc(100vh - 230px);">
							<table id="courseList_table" class="table table-hover table-bordered table-striped">
								<thead>
									<tr>
										<th style="width: 50px;">序号</th>
										<th>课程号</th>
										<th>课程名</th>
										<th>最高成绩</th>
										<th>学年学期</th>
									</tr>
								</thead>
								<tbody>
									<c:if test="${empty cxkc}">
										<tr><td colspan="5">无数据</td></tr>
									</c:if>
									<c:if test="${not empty cxkc}">
										<c:forEach var="kc" items="${cxkc}" varStatus="i">
											<tr>
												<td>${i.index + 1}</td>
												<td>${kc[0] == null ? "" : kc[0]}</td>
												<td>${kc[1] == null ? "" : kc[1]}</td>
												<td>${kc[2] == null ? "" : kc[2]}</td>
												<td>${kc[3] == null ? "" : kc[3]}</td>
											</tr>
										</c:forEach>
									</c:if>
								</tbody>
							</table>
						</div>
						
						<div style="clear:both;"></div>
					</div>
		
					<div class="modal-footer no-margin-top" style="padding:5px 10px;">
						<button class="btn btn-xs btn-round" data-dismiss="modal" id="closeFilterData" onclick="closeModel();">
							<i class="ace-icon fa fa-times bigger-120"></i> 关闭
						</button>
					</div>
				</div>
			</div>
		</div>
	</c:if>
</body>
</html>
