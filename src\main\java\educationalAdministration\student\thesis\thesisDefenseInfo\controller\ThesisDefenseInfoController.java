package educationalAdministration.student.thesis.thesisDefenseInfo.controller;

import com.alibaba.fastjson.JSONObject;
import com.urpSoft.core.license.LicenseManger;
import com.urpSoft.core.pagination.service.IPageService;
import com.urpSoft.core.util.AuthUtil;
import educationalAdministration.student.common.utils.CommonUtils;
import educationalAdministration.student.thesis.thesisDefenseInfo.service.ThesisDefenseInfoService;
import org.springframework.stereotype.Controller;
import com.urpSoft.core.util.CSRFToken;
import org.apache.log4j.Logger;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.List;

/**
 * 我的答辩
 */
@Controller
public class ThesisDefenseInfoController {
	private static final Logger logger = Logger.getLogger(ThesisDefenseInfoController.class);

	@Resource
	private IPageService pageService;

	private CSRFToken csrfToken = CSRFToken.getInstance();

	@Resource
	private ThesisDefenseInfoService thesisDefenseInfoService;


	@RequestMapping(value = "/student/thesis/thesis/defense/information/index", method = RequestMethod.GET)
	public String index(Model model, HttpSession session) {
		String schoolId = LicenseManger.getSchoolId();
		model.addAttribute("schoolid", schoolId);

		String xh = AuthUtil.getCurrentUser().getIdNumber();
		String sql = "select b.pcmc," +
				"       p.fajhh," +
				"       p.famc," +
				"       p.nj," +
				"       pn.xsm(p.xsh) as xsm," +
				"       pn.zym(p.xsh, p.zyh) as zym," +
				"       t.tmbh," +
				"       t.tmmc_xs as tmmc," +
				"       t.jsh," +
				"       pn.jsm(t.jsh) as jsm," +
				"       t.dezdjs as jsh2nd," +
				"       pn.jsm(t.dezdjs) as jsm2nd," +
				"       d.dbzgscjg," +
				"       pn.jsm(x.dbzgczr) as scr," +
				"       to_char(to_date(x.dbzgczsj,'yyyymmddhh24miss'),'yyyy-mm-dd hh24:mi:ss') as dbzgczsj,";
		if ("100015".equals(schoolId)) {
			sql += "       g.dbrq || g.dbqzsj as dbsj," +
					"       g.dbdd,";
		} else {
			sql += "       e.dbsj," +
					"       e.dbdd,";
		}

		sql += "       g.dbxzmc," +
				"       (select listagg(pn.jsm(m.jsh) || '[' || u.dbxzzwsm || ']' || '[' || nvl(pn.js_zc(m.jsh), '无职称') || ']', '<br>') within group(order by u.dbxzzwdm, m.jsh)" +
				"          from lw_dbxzcyb m, code_dbxzzwdm u where m.dbxzzwdm = u.dbxzzwdm(+) and (m.zxjxjhh, m.dbxzbh) in ((g.zxjxjhh, g.dbxzbh))) as dbcy," +
				"       (select c.dbxzlbmc from lw_code_dbxzlbb c where g.dbxzlb = c.dbxzlbdm) as dbxzlbmc" +
				"  from lw_pcb         b," +
				"       lw_tmxxb       t," +
				"       lw_xtb         x," +
				"       jh_fajhb       p," +
				"       code_dbzgscjgb d," +
				"       lw_dbxzxsb     e," +
				"       lw_dbxzb       g" +
				" where b.pch(+) = t.zxjxjhh" +
				"   and t.zxjxjhh = x.zxjxjhh" +
				"   and t.tmbh = x.tmbh" +
				"   and x.fajhh = p.fajhh" +
				"   and x.dbzgscjgm = d.dbzgscjgm(+)" +
				"   and x.zxjxjhh = e.zxjxjhh(+)" +
				"   and x.tmbh = e.tmbh(+)" +
				"   and x.xh = e.xh(+)" +
				"   and e.zxjxjhh = g.zxjxjhh(+)" +
				"   and e.dbxzbh = g.dbxzbh(+)" +
				"   and x.xtztdm = '02'" +
				"   and x.xh = '" + xh + "'" +
				" order by x.zxjxjhh desc, x.fajhh ";
		List<JSONObject> xsxtfabs = CommonUtils.queryJsonDataBySql(sql);
		model.addAttribute("xsxtfabs", xsxtfabs);
		return "/student/thesis/thesisDefenseInfo/index";
	}

}