<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>微专业方案注册</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 微专业方案注册页面样式 */
        .microspecialty-header {
            background: linear-gradient(135deg, var(--primary-color), var(--warning-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .microspecialty-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .microspecialty-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .time-info-section {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--info-dark);
            font-size: var(--font-size-small);
            line-height: 1.5;
        }
        
        .action-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-add-application {
            width: 100%;
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-add-application:hover {
            background: var(--success-dark);
        }
        
        .btn-add-application:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .programs-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .programs-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .programs-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .programs-section-title i {
            color: var(--warning-color);
        }
        
        .program-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .program-item:last-child {
            border-bottom: none;
        }
        
        .program-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .program-index {
            background: var(--warning-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .program-content {
            flex: 1;
        }
        
        .program-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
            cursor: pointer;
        }
        
        .program-name:hover {
            color: var(--primary-color);
        }
        
        .program-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            align-items: flex-start;
        }
        
        .detail-label {
            font-weight: 500;
            min-width: 60px;
            margin-right: var(--margin-sm);
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
            word-break: break-word;
        }
        
        .status-badges {
            display: flex;
            gap: var(--spacing-xs);
            margin-bottom: var(--margin-sm);
            flex-wrap: wrap;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-submitted {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-processing {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-revoked {
            background: var(--text-disabled);
            color: white;
        }
        
        .program-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-program-action {
            flex: 1;
            min-width: 80px;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-print {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-revoke {
            background: var(--error-color);
            color: white;
        }
        
        .btn-apply {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-records {
            background: var(--success-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .empty-state-action {
            margin-top: var(--margin-md);
        }
        
        .btn-empty-action {
            background: var(--success-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            text-decoration: none;
        }
        
        .warning-alert {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .warning-alert i {
            color: var(--warning-color);
            font-size: 20px;
        }
        
        @media (max-width: 480px) {
            .microspecialty-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .time-info-section,
            .action-section,
            .programs-section,
            .warning-alert {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .program-details {
                grid-template-columns: 1fr;
            }
            
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .detail-value {
                text-align: left;
                margin-top: 4px;
            }
            
            .program-actions {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">微专业方案注册</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 微专业方案注册头部 -->
        <div class="microspecialty-header">
            <div class="microspecialty-title">微专业方案注册</div>
            <div class="microspecialty-desc">管理您的微专业方案注册申请</div>
        </div>
        
        <!-- 状态提示信息 -->
        <c:if test="${flag == 'nonparametric'}">
            <div class="warning-alert">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>注册申请参数未维护，请联系管理员处理</span>
            </div>
        </c:if>
        
        <c:if test="${flag == 'notenabled'}">
            <div class="warning-alert">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>注册申请未启用，请联系管理员处理</span>
            </div>
        </c:if>
        
        <c:if test="${flag == 'nottime'}">
            <div class="warning-alert">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>不在可注册申请时间范围或注册申请开关关闭</span>
            </div>
        </c:if>
        
        <!-- 申请时间信息 -->
        <c:if test="${flag == 'showAdd'}">
            <div class="time-info-section">
                <strong>申请时间：</strong>
                ${fn:substring(kzkg.kssj, 0, 4)}-${fn:substring(kzkg.kssj, 4, 6)}-${fn:substring(kzkg.kssj, 6, 8)} ${fn:substring(kzkg.kssj, 8, 10)}:${fn:substring(kzkg.kssj, 10, 12)}:${fn:substring(kzkg.kssj, 12, 14)}~${fn:substring(kzkg.jssj, 0, 4)}-${fn:substring(kzkg.jssj, 4, 6)}-${fn:substring(kzkg.jssj, 6, 8)} ${fn:substring(kzkg.jssj, 8, 10)}:${fn:substring(kzkg.jssj, 10, 12)}:${fn:substring(kzkg.jssj, 12, 14)}
            </div>
        </c:if>
        
        <!-- 添加申请按钮 -->
        <c:if test="${flag == 'showAdd'}">
            <div class="action-section">
                <button class="btn-add-application" onclick="registerChecked();">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>申请注册微专业</span>
                </button>
            </div>
        </c:if>
        
        <!-- 方案列表 -->
        <div class="programs-section">
            <div class="programs-section-header">
                <div class="programs-section-title">
                    <i class="ace-icon fa fa-list"></i>
                    微专业方案列表
                </div>
            </div>
            
            <div id="programsList">
                <!-- 动态加载方案列表 -->
            </div>
        </div>
        
        <!-- 空状态 -->
        <c:if test="${empty xdsqList && empty zxxdsqList && empty zrZxxdsqList}">
            <div class="empty-state">
                <i class="ace-icon fa fa-graduation-cap"></i>
                <div class="empty-state-title">暂无方案记录</div>
                <div class="empty-state-desc">您还没有申请微专业方案</div>
                <c:if test="${flag == 'showAdd'}">
                    <div class="empty-state-action">
                        <button class="btn-empty-action" onclick="registerChecked();">点此申请</button>
                    </div>
                </c:if>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let xdsqList = eval('${xdsqList}') || [];
        let zxxdsqList = eval('(${zxxdsqList})') || [];
        let sqList = eval('${sqList}') || [];
        let zrZxxdsqList = eval('${zrZxxdsqList}') || [];

        $(function() {
            initPage();
            renderPrograms();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 渲染方案列表
        function renderPrograms() {
            const container = $('#programsList');
            container.empty();

            // 渲染注册申请列表
            if (xdsqList && xdsqList.length > 0) {
                xdsqList.forEach(function(program, index) {
                    const programHtml = createProgramItem(program, index, 'register');
                    container.append(programHtml);
                });
            }

            // 渲染自然注销申请列表
            if (zrZxxdsqList && zrZxxdsqList.length > 0) {
                zrZxxdsqList.forEach(function(program, index) {
                    const programHtml = createProgramItem(program, index + (xdsqList ? xdsqList.length : 0), 'cancel');
                    container.append(programHtml);
                });
            }
        }

        // 创建方案项目HTML
        function createProgramItem(program, index, type) {
            const statusInfo = getStatusInfo(program, type);
            const cancelStatusInfo = getCancelStatusInfo(program);

            return `
                <div class="program-item">
                    <div class="program-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="program-index">${index + 1}</div>
                            <div class="program-content">
                                <div class="program-name" onclick="showProgramInfo('${program.id.jhfajhb.fajhh}');">
                                    ${program.id.jhfajhb.famc || ''}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="program-details">
                        <div class="detail-item">
                            <span class="detail-label">所属院系</span>
                            <span class="detail-value">${program.id.jhfajhb.department.departmentName || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">修读类型</span>
                            <span class="detail-value">${program.id.jhfajhb.codeXdlxb.xdlxmc || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">申请日期</span>
                            <span class="detail-value">${dealNull(program.sqrq)}</span>
                        </div>
                    </div>

                    <div class="status-badges">
                        <div class="status-badge ${statusInfo.class}">${statusInfo.text}</div>
                        ${cancelStatusInfo.text ? `<div class="status-badge ${cancelStatusInfo.class}">${cancelStatusInfo.text}</div>` : ''}
                    </div>

                    <div class="program-actions">
                        ${getActionButtons(program, type)}
                    </div>
                </div>
            `;
        }

        // 获取注册状态信息
        function getStatusInfo(program, type) {
            if (type === 'cancel') {
                // 注销申请状态
                if (program.apply_status == '1') {
                    return { class: 'status-submitted', text: '注销已提交' };
                } else if (program.apply_status == '2') {
                    return { class: 'status-processing', text: '注销审批中' };
                } else if (program.ea_rslt == '1') {
                    return { class: 'status-approved', text: '注销已批准' };
                } else if (program.ea_rslt == '0') {
                    return { class: 'status-rejected', text: '注销未批准' };
                } else if (program.apply_status == '-1') {
                    return { class: 'status-revoked', text: '注销撤销' };
                } else {
                    return { class: '', text: '' };
                }
            } else {
                // 注册申请状态
                if (program.apply_status == '1') {
                    return { class: 'status-submitted', text: '注册已提交' };
                } else if (program.apply_status == '2') {
                    return { class: 'status-processing', text: '注册审批中' };
                } else if (program.ea_rslt == '1') {
                    return { class: 'status-approved', text: '注册已批准' };
                } else if (program.ea_rslt == '0') {
                    return { class: 'status-rejected', text: '注册未批准' };
                } else if (program.apply_status == '-1') {
                    return { class: 'status-revoked', text: '注册撤销' };
                } else {
                    return { class: '', text: '' };
                }
            }
        }

        // 获取注销状态信息
        function getCancelStatusInfo(program) {
            if (!zxxdsqList || zxxdsqList.length === 0) {
                return { class: '', text: '' };
            }

            for (let i = 0; i < zxxdsqList.length; i++) {
                const cancel = zxxdsqList[i];
                if (program.id.jhfajhb.fajhh == cancel.id.jhfajhb.fajhh &&
                    program.id.xh == cancel.id.xh &&
                    (program.commit_dt < cancel.commit_dt || program.commit_dt == "" || program.commit_dt == null)) {

                    if (cancel.apply_status == '1') {
                        return { class: 'status-submitted', text: '注销已提交' };
                    } else if (cancel.apply_status == '2') {
                        return { class: 'status-processing', text: '注销审批中' };
                    } else if (cancel.ea_rslt == '1') {
                        return { class: 'status-approved', text: '注销已批准' };
                    } else if (cancel.ea_rslt == '0') {
                        return { class: 'status-rejected', text: '注销未批准' };
                    } else if (cancel.apply_status == '-1') {
                        return { class: 'status-revoked', text: '注销撤销' };
                    }
                }
            }
            return { class: '', text: '' };
        }

        // 获取操作按钮
        function getActionButtons(program, type) {
            let buttons = [];

            if (type === 'cancel') {
                // 注销申请的操作按钮
                if (program.apply_status == '1' ||
                    (program.codeSqztb.sqztdm == '11' &&
                     (program.yxspzt == "" || program.yxspzt == null || program.yxspzt == "null"))) {
                    buttons.push(`
                        <button class="btn-program-action btn-revoke" onclick="revokeApplication('${program.id.sqbh}@@@${program.id.xh}@@@${program.id.jhfajhb.fajhh}');">
                            <i class="ace-icon fa fa-trash-o"></i>
                            <span>撤回注销</span>
                        </button>
                    `);
                }

                buttons.push(`
                    <button class="btn-program-action btn-view" onclick="viewApplication('${program.id.sqbh}', '${program.codeSqlxb.sqlxdm}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看注销</span>
                    </button>
                `);

                buttons.push(`
                    <button class="btn-program-action btn-print" onclick="printApplication('${program.id.jhfajhb.fajhh}', '${program.id.sqbh}', '${program.codeSqlxb.sqlxdm}');">
                        <i class="ace-icon fa fa-print"></i>
                        <span>打印注销</span>
                    </button>
                `);
            } else {
                // 注册申请的操作按钮
                if (program.apply_status != "" && program.apply_status != null) {
                    if (program.apply_status == '1') {
                        buttons.push(`
                            <button class="btn-program-action btn-revoke" onclick="revokeApplication('${program.id.sqbh}@@@${program.id.xh}@@@${program.id.jhfajhb.fajhh}');">
                                <i class="ace-icon fa fa-trash-o"></i>
                                <span>撤回注册</span>
                            </button>
                        `);
                    }
                } else {
                    if ((program.codeSqztb.sqztdm == '11' &&
                         (program.yxspzt == "" || program.yxspzt == null || program.yxspzt == "null"))) {
                        buttons.push(`
                            <button class="btn-program-action btn-revoke" onclick="revokeApplication('${program.id.sqbh}@@@${program.id.xh}@@@${program.id.jhfajhb.fajhh}');">
                                <i class="ace-icon fa fa-trash-o"></i>
                                <span>撤回注册</span>
                            </button>
                        `);
                    }
                }

                // 查看和打印按钮
                if (sqList && sqList.length > 0) {
                    for (let j = 0; j < sqList.length; j++) {
                        if (program.id.jhfajhb.fajhh == sqList[j][0]) {
                            if (sqList[j][1] > 1) {
                                buttons.push(`
                                    <button class="btn-program-action btn-records" onclick="queryApplications('${program.id.jhfajhb.fajhh}');">
                                        <i class="ace-icon fa fa-exchange"></i>
                                        <span>变更记录</span>
                                    </button>
                                `);
                            } else {
                                if (program.id.sqbh != "zr") {
                                    buttons.push(`
                                        <button class="btn-program-action btn-view" onclick="viewApplication('${program.id.sqbh}', '${program.codeSqlxb.sqlxdm}');">
                                            <i class="ace-icon fa fa-eye"></i>
                                            <span>查看注册</span>
                                        </button>
                                    `);
                                    buttons.push(`
                                        <button class="btn-program-action btn-print" onclick="printApplication('${program.id.jhfajhb.fajhh}', '${program.id.sqbh}', '${program.codeSqlxb.sqlxdm}');">
                                            <i class="ace-icon fa fa-print"></i>
                                            <span>打印注册</span>
                                        </button>
                                    `);
                                }
                            }
                            break;
                        }
                    }
                }
            }

            return buttons.join('');
        }

        // 显示方案信息
        function showProgramInfo(fajhh) {
            const url = "/student/rollManagement/minorProgramRegistration/detail?fajhh=" + fajhh + "&lx=1";

            if (parent && parent.addTab) {
                parent.addTab('方案详情', url);
            } else {
                window.location.href = url;
            }
        }

        // 查看申请
        function viewApplication(sqbh, sqlxdm) {
            const applyType = sqlxdm == "17" ? "10006" : "10008";
            const url = "/student/application/index/seeInfo?applyId=" + sqbh + "&applyType=" + applyType;

            if (parent && parent.addTab) {
                parent.addTab('查看申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 打印申请
        function printApplication(fajhh, sqbh, sqlxdm) {
            if (sqbh == "" || sqbh == null || sqbh == undefined || sqbh == "null") {
                sqbh = "zr";
            }
            if (sqlxdm == "" || sqlxdm == null || sqlxdm == undefined || sqlxdm == "null") {
                sqlxdm = "11";
            }

            const url = "/student/personalManagement/individualApplication/microspecialtyMgt/microprofessional/registration/applyView/" + fajhh + "/" + sqbh + "/" + sqlxdm;
            window.open(url, '_blank');
        }

        // 撤销申请
        function revokeApplication(id) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm("是否确认撤销?", function(confirmed) {
                    if (confirmed) {
                        doRevokeApplication(id);
                    }
                });
            } else {
                if (confirm("是否确认撤销?")) {
                    doRevokeApplication(id);
                }
            }
        }

        // 执行撤销申请
        function doRevokeApplication(id) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/microspecialtyMgt/microprofessional/registration/deleteByID",
                type: "post",
                data: "id=" + id + "&tokenValue=" + $("#tokenValue").val(),
                dataType: "json",
                success: function(response) {
                    $("#tokenValue").val(response.token);
                    if (response.result.indexOf("/logout") != -1) {
                        window.location.href = response.result;
                    } else {
                        showSuccess("撤销成功！");
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 查询申请记录
        function queryApplications(fajhh) {
            const url = "/student/personalManagement/individualApplication/microspecialtyMgt/microprofessional/registration/queryApplyList/" + fajhh;

            if (parent && parent.addTab) {
                parent.addTab('变更记录', url);
            } else {
                window.location.href = url;
            }
        }

        // 注册检查
        function registerChecked() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/microspecialtyMgt/microprofessional/registration/registerChecked",
                type: "post",
                dataType: "json",
                success: function(response) {
                    if (response.msg == "ok") {
                        const url = "/student/personalManagement/individualApplication/microspecialtyMgt/microprofessional/registration/regNotice";

                        if (parent && parent.addTab) {
                            parent.addTab('申请注册', url);
                        } else {
                            window.location.href = url;
                        }
                    } else {
                        showError(response.msg);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 处理空值
        function dealNull(value) {
            if (value == "" || value == "null" || value == null || value == undefined) {
                return "";
            } else {
                return value;
            }
        }

        // 返回首页
        function returnIndex() {
            window.location.href = "/student/application/index";
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
