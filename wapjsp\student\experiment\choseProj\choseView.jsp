<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<!DOCTYPE html>
<html>
<head>
    <title>实验项目选择</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="/wapjsp/css/mobile-framework.css">
    <link rel="stylesheet" href="/wapjsp/css/font-awesome.min.css">
    <script src="/wapjsp/js/jquery.min.js"></script>
    <script src="/wapjsp/js/layer.js"></script>
    <script src="/wapjsp/js/urp.js"></script>
    <style>
        .course-info-card {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: 16px;
            margin-bottom: 16px;
            border-radius: 12px;
        }
        
        .course-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 14px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .detail-label {
            opacity: 0.9;
        }
        
        .detail-value {
            font-weight: 500;
        }
        
        .project-card {
            background: var(--card-background);
            border-radius: 12px;
            margin-bottom: 12px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }
        
        .project-card.selected {
            border-color: var(--primary-color);
            box-shadow: 0 4px 20px rgba(24, 144, 255, 0.2);
        }
        
        .project-header {
            background: var(--background-secondary);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .project-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            flex: 1;
            margin-right: 12px;
        }
        
        .project-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-selected {
            background: var(--success-light);
            color: var(--success-color);
        }
        
        .status-unselected {
            background: var(--background-primary);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }
        
        .project-body {
            padding: 16px;
            display: none;
        }
        
        .project-body.show {
            display: block;
        }
        
        .arrangement-item {
            background: var(--background-secondary);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            border: 1px solid var(--border-color);
        }
        
        .arrangement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .batch-number {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .arrangement-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-select {
            background: linear-gradient(135deg, var(--success-color), var(--success-light));
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .btn-selected {
            background: var(--text-secondary);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: default;
        }
        
        .arrangement-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: 6px;
            font-size: 13px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .detail-row .label {
            color: var(--text-secondary);
            min-width: 80px;
        }
        
        .detail-row .value {
            color: var(--text-primary);
            font-weight: 500;
            text-align: right;
        }
        
        .conflict-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .conflict-yes {
            background: var(--error-light);
            color: var(--error-color);
        }
        
        .conflict-no {
            background: var(--warning-light);
            color: var(--warning-color);
        }
        
        .remaining-count {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .refresh-btn {
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 16px;
            cursor: pointer;
            padding: 4px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 8px;
            height: 120px;
            margin-bottom: 12px;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .alert-mobile {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .alert-icon {
            color: var(--info-color);
            font-size: 20px;
            flex-shrink: 0;
        }
        
        .alert-text {
            color: var(--info-dark);
            font-size: 14px;
            line-height: 1.4;
        }
        
        .section-header-mobile {
            padding: 16px;
            background: var(--background-secondary);
            border-bottom: 1px solid var(--border-color);
        }
        
        .section-title-mobile {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title-mobile i {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="history.back();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实验项目选择</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-mobile">
            <c:if test="${not empty msg}">
                <div class="alert-mobile">
                    <i class="fa fa-info-circle alert-icon"></i>
                    <div class="alert-text">${msg}</div>
                </div>
            </c:if>

            <c:if test="${empty msg}">
                <!-- 课程信息卡片 -->
                <div class="course-info-card">
                    <div class="course-title">${kcm}</div>
                    <div class="course-details">
                        <div class="detail-item">
                            <span class="detail-label">学年学期</span>
                            <span class="detail-value">
                                <cache:get var="zxjxjhh" region="jh_zxjxjhb_view" key="${xnxq}" keyName="zxjxjhh" targetprop="zxjxjhm" out="true"/>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">课程号</span>
                            <span class="detail-value">${kch}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">课序号</span>
                            <span class="detail-value">${kxh}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">当前阶段</span>
                            <span class="detail-value">${xkjd.id.xkjd}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">可选项目数</span>
                            <span class="detail-value">${zxms}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">已选项目数</span>
                            <span class="detail-value">${yxxms}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">限选项目数</span>
                            <span class="detail-value">
                                <c:if test="${xkjd.sfkzxxmsl eq '是'}">${xxxms}</c:if>
                                <c:if test="${xkjd.sfkzxxmsl ne '是'}">不限制</c:if>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 页面标题 -->
                <div class="section-header-mobile">
                    <h3 class="section-title-mobile">
                        <i class="fa fa-cog"></i>
                        选择项目
                    </h3>
                </div>

                <!-- 项目列表 -->
                <div class="section-mobile">
                    <div id="projectListContainer">
                        <!-- 加载中状态 -->
                        <div id="loadingState" class="loading-container">
                            <div class="loading-skeleton"></div>
                            <div class="loading-skeleton"></div>
                            <div class="loading-skeleton"></div>
                        </div>
                        
                        <!-- 项目列表 -->
                        <div id="projectList"></div>
                        
                        <!-- 空状态 -->
                        <div id="emptyState" class="empty-state" style="display: none;">
                            <div class="empty-icon">
                                <i class="fa fa-flask"></i>
                            </div>
                            <p>暂无实验项目</p>
                            <small>请联系老师确认实验安排</small>
                        </div>
                    </div>
                </div>
            </c:if>
        </div>
    </div>

    <!-- 隐藏输入 -->
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}" />
    <input type="hidden" id="yxxms" value="${yxxms}" />
    <input type="hidden" id="xxxms" value="${xxxmsl}" />
    <input type="hidden" id="syrwId" value="${syrwId}" />

    <script>
        // 全局变量
        let projectData = [];
        let isLoading = false;
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            initializePage();
            queryPageList();
        });
        
        // 初始化页面
        function initializePage() {
            // 添加触摸事件处理
            $(document).on('click', '.project-card', function() {
                toggleProjectDetails(this);
            });
        }
        
        // 切换项目详情显示
        function toggleProjectDetails(card) {
            const $card = $(card);
            const $body = $card.find('.project-body');
            const xmrwId = $card.data('xmrwid');
            const chosedCnt = $card.data('chosed-cnt');
            
            if ($body.hasClass('show')) {
                $body.removeClass('show');
                $card.removeClass('selected');
            } else {
                // 关闭其他项目详情
                $('.project-body').removeClass('show');
                $('.project-card').removeClass('selected');
                
                // 显示当前项目详情
                $body.addClass('show');
                $card.addClass('selected');
                
                // 加载项目安排
                queryProjectArranges(xmrwId, chosedCnt);
            }
        }
        
        // 查询项目列表
        function queryPageList() {
            if (isLoading) return;
            
            isLoading = true;
            showLoading();
            
            $.ajax({
                url: "/student/experiment/choseProj/queryAllProjects",
                cache: false,
                type: "get",
                data: "syrwId=" + $("#syrwId").val(),
                dataType: "json",
                success: function (d) {
                    isLoading = false;
                    hideLoading();
                    
                    projectData = d.data.result;
                    renderProjectList(d.data.result);
                },
                error: function (xhr) {
                    isLoading = false;
                    hideLoading();
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                }
            });
        }
        
        // 渲染项目列表
        function renderProjectList(data) {
            if (!data || data.length === 0) {
                showEmptyState();
                return;
            }
            
            let html = '';
            data.forEach(function(project, index) {
                const isSelected = parseInt(project.chosedCnt) > 0;
                
                html += `
                    <div class="project-card" data-xmrwid="${project.xmrwid}" data-chosed-cnt="${project.chosedCnt}">
                        <div class="project-header">
                            <div class="project-name">${project.syxmmc || ''}</div>
                            <span class="project-status ${isSelected ? 'status-selected' : 'status-unselected'}">
                                ${isSelected ? '已选' : '未选'}
                            </span>
                        </div>
                        <div class="project-body">
                            <div id="arrangements_${project.xmrwid}">
                                <!-- 动态加载项目安排 -->
                            </div>
                        </div>
                    </div>
                `;
            });
            
            $('#projectList').html(html);
            $('#projectList').show();
            $('#emptyState').hide();
        }
        
        // 查询项目安排
        function queryProjectArranges(xmrwId, chosedCnt) {
            $.ajax({
                url: "/student/experiment/choseProj/queryProjArranges",
                cache: false,
                type: "get",
                data: "xmrwId=" + xmrwId,
                dataType: "json",
                success: function (d) {
                    renderProjectArrangements(d.data.result, chosedCnt, xmrwId);
                },
                error: function (xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                }
            });
        }
        
        // 渲染项目安排
        function renderProjectArrangements(data, chosedCnt, xmrwId) {
            if (!data || data.length === 0) {
                $(`#arrangements_${xmrwId}`).html('<p class="text-center">暂无安排信息</p>');
                return;
            }
            
            // 按分组号分组
            const groups = {};
            data.forEach(function(item) {
                if (!groups[item.fzh]) {
                    groups[item.fzh] = [];
                }
                groups[item.fzh].push(item);
            });
            
            let html = '';
            Object.keys(groups).forEach(function(fzh) {
                const arrangements = groups[fzh];
                const firstItem = arrangements[0];
                const canSelect = chosedCnt == 0 && firstItem.e_confliced == 0 && firstItem.t_confliced == 0;
                const isSelected = chosedCnt > 0 && parseInt(firstItem.sfyx) >= 1;
                
                html += `
                    <div class="arrangement-item">
                        <div class="arrangement-header">
                            <div class="batch-number">批次 ${fzh}</div>
                            <div class="arrangement-actions">
                                ${canSelect ? `
                                    <button class="btn-select" onclick="choseProject('${firstItem.xmfzid}')">
                                        <i class="fa fa-check"></i> 选择
                                    </button>
                                ` : ''}
                                ${isSelected ? `
                                    <span class="btn-selected">已选择</span>
                                ` : ''}
                            </div>
                        </div>
                        <div class="arrangement-details">
                `;
                
                arrangements.forEach(function(item) {
                    html += `
                        <div class="detail-row">
                            <span class="label">实验冲突</span>
                            <span class="value">
                                <span class="conflict-badge ${item.e_confliced > 0 ? 'conflict-yes' : 'conflict-no'}">
                                    ${item.e_confliced > 0 ? '是' : '否'}
                                </span>
                            </span>
                        </div>
                        <div class="detail-row">
                            <span class="label">理论冲突</span>
                            <span class="value">
                                <span class="conflict-badge ${item.t_confliced > 0 ? 'conflict-yes' : 'conflict-no'}">
                                    ${item.t_confliced > 0 ? '是' : '否'}
                                </span>
                            </span>
                        </div>
                        <div class="detail-row">
                            <span class="label">实验时间</span>
                            <span class="value">${dealTime(item.kssj, item.jssj, item.syzc)}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">实验地点</span>
                            <span class="value">${item.sydd || ''}</span>
                        </div>
                    `;
                });
                
                if (chosedCnt == 0) {
                    html += `
                        <div class="detail-row">
                            <span class="label">项目余量</span>
                            <span class="value">
                                <div class="remaining-count">
                                    <span id="xmyl_${firstItem.xmfzid}">${firstItem.xmyl || ''}</span>
                                    <button class="refresh-btn" onclick="refreshXmyl('${firstItem.xmfzid}')" title="刷新项目余量">
                                        <i class="fa fa-refresh"></i>
                                    </button>
                                </div>
                            </span>
                        </div>
                    `;
                }
                
                html += `
                        </div>
                    </div>
                `;
            });
            
            $(`#arrangements_${xmrwId}`).html(html);
        }
        
        // 处理时间显示
        function dealTime(kssj, jssj, syzc) {
            if (!kssj || !jssj || !syzc) return '';
            
            const startTime = kssj.substring(0, 2) + ":" + kssj.substring(2, 4);
            const endTime = jssj.substring(0, 2) + ":" + jssj.substring(2, 4);
            
            return `${syzc} 【${startTime}~${endTime}】`;
        }
        
        // 选择项目
        function choseProject(xmfzId) {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            const yxxmsTep = $("#yxxms").val();
            const xxxms = $("#xxxms").val();
            
            if (parseInt(yxxmsTep) >= parseInt(xxxms)) {
                showError("您所选项目数已经超出了要求的" + xxxms + "个，请确认！");
                return;
            }
            
            layer.confirm('确定选择当前项目安排吗？', {
                icon: 3,
                title: '确认选择',
                skin: 'layer-mobile'
            }, function(index) {
                layer.close(index);
                
                const loadingIndex = layer.load(0, {
                    shade: [0.2, "#000"]
                });
                
                $.ajax({
                    url: "/student/experiment/choseProj/saveArranges",
                    cache: false,
                    type: "post",
                    data: "xmfzId=" + xmfzId + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(d) {
                        layer.close(loadingIndex);
                        
                        const data = d.data;
                        $("#tokenValue").val(data["token"]);
                        
                        if (data["result"].indexOf("/") != -1) {
                            window.location.href = data.result;
                        } else {
                            if (data["msg"] == "ok") {
                                showSuccess("保存成功！");
                                setTimeout(function() {
                                    location.reload();
                                }, 1500);
                            } else {
                                handleSaveError(data["msg"]);
                            }
                        }
                    },
                    error: function(xhr) {
                        layer.close(loadingIndex);
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    }
                });
            });
        }
        
        // 处理保存错误
        function handleSaveError(msg) {
            const errorMessages = {
                "lack": "项目余量不足！",
                "no_kzcs": "选课控制参数不存在！",
                "zkg_close": "选课总开关关闭！",
                "no_xnxq": "实验学年学期参数不存在！",
                "no_run_xkjd": "没有正在运行的选课阶段！",
                "out_time": "不在当前阶段的时间范围内！",
                "nj_no_exists": "您不在限制的年级范围内！",
                "yx_no_exists": "您不在限制院系/专业/专业方向范围内！",
                "proj_num_exceed": "当前课程限选项目为${xxxms}个，您已经选完，请确认！"
            };
            
            const errorMsg = errorMessages[msg] || "操作失败，请重试！";
            showError(errorMsg);
        }
        
        // 刷新项目余量
        function refreshXmyl(xmfzid) {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            const loadingIndex = layer.load(0, {
                shade: [0.2, "#000"]
            });
            
            $.ajax({
                url: "/student/experiment/choseProj/refreshXmyl",
                cache: false,
                data: {
                    "xmfzid": xmfzid
                },
                type: "get",
                dataType: "json",
                success: function (d) {
                    layer.close(loadingIndex);
                    
                    showSuccess("刷新成功");
                    const xmfzb = d.data.result;
                    const xmyl = xmfzb.xmyl;
                    $("#xmyl_" + xmfzid).text(xmyl);
                },
                error: function (xhr) {
                    layer.close(loadingIndex);
                    showError("刷新失败，请重试！");
                }
            });
        }
        
        // 刷新数据
        function refreshData() {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            queryPageList();
        }
        
        // 显示加载状态
        function showLoading() {
            $('#loadingState').show();
            $('#projectList').hide();
            $('#emptyState').hide();
        }
        
        // 隐藏加载状态
        function hideLoading() {
            $('#loadingState').hide();
        }
        
        // 显示空状态
        function showEmptyState() {
            $('#loadingState').hide();
            $('#projectList').hide();
            $('#emptyState').show();
        }
        
        // 显示错误信息
        function showError(message) {
            layer.alert(message, {
                icon: 2,
                title: '错误提示',
                skin: 'layer-mobile'
            });
        }
        
        // 显示成功信息
        function showSuccess(message) {
            layer.msg(message, {
                icon: 1,
                skin: 'layer-mobile'
            });
        }
        
        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                setTimeout(function() {
                    refreshData();
                }, 500);
            }
        });
    </script>
</body>
</html>
