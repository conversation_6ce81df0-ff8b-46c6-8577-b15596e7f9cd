<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html>
<html>
<head>
    <title>学生评估问卷列表</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="/wapjsp/css/mobile-framework.css">
    <link rel="stylesheet" href="/wapjsp/css/font-awesome.min.css">
    <script src="/wapjsp/js/jquery.min.js"></script>
    <script src="/wapjsp/js/layer.js"></script>
    <script src="/wapjsp/js/urp.js"></script>
    <style>
        .evaluation-stats {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: 20px;
            margin-bottom: 16px;
            border-radius: 12px;
            text-align: center;
        }
        
        .stats-title {
            font-size: 16px;
            margin-bottom: 16px;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 600;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .evaluation-card {
            background: var(--card-background);
            border-radius: 12px;
            margin-bottom: 12px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }
        
        .evaluation-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--card-shadow-hover);
        }
        
        .evaluation-card.completed {
            border-left: 4px solid var(--success-color);
        }
        
        .evaluation-card.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .evaluation-header {
            background: var(--background-secondary);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .questionnaire-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            flex: 1;
            margin-right: 12px;
        }
        
        .evaluation-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-completed {
            background: var(--success-light);
            color: var(--success-color);
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-color);
        }
        
        .evaluation-body {
            padding: 16px;
        }
        
        .evaluation-info {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            padding: 4px 0;
        }
        
        .info-label {
            color: var(--text-secondary);
            min-width: 80px;
        }
        
        .info-value {
            color: var(--text-primary);
            font-weight: 500;
            text-align: right;
            flex: 1;
        }
        
        .evaluation-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }
        
        .btn-evaluate {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }
        
        .alert-mobile {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .alert-icon {
            color: var(--warning-color);
            font-size: 20px;
            flex-shrink: 0;
        }
        
        .alert-text {
            color: var(--warning-dark);
            font-size: 14px;
            line-height: 1.4;
        }
        
        .alert-disabled {
            background: var(--error-light);
            border-color: var(--error-color);
        }
        
        .alert-disabled .alert-icon {
            color: var(--error-color);
        }
        
        .alert-disabled .alert-text {
            color: var(--error-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        
        .empty-desc {
            font-size: 14px;
            line-height: 1.5;
        }
        
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 8px;
            height: 120px;
            margin-bottom: 12px;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .section-header-mobile {
            padding: 16px;
            background: var(--background-secondary);
            border-bottom: 1px solid var(--border-color);
        }
        
        .section-title-mobile {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title-mobile i {
            color: var(--primary-color);
        }
        
        .evaluation-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .modal-content {
            background: var(--card-background);
            border-radius: 12px;
            max-width: 100%;
            max-height: 80vh;
            overflow-y: auto;
            width: 100%;
        }
        
        .modal-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-body {
            padding: 16px;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">教学评估</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-mobile">
            <!-- 页面标题 -->
            <div class="section-header-mobile">
                <h3 class="section-title-mobile">
                    <i class="fa fa-pencil-square-o"></i>
                    教学评估
                </h3>
            </div>

            <c:if test="${pgOff == '0'}">
                <div class="alert-mobile alert-disabled">
                    <i class="fa fa-exclamation-circle alert-icon"></i>
                    <div class="alert-text">评估开关已关闭！</div>
                </div>
            </c:if>

            <c:if test="${pgOff == '1'}">
                <!-- 评估统计 -->
                <div class="evaluation-stats">
                    <div class="stats-title">当前评估学期：${nowSemester.executiveEducationPlanName}</div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="totalCourses">0</div>
                            <div class="stat-label">总课程</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="pendingCourses">0</div>
                            <div class="stat-label">待评估</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="completedCourses">0</div>
                            <div class="stat-label">已完成</div>
                        </div>
                    </div>
                </div>

                <!-- 评估列表 -->
                <div class="section-mobile">
                    <div id="evaluationListContainer">
                        <!-- 加载中状态 -->
                        <div id="loadingState" class="loading-container">
                            <div class="loading-skeleton"></div>
                            <div class="loading-skeleton"></div>
                            <div class="loading-skeleton"></div>
                        </div>
                        
                        <!-- 评估列表 -->
                        <div id="evaluationList"></div>
                        
                        <!-- 空状态 -->
                        <div id="emptyState" class="empty-state" style="display: none;">
                            <div class="empty-icon">
                                <i class="fa fa-clipboard"></i>
                            </div>
                            <div class="empty-title">暂无评估任务</div>
                            <div class="empty-desc">
                                当前学期没有需要评估的课程<br>
                                请关注后续评估安排
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>
        </div>

        <!-- 评估详情模态框 -->
        <div class="evaluation-detail-modal" id="detailModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="modalTitle">评估详情</h3>
                    <button class="modal-close" onclick="closeDetailModal()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- 动态内容 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 隐藏表单 -->
    <form method="POST" name="WjList" action="/student/teachingEvaluation/teachingEvaluation/evaluationPage" style="display: none;">
        <input type="hidden" name="evaluatedPeople">
        <input type="hidden" name="evaluatedPeopleNumber">
        <input type="hidden" name="questionnaireCode">
        <input type="hidden" name="questionnaireName">
        <input type="hidden" name="evaluationContentNumber">
        <input type="hidden" name="evaluationContentContent">
    </form>

    <form method="POST" name="zgnyEvaluationCover" action="/student/teachingEvaluation/teachingEvaluation/zgnyEvaluationCover" style="display: none;">
        <input type="hidden" name="evaluatedPeople" id="evaluatedPeople">
        <input type="hidden" name="evaluatedPeopleNumber" id="evaluatedPeopleNumber">
        <input type="hidden" name="questionnaireCode" id="questionnaireCode">
        <input type="hidden" name="questionnaireName" id="questionnaireName">
        <input type="hidden" name="evaluationContentNumber" id="evaluationContentNumber">
    </form>

    <script>
        // 全局变量
        let evaluationData = [];
        let isLoading = false;
        let schoolId = "${schoolId}";
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            initializePage();
            if (${pgOff != '0'}) {
                loadEvaluations();
            }
        });
        
        // 初始化页面
        function initializePage() {
            // 添加触摸事件处理
            $('.evaluation-card').on('touchstart', function() {
                $(this).css('transform', 'scale(0.98)');
            }).on('touchend', function() {
                $(this).css('transform', '');
            });
        }
        
        // 加载评估列表
        function loadEvaluations() {
            if (isLoading) return;
            
            isLoading = true;
            showLoading();
            
            $.ajax({
                url: "/student/teachingEvaluation/teachingEvaluation/search",
                cache: false,
                type: "post",
                dataType: "json",
                success: function (d) {
                    isLoading = false;
                    hideLoading();
                    
                    evaluationData = d.data;
                    updateStatistics(d);
                    renderEvaluations(d.data);
                },
                error: function (xhr) {
                    isLoading = false;
                    hideLoading();
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                }
            });
        }
        
        // 更新统计信息
        function updateStatistics(data) {
            const totalCourses = data.evaluationNum || 0;
            const pendingCourses = data.notFinishedNum || 0;
            const completedCourses = totalCourses - pendingCourses;
            
            $('#totalCourses').text(totalCourses);
            $('#pendingCourses').text(pendingCourses);
            $('#completedCourses').text(completedCourses);
        }
        
        // 渲染评估列表
        function renderEvaluations(data) {
            if (!data || data.length === 0) {
                showEmptyState();
                return;
            }
            
            let html = '';
            data.forEach(function(evaluation, index) {
                const isCompleted = evaluation.isEvaluated === '是';
                
                html += `
                    <div class="evaluation-card ${isCompleted ? 'completed' : 'pending'}">
                        <div class="evaluation-header">
                            <div class="questionnaire-name">${evaluation.questionnaire.questionnaireName}</div>
                            <span class="evaluation-status ${isCompleted ? 'status-completed' : 'status-pending'}">
                                ${isCompleted ? '已评估' : '待评估'}
                            </span>
                        </div>
                        
                        <div class="evaluation-body">
                            <div class="evaluation-info">
                                <div class="info-item">
                                    <span class="info-label">被评人</span>
                                    <span class="info-value">${evaluation.evaluatedPeople}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">评估内容</span>
                                    <span class="info-value">${evaluation.evaluationContent}</span>
                                </div>
                            </div>
                            
                            <div class="evaluation-actions">
                                ${getActionButtonsHtml(evaluation)}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            $('#evaluationList').html(html);
            $('#evaluationList').show();
            $('#emptyState').hide();
        }
        
        // 获取操作按钮HTML
        function getActionButtonsHtml(evaluation) {
            if (evaluation.isEvaluated === '是') {
                return `
                    <button class="btn-view" onclick="viewEvaluationResult('${evaluation.id.questionnaireCoding}', '${evaluation.questionnaire.questionnaireName}', '${evaluation.id.evaluatedPeople}', '${evaluation.evaluatedPeople}', '${evaluation.id.evaluationContentNumber}')">
                        <i class="fa fa-eye"></i>
                        查看结果
                    </button>
                `;
            } else {
                return `
                    <button class="btn-evaluate" onclick="startEvaluation('${evaluation.id.questionnaireCoding}', '${evaluation.questionnaire.questionnaireName}', '${evaluation.id.evaluatedPeople}', '${evaluation.evaluatedPeople}', '${evaluation.id.evaluationContentNumber}')">
                        <i class="fa fa-edit"></i>
                        开始评估
                    </button>
                `;
            }
        }
        
        // 开始评估
        function startEvaluation(questionnaireCode, questionnaireName, evaluatedPeopleNumber, evaluatedPeople, evaluationContentNumber) {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            const form = document.WjList;
            form.evaluatedPeople.value = evaluatedPeople;
            form.evaluatedPeopleNumber.value = evaluatedPeopleNumber;
            form.questionnaireCode.value = questionnaireCode;
            form.questionnaireName.value = questionnaireName;
            form.evaluationContentNumber.value = evaluationContentNumber;
            form.submit();
        }
        
        // 查看评估结果
        function viewEvaluationResult(questionnaireCode, questionnaireName, evaluatedPeopleNumber, evaluatedPeople, evaluationContentNumber) {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            if (schoolId === '100010') {
                $("#evaluatedPeople").val(evaluatedPeople);
                $("#evaluatedPeopleNumber").val(evaluatedPeopleNumber);
                $("#questionnaireCode").val(questionnaireCode);
                $("#questionnaireName").val(questionnaireName);
                $("#evaluationContentNumber").val(evaluationContentNumber);
                document.zgnyEvaluationCover.submit();
            } else {
                showLoading();
                
                $.ajax({
                    url: "/student/teachingEvaluation/teachingEvaluation/evaluationReusltPage",
                    cache: false,
                    type: "post",
                    data: "evaluatedPeople=" + evaluatedPeople +
                          "&evaluatedPeopleNumber=" + evaluatedPeopleNumber +
                          "&questionnaireCode=" + questionnaireCode +
                          "&questionnaireName=" + questionnaireName +
                          "&evaluationContentNumber=" + evaluationContentNumber,
                    dataType: "json",
                    success: function (d) {
                        hideLoading();
                        showEvaluationDetail(d);
                    },
                    error: function (xhr) {
                        hideLoading();
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    }
                });
            }
        }
        
        // 显示评估详情
        function showEvaluationDetail(data) {
            let html = `
                <div class="evaluation-info">
                    <div class="info-item">
                        <span class="info-label">被评人</span>
                        <span class="info-value">${data.evaluatedPeople}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">评估内容</span>
                        <span class="info-value">${data.evaluationContentContent}</span>
                    </div>
                </div>
                <div style="margin-top: 16px;">
            `;
            
            if (data.data && data.data.length > 0) {
                data.data.forEach(function(pageData) {
                    html += `<h4 style="color: var(--primary-color); margin: 16px 0 8px 0;">${pageData.questionsCourseName}</h4>`;
                    
                    pageData.questionsList.forEach(function(question, index) {
                        html += `
                            <div style="background: var(--background-secondary); padding: 12px; margin-bottom: 8px; border-radius: 8px;">
                                <div style="font-weight: 500; margin-bottom: 8px;">${index + 1}、${question.questionsTopic.questionsName}</div>
                        `;
                        
                        if (question.answerList && question.answerList.length > 0) {
                            const stxh = "ABCDEFGH";
                            question.answerList.forEach(function(answer, answerIndex) {
                                data.selectedAnswerList.forEach(function(selectedAnswer) {
                                    if (question.id.questionsCode === selectedAnswer.id.questionsCode) {
                                        let isSelected = false;
                                        
                                        if (question.questionsCourseType.integrationType === "01") {
                                            isSelected = answer.id.answerCode === selectedAnswer.answer;
                                        } else if (question.questionsCourseType.integrationType !== "04") {
                                            const daValue = question.questionsUnit + "_" + answer.scoresScale;
                                            isSelected = daValue === selectedAnswer.answer;
                                        }
                                        
                                        html += `
                                            <div style="margin: 4px 0; ${isSelected ? 'color: var(--primary-color); font-weight: 500;' : ''}">
                                                ${stxh.substring(answerIndex, answerIndex + 1)} ${answer.answerName}
                                            </div>
                                        `;
                                    }
                                });
                            });
                        } else {
                            data.selectedAnswerList.forEach(function(selectedAnswer) {
                                if (question.id.questionsCode === selectedAnswer.id.questionsCode) {
                                    html += `<div style="margin: 4px 0; color: var(--primary-color); font-weight: 500;">${selectedAnswer.answer}</div>`;
                                }
                            });
                        }
                        
                        html += '</div>';
                    });
                });
            }
            
            if (data.subjectiveResult) {
                html += `
                    <h4 style="color: var(--primary-color); margin: 16px 0 8px 0;">主观评价</h4>
                    <div style="background: var(--background-secondary); padding: 12px; border-radius: 8px; line-height: 1.5;">
                        ${data.subjectiveResult}
                    </div>
                `;
            }
            
            html += '</div>';
            
            $('#modalTitle').text(data.questionnaireName);
            $('#modalBody').html(html);
            $('#detailModal').css('display', 'flex');
        }
        
        // 关闭详情模态框
        function closeDetailModal() {
            $('#detailModal').hide();
        }
        
        // 刷新数据
        function refreshData() {
            // 添加触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            
            if (${pgOff != '0'}) {
                loadEvaluations();
            }
        }
        
        // 显示加载状态
        function showLoading() {
            $('#loadingState').show();
            $('#evaluationList').hide();
            $('#emptyState').hide();
        }
        
        // 隐藏加载状态
        function hideLoading() {
            $('#loadingState').hide();
        }
        
        // 显示空状态
        function showEmptyState() {
            $('#loadingState').hide();
            $('#evaluationList').hide();
            $('#emptyState').show();
        }
        
        // 显示错误信息
        function showError(message) {
            layer.alert(message, {
                icon: 2,
                title: '错误提示',
                skin: 'layer-mobile'
            });
        }
        
        // 点击模态框背景关闭
        $(document).on('click', '.evaluation-detail-modal', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });
        
        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                setTimeout(function() {
                    if (${pgOff != '0'}) {
                        refreshData();
                    }
                }, 500);
            }
        });
    </script>
</body>
</html>
